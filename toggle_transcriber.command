#!/bin/bash

PORT=5050
APP_DIR="$HOME/transcriber-web"
VENV_DIR="$HOME/whisper-env"

# Get list of PIDs running on the port
RUNNING_PIDS=$(lsof -ti :$PORT)

if [ -n "$RUNNING_PIDS" ]; then
  echo "🛑 Flask app is running (PID(s): $RUNNING_PIDS). Stopping it..."
  # Kill each PID separately
  for pid in $RUNNING_PIDS; do
    kill -9 "$pid"
  done
  echo "✅ Stopped."
else
  echo "🚀 Starting Flask app..."
  cd "$APP_DIR"
  source "$VENV_DIR/bin/activate"
  nohup python3 app.py > flask.log 2>&1 &
  echo "✅ Flask app started in background."
  
  sleep 2
  open http://localhost:5050
fi
