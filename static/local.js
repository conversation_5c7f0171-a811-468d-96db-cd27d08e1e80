document.addEventListener("DOMContentLoaded", function () {
  const form = document.getElementById("uploadForm");
  const fileInput = document.getElementById("fileInput");
  const uploadBtn = document.getElementById("uploadBtn");
  const selectedFileDiv = document.getElementById("selectedFile");
  const queueBody = document.getElementById("queueBody");
  const searchInput = document.getElementById("searchInput");

  let jobsData = {};
  let currentPage = 1;
  const itemsPerPage = 10;

  // Handle file selection
  fileInput.addEventListener("change", function() {
    if (this.files && this.files[0]) {
      const file = this.files[0];
      selectedFileDiv.textContent = `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
      selectedFileDiv.style.display = "block";
      uploadBtn.disabled = false;
    } else {
      selectedFileDiv.style.display = "none";
      uploadBtn.disabled = true;
    }
  });

  // Handle form submission
  form.addEventListener("submit", async function (e) {
    e.preventDefault();
    
    const file = fileInput.files[0];
    if (!file) return;

    // Disable upload button and show progress
    uploadBtn.disabled = true;
    uploadBtn.textContent = "Uploading...";

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch("/upload", {
        method: "POST",
        body: formData
      });
      
      const data = await response.json();
      
      if (response.ok) {
        // Reset form
        fileInput.value = "";
        selectedFileDiv.style.display = "none";
        uploadBtn.textContent = "Upload & Transcribe";
        
        // Show success message
        alert(`File uploaded successfully! Job ID: ${data.job_id}`);
        
        // Refresh the queue
        currentPage = 1;
        await fetchAndRender();
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (err) {
      console.error("Failed to upload file:", err);
      alert("Failed to upload file. Please try again.");
    } finally {
      uploadBtn.disabled = false;
      uploadBtn.textContent = "Upload & Transcribe";
    }
  });

  searchInput.addEventListener("input", () => {
    currentPage = 1;
    renderTable();
  });

  async function fetchAndRender() {
    try {
      const res = await fetch("/progress");
      jobsData = await res.json();
      renderTable();
    } catch (err) {
      console.error("Error fetching job progress:", err);
    }
  }

  function renderTable() {
    const searchTerm = searchInput.value.toLowerCase();
    
    // Filter to show only local file jobs and apply search
    const jobsArray = Object.entries(jobsData)
      .filter(([id, job]) => job.type === 'local')
      .filter(([id, job]) => {
        return (
          id.toLowerCase().includes(searchTerm) ||
          (job.youtube_title && job.youtube_title.toLowerCase().includes(searchTerm)) ||
          (job.saved_filename && job.saved_filename.toLowerCase().includes(searchTerm)) ||
          (job.url && job.url.toLowerCase().includes(searchTerm)) ||
          (job.status && job.status.toLowerCase().includes(searchTerm))
        );
      });

    const totalItems = jobsArray.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    if (currentPage > totalPages) currentPage = totalPages || 1;

    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const pageItems = jobsArray.slice(start, end);

    queueBody.innerHTML = "";
    for (const [id, job] of pageItems) {
      const tr = document.createElement("tr");
      const statusClass = "status-" + (job.status.split(" ")[0] || "Queued");
      tr.innerHTML = `
        <td>${id}</td>
        <td>${job.youtube_title || ""}</td>
        <td>${job.saved_filename || ""}</td>
        <td>${job.url || ""}</td>
        <td class="${statusClass}" title="${job.log || ''}">${job.status}</td>
      `;
      queueBody.appendChild(tr);
    }

    renderPagination(totalPages);
  }

  function renderPagination(totalPages) {
    let paginationDiv = document.getElementById("pagination");
    if (!paginationDiv) {
      paginationDiv = document.createElement("div");
      paginationDiv.id = "pagination";
      paginationDiv.style.marginTop = "12px";
      document.body.appendChild(paginationDiv);
    }
    paginationDiv.innerHTML = "";

    if (totalPages <= 1) return;

    for (let i = 1; i <= totalPages; i++) {
      const btn = document.createElement("button");
      btn.textContent = i;
      btn.style.margin = "0 4px";
      btn.disabled = i === currentPage;
      btn.addEventListener("click", () => {
        currentPage = i;
        renderTable();
      });
      paginationDiv.appendChild(btn);
    }
  }

  // Poll every 3 seconds
  setInterval(fetchAndRender, 3000);
  fetchAndRender();
});
