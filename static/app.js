document.addEventListener("DOMContentLoaded", function () {
  const form = document.getElementById("urlForm");
  const urlInput = document.getElementById("urlInput");
  const filenameInput = document.getElementById("filenameInput");
  const queueBody = document.getElementById("queueBody");
  const queueBodyMobile = document.getElementById("queueBodyMobile");
  const searchInput = document.getElementById("searchInput");

  let jobsData = {};
  let currentPage = 1;
  const itemsPerPage = 10;

  form.addEventListener("submit", async function (e) {
    e.preventDefault();
    const url = urlInput.value.trim();
    const filename = filenameInput.value.trim();

    if (!url) return;

    try {
      const response = await fetch("/submit", {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: `url=${encodeURIComponent(url)}&filename=${encodeURIComponent(filename)}`,
      });
      const data = await response.json();
      urlInput.value = "";
      filenameInput.value = "";
      currentPage = 1;
      await fetchAndRender();
    } catch (err) {
      console.error("Failed to add job:", err);
    }
  });

  searchInput.addEventListener("input", () => {
    currentPage = 1;
    renderTable();
  });

  async function fetchAndRender() {
    try {
      const res = await fetch("/progress");
      jobsData = await res.json();
      renderTable();
    } catch (err) {
      console.error("Error fetching job progress:", err);
    }
  }

  function renderTable() {
    const searchTerm = searchInput.value.toLowerCase();
    const jobsArray = Object.entries(jobsData)
      .filter(([id, job]) => job.type !== 'local') // Only show YouTube jobs on this page
      .filter(([id, job]) => {
        return (
          id.toLowerCase().includes(searchTerm) ||
          (job.youtube_title && job.youtube_title.toLowerCase().includes(searchTerm)) ||
          (job.saved_filename && job.saved_filename.toLowerCase().includes(searchTerm)) ||
          (job.url && job.url.toLowerCase().includes(searchTerm)) ||
          (job.status && job.status.toLowerCase().includes(searchTerm))
        );
      });

    const totalItems = jobsArray.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    if (currentPage > totalPages) currentPage = totalPages || 1;

    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const pageItems = jobsArray.slice(start, end);

    // Desktop table
    queueBody.innerHTML = "";
    for (const [id, job] of pageItems) {
      const tr = document.createElement("tr");
      const statusClass = "status-" + (job.status.split(" ")[0] || "Queued");
      tr.className = "hover:bg-gray-50 transition-colors";
      tr.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${id.substring(0, 8)}...</td>
        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">${job.youtube_title || ""}</td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${job.saved_filename || ""}</td>
        <td class="px-6 py-4 text-sm text-blue-600 hover:text-blue-800 max-w-xs truncate">
          <a href="${job.url}" target="_blank" class="hover:underline">${job.url}</a>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(job.status)}" title="${job.log || ''}">
            ${job.status}
          </span>
        </td>
      `;
      queueBody.appendChild(tr);
    }

    // Mobile cards
    queueBodyMobile.innerHTML = "";
    for (const [id, job] of pageItems) {
      const card = document.createElement("div");
      card.className = "p-4 hover:bg-gray-50 transition-colors";
      card.innerHTML = `
        <div class="space-y-3">
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">${job.youtube_title || "Untitled"}</p>
              <p class="text-xs text-gray-500">ID: ${id.substring(0, 8)}...</p>
            </div>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(job.status)}" title="${job.log || ''}">
              ${job.status}
            </span>
          </div>
          <div class="text-sm text-gray-600">
            <p><span class="font-medium">Filename:</span> ${job.saved_filename || "N/A"}</p>
            <p class="truncate"><span class="font-medium">URL:</span>
              <a href="${job.url}" target="_blank" class="text-blue-600 hover:text-blue-800 hover:underline">${job.url}</a>
            </p>
          </div>
        </div>
      `;
      queueBodyMobile.appendChild(card);
    }

    renderPagination(totalPages);
  }

  function getStatusBadgeClass(status) {
    const statusType = status.split(" ")[0] || "Queued";
    const classes = {
      "Queued": "bg-amber-100 text-amber-800",
      "Downloading": "bg-blue-100 text-blue-800",
      "Transcribing": "bg-purple-100 text-purple-800",
      "Done": "bg-green-100 text-green-800",
      "Error": "bg-red-100 text-red-800",
      "Copying": "bg-indigo-100 text-indigo-800"
    };
    return classes[statusType] || "bg-gray-100 text-gray-800";
  }

  function renderPagination(totalPages) {
    let paginationDiv = document.getElementById("pagination");
    if (!paginationDiv) {
      paginationDiv = document.createElement("div");
      paginationDiv.id = "pagination";
      document.body.appendChild(paginationDiv);
    }
    paginationDiv.innerHTML = "";

    if (totalPages <= 1) return;

    paginationDiv.className = "flex justify-center space-x-1";

    // Previous button
    if (currentPage > 1) {
      const prevBtn = document.createElement("button");
      prevBtn.innerHTML = `
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      `;
      prevBtn.className = "px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 transition-colors";
      prevBtn.addEventListener("click", () => {
        currentPage--;
        renderTable();
      });
      paginationDiv.appendChild(prevBtn);
    }

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
      const btn = document.createElement("button");
      btn.textContent = i;
      btn.className = i === currentPage
        ? "px-3 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600"
        : "px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 transition-colors";
      btn.addEventListener("click", () => {
        currentPage = i;
        renderTable();
      });
      paginationDiv.appendChild(btn);
    }

    // Next button
    if (currentPage < totalPages) {
      const nextBtn = document.createElement("button");
      nextBtn.innerHTML = `
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      `;
      nextBtn.className = "px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 transition-colors";
      nextBtn.addEventListener("click", () => {
        currentPage++;
        renderTable();
      });
      paginationDiv.appendChild(nextBtn);
    }
  }

  // Poll every 3 seconds
  setInterval(fetchAndRender, 3000);
  fetchAndRender();
});
