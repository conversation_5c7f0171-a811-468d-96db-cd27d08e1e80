document.addEventListener("DOMContentLoaded", function () {
  const form = document.getElementById("urlForm");
  const urlInput = document.getElementById("urlInput");
  const filenameInput = document.getElementById("filenameInput");
  const queueBody = document.getElementById("queueBody");
  const searchInput = document.getElementById("searchInput");

  let jobsData = {};
  let currentPage = 1;
  const itemsPerPage = 10;

  form.addEventListener("submit", async function (e) {
    e.preventDefault();
    const url = urlInput.value.trim();
    const filename = filenameInput.value.trim();

    if (!url) return;

    try {
      const response = await fetch("/submit", {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: `url=${encodeURIComponent(url)}&filename=${encodeURIComponent(filename)}`,
      });
      const data = await response.json();
      urlInput.value = "";
      filenameInput.value = "";
      currentPage = 1;
      await fetchAndRender();
    } catch (err) {
      console.error("Failed to add job:", err);
    }
  });

  searchInput.addEventListener("input", () => {
    currentPage = 1;
    renderTable();
  });

  async function fetchAndRender() {
    try {
      const res = await fetch("/progress");
      jobsData = await res.json();
      renderTable();
    } catch (err) {
      console.error("Error fetching job progress:", err);
    }
  }

  function renderTable() {
    const searchTerm = searchInput.value.toLowerCase();
    const jobsArray = Object.entries(jobsData).filter(([id, job]) => {
      return (
        id.toLowerCase().includes(searchTerm) ||
        (job.youtube_title && job.youtube_title.toLowerCase().includes(searchTerm)) ||
        (job.saved_filename && job.saved_filename.toLowerCase().includes(searchTerm)) ||
        (job.url && job.url.toLowerCase().includes(searchTerm)) ||
        (job.status && job.status.toLowerCase().includes(searchTerm))
      );
    });

    const totalItems = jobsArray.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    if (currentPage > totalPages) currentPage = totalPages || 1;

    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const pageItems = jobsArray.slice(start, end);

    queueBody.innerHTML = "";
    for (const [id, job] of pageItems) {
      const tr = document.createElement("tr");
      const statusClass = "status-" + (job.status.split(" ")[0] || "Queued");
      tr.innerHTML = `
        <td>${id}</td>
        <td>${job.youtube_title || ""}</td>
        <td>${job.saved_filename || ""}</td>
        <td><a href="${job.url}" target="_blank">${job.url}</a></td>
		<td class="${statusClass}" title="${job.log || ''}">${job.status}</td>
      `;
      queueBody.appendChild(tr);
    }

    renderPagination(totalPages);
  }

  function renderPagination(totalPages) {
    let paginationDiv = document.getElementById("pagination");
    if (!paginationDiv) {
      paginationDiv = document.createElement("div");
      paginationDiv.id = "pagination";
      paginationDiv.style.marginTop = "12px";
      document.body.appendChild(paginationDiv);
    }
    paginationDiv.innerHTML = "";

    if (totalPages <= 1) return;

    for (let i = 1; i <= totalPages; i++) {
      const btn = document.createElement("button");
      btn.textContent = i;
      btn.style.margin = "0 4px";
      btn.disabled = i === currentPage;
      btn.addEventListener("click", () => {
        currentPage = i;
        renderTable();
      });
      paginationDiv.appendChild(btn);
    }
  }

  // Poll every 3 seconds
  setInterval(fetchAndRender, 3000);
  fetchAndRender();
});
