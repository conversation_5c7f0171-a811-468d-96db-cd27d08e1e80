document.addEventListener("DOMContentLoaded", function () {
    const form = document.getElementById("urlForm");
    const urlInput = document.getElementById("urlInput");
    const queueBody = document.getElementById("queueBody");

    form.addEventListener("submit", async function (e) {
        e.preventDefault();
        const url = urlInput.value;
        if (!url) return;

        try {
            const response = await fetch("/submit", {
                method: "POST",
                headers: { "Content-Type": "application/x-www-form-urlencoded" },
                body: `url=${encodeURIComponent(url)}`
            });

            const data = await response.json();
            console.log("Job added:", data);
            urlInput.value = "";
        } catch (err) {
            console.error("Failed to add job:", err);
        }
    });

    async function fetchProgress() {
        try {
            const res = await fetch("/progress");
            const jobs = await res.json();

            queueBody.innerHTML = "";
            for (const [id, job] of Object.entries(jobs)) {
                const row = document.createElement("tr");
                row.innerHTML = `
                    <td>${id}</td>
                    <td><a href="${job.url}" target="_blank">${job.url}</a></td>
                    <td class="status-${job.status}">${job.status}</td>
                `;
                queueBody.appendChild(row);
            }
        } catch (err) {
            console.error("Error fetching job progress:", err);
        }
    }

    setInterval(fetchProgress, 3000);
    fetchProgress();
});
