from flask import Flask, request, render_template, redirect, url_for, send_from_directory, flash
import subprocess
import os

app = Flask(__name__)
app.secret_key = 'supersecretkey'  # Needed for flash messages

OUTPUT_DIR = os.path.expanduser("~/transcripts")
SCRIPT_PATH = os.path.abspath("transcribe.sh")

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        url = request.form['youtube_url']
        if not url.strip():
            flash("Please enter a valid YouTube URL.")
            return redirect(url_for('index'))

        try:
            subprocess.run([SCRIPT_PATH, url], check=True)
            flash("✅ Transcription started. Refresh this page after a few minutes to see the output.")
        except subprocess.CalledProcessError as e:
            flash(f"❌ Error: {e}")
        
        return redirect(url_for('index'))
    
    # Show list of available transcripts
    transcripts = [f for f in os.listdir(OUTPUT_DIR) if f.endswith("-clean.txt")]
    return render_template("index.html", transcripts=transcripts)

@app.route('/transcripts/<filename>')
def download_file(filename):
    return send_from_directory(OUTPUT_DIR, filename, as_attachment=True)

if __name__ == '__main__':
	app.run(debug=True, host='0.0.0.0', port=5050)