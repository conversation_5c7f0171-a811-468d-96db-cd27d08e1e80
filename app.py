import os
import threading
import uuid
import subprocess
import time
import shutil
from flask import Flask, render_template, request, jsonify, redirect, url_for
from werkzeug.utils import secure_filename

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size
OUTPUT_DIR = os.path.expanduser("~/transcriber-web/transcripts")
TRANSCRIBE_SCRIPT = os.path.expanduser("~/transcriber-web/transcribe.sh")
UPLOAD_DIR = os.path.expanduser("~/transcriber-web/uploads")
ALLOWED_EXTENSIONS = {'mp3', 'wav', 'm4a', 'flac', 'ogg'}

job_queue = []
jobs_status = {}

import json

JOB_HISTORY_FILE = os.path.expanduser("~/transcriber-web/job_history.json")

def load_job_history():
    global jobs_status
    if os.path.exists(JO<PERSON>_HISTORY_FILE):
        with open(JOB_HISTORY_FILE, 'r') as f:
            try:
                jobs_status.update(json.load(f))
            except Exception:
                print("⚠️ Failed to load job history")

def save_job_to_history(job_id):
    with open(JOB_HISTORY_FILE, 'w') as f:
        json.dump(jobs_status, f, indent=2)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_youtube_title(url):
    try:
        result = subprocess.run(
            ["yt-dlp", "--get-title", url],
            capture_output=True, text=True, check=True
        )
        return result.stdout.strip()
    except Exception:
        return None

def run_process_with_progress(cmd, job_id, step_name):
    proc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

    while True:
        line = proc.stderr.readline()
        if not line and proc.poll() is not None:
            break
        if line:
            if "%" in line and "ETA" in line:
                try:
                    parts = line.split()
                    percent = next((p for p in parts if p.endswith("%")), None)
                    eta_index = parts.index("ETA") + 1 if "ETA" in parts else None
                    eta = parts[eta_index] if eta_index and eta_index < len(parts) else None
                    jobs_status[job_id]['status'] = f"{step_name} ({percent} ETA {eta})"
                except Exception:
                    pass
            else:
                jobs_status[job_id]['status'] = step_name
    proc.wait()
    return proc.returncode

def process_jobs():
    while True:
        if job_queue:
            job = job_queue.pop(0)
            job_id = job['id']
            job_type = job.get('type', 'youtube')

            jobs_status[job_id]['status'] = 'Queued'

            if job_type == 'youtube':
                url = job['url']
                yt_title = get_youtube_title(url)
                yt_title = yt_title or f"video_{job_id}"
                filename = secure_filename(yt_title)

                jobs_status[job_id]['youtube_title'] = yt_title
                jobs_status[job_id]['saved_filename'] = filename

                os.makedirs(OUTPUT_DIR, exist_ok=True)

                jobs_status[job_id]['status'] = "Downloading (0%)"
                out_template = os.path.join(OUTPUT_DIR, f"{filename}.%(ext)s")

                cmd_download = [
                    "yt-dlp",
                    "-f", "bestaudio",
                    "--extract-audio",
                    "--audio-format", "mp3",
                    "-o", out_template,
                    url
                ]

                print(f"[DEBUG] Job {job_id} download command: {' '.join(cmd_download)}")

                retcode = run_process_with_progress(cmd_download, job_id, "Downloading")
                if retcode != 0:
                    jobs_status[job_id]['status'] = "Error during download"
                    save_job_to_history(job_id)
                    continue

                audio_file = os.path.join(OUTPUT_DIR, f"{filename}.mp3")

            elif job_type == 'local':
                # For local files, copy to transcripts directory
                source_file = job['file_path']
                original_filename = job['original_filename']
                filename = secure_filename(os.path.splitext(original_filename)[0])

                jobs_status[job_id]['youtube_title'] = original_filename
                jobs_status[job_id]['saved_filename'] = filename

                os.makedirs(OUTPUT_DIR, exist_ok=True)

                # Copy file to transcripts directory as mp3
                audio_file = os.path.join(OUTPUT_DIR, f"{filename}.mp3")
                jobs_status[job_id]['status'] = "Copying file..."

                try:
                    shutil.copy2(source_file, audio_file)
                    # Clean up uploaded file
                    os.remove(source_file)
                except Exception as e:
                    jobs_status[job_id]['status'] = f"Error copying file: {str(e)}"
                    save_job_to_history(job_id)
                    continue

            jobs_status[job_id]['status'] = "Transcribing (estimating time...)"

            cmd_transcribe = [
                TRANSCRIBE_SCRIPT,
                audio_file,
                "--model", "medium",
                "--output_format", "txt",
                "--output_dir", OUTPUT_DIR
            ]
            ret = subprocess.run(cmd_transcribe, capture_output=True, text=True)
            if ret.returncode != 0:
                jobs_status[job_id]['status'] = "Error during transcription"
                jobs_status[job_id]['log'] = ret.stderr
                print(f"Transcription error for job {job_id}: {ret.stderr}")
                save_job_to_history(job_id)
                continue

            jobs_status[job_id]['status'] = "Done"
            save_job_to_history(job_id)
        else:
            time.sleep(2)

@app.route('/')
def index():
    return render_template('index.html', jobs=jobs_status)

@app.route('/local')
def local():
    return render_template('local.html', jobs=jobs_status)

@app.route('/submit', methods=['POST'])
def submit():
    url = request.form['url']
    job_id = str(uuid.uuid4())
    job = {'id': job_id, 'url': url, 'type': 'youtube'}
    job_queue.append(job)
    jobs_status[job_id] = {
        'url': url,
        'status': 'Queued',
        'youtube_title': '',
        'saved_filename': '',
        'log': '',
        'type': 'youtube'
    }
    return jsonify({'job_id': job_id})

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': 'No file selected'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    if file and allowed_file(file.filename):
        # Create uploads directory if it doesn't exist
        os.makedirs(UPLOAD_DIR, exist_ok=True)

        # Generate unique filename
        job_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_DIR, f"{job_id}_{filename}")

        try:
            file.save(file_path)

            # Add to job queue
            job = {
                'id': job_id,
                'type': 'local',
                'file_path': file_path,
                'original_filename': filename
            }
            job_queue.append(job)
            jobs_status[job_id] = {
                'url': f'Local file: {filename}',
                'status': 'Queued',
                'youtube_title': filename,
                'saved_filename': secure_filename(os.path.splitext(filename)[0]),
                'log': '',
                'type': 'local'
            }

            return jsonify({'job_id': job_id, 'filename': filename})
        except Exception as e:
            return jsonify({'error': f'Failed to save file: {str(e)}'}), 500
    else:
        return jsonify({'error': 'Invalid file type. Please upload MP3, WAV, M4A, FLAC, or OGG files.'}), 400

@app.route('/progress')
def progress():
    return jsonify(jobs_status)

load_job_history()
threading.Thread(target=process_jobs, daemon=True).start()

if __name__ == '__main__':
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(UPLOAD_DIR, exist_ok=True)
    app.run(debug=True, host='0.0.0.0', port=5050)
