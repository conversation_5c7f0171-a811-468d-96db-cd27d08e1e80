import os
import threading
import uuid
import subprocess
import time
from flask import Flask, render_template, request, jsonify
from werkzeug.utils import secure_filename

app = Flask(__name__)
OUTPUT_DIR = os.path.expanduser("~/transcriber-web/transcripts")
TRANSCRIBE_SCRIPT = os.path.expanduser("~/transcriber-web/transcribe.sh")

job_queue = []
jobs_status = {}

def get_youtube_title(url):
    try:
        result = subprocess.run(
            ["yt-dlp", "--get-title", url],
            capture_output=True, text=True, check=True
        )
        return result.stdout.strip()
    except Exception:
        return None

def run_process_with_progress(cmd, job_id, step_name):
    proc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

    while True:
        line = proc.stderr.readline()
        if not line and proc.poll() is not None:
            break
        if line:
            if "%" in line and "ETA" in line:
                try:
                    parts = line.split()
                    percent = next((p for p in parts if p.endswith("%")), None)
                    eta_index = parts.index("ETA") + 1 if "ETA" in parts else None
                    eta = parts[eta_index] if eta_index and eta_index < len(parts) else None
                    jobs_status[job_id]['status'] = f"{step_name} ({percent} ETA {eta})"
                except Exception:
                    pass
            else:
                jobs_status[job_id]['status'] = step_name
    proc.wait()
    return proc.returncode

def process_jobs():
    while True:
        if job_queue:
            job = job_queue.pop(0)
            job_id = job['id']
            url = job['url']

            jobs_status[job_id]['status'] = 'Queued'

            yt_title = get_youtube_title(url)
            yt_title = yt_title or f"video_{job_id}"
            filename = secure_filename(yt_title)

            jobs_status[job_id]['youtube_title'] = yt_title
            jobs_status[job_id]['saved_filename'] = filename

            os.makedirs(OUTPUT_DIR, exist_ok=True)

            jobs_status[job_id]['status'] = "Downloading (0%)"
            out_template = os.path.join(OUTPUT_DIR, f"{filename}.%(ext)s")

            cmd_download = [
                "yt-dlp",
                "-f", "bestaudio",
                "--extract-audio",
                "--audio-format", "mp3",
                "-o", out_template,
                url
            ]

            print(f"[DEBUG] Job {job_id} download command: {' '.join(cmd_download)}")

            retcode = run_process_with_progress(cmd_download, job_id, "Downloading")
            if retcode != 0:
                jobs_status[job_id]['status'] = "Error during download"
                continue

            jobs_status[job_id]['status'] = "Transcribing (estimating time...)"
            audio_file = os.path.join(OUTPUT_DIR, f"{filename}.mp3")

            cmd_transcribe = [
                TRANSCRIBE_SCRIPT,
                audio_file,
                "--model", "medium",
                "--output_format", "txt",
                "--output_dir", OUTPUT_DIR
            ]
            ret = subprocess.run(cmd_transcribe, capture_output=True, text=True)
            if ret.returncode != 0:
                jobs_status[job_id]['status'] = "Error during transcription"
                jobs_status[job_id]['log'] = ret.stderr
                print(f"Transcription error for job {job_id}: {ret.stderr}")
                continue

            jobs_status[job_id]['status'] = "Done"
        else:
            time.sleep(2)

@app.route('/')
def index():
    return render_template('index.html', jobs=jobs_status)

@app.route('/submit', methods=['POST'])
def submit():
    url = request.form['url']
    job_id = str(uuid.uuid4())
    job = {'id': job_id, 'url': url}
    job_queue.append(job)
    jobs_status[job_id] = {
        'url': url,
        'status': 'Queued',
        'youtube_title': '',
        'saved_filename': '',
        'log': ''
    }
    return jsonify({'job_id': job_id})

@app.route('/progress')
def progress():
    return jsonify(jobs_status)

threading.Thread(target=process_jobs, daemon=True).start()

if __name__ == '__main__':
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    app.run(debug=True, host='0.0.0.0', port=5050)
