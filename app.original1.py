import os
import threading
import uuid
import subprocess
from flask import Flask, render_template, request, jsonify

app = Flask(__name__)
OUTPUT_DIR = os.path.expanduser("~/transcriber-web/transcripts")
TRANSCRIBE_SCRIPT = os.path.expanduser("~/transcriber-web/transcribe.sh")

# Job Queue and Status
job_queue = []
jobs_status = {}

def process_jobs():
    while True:
        if job_queue:
            job = job_queue.pop(0)
            job_id = job['id']
            url = job['url']
            jobs_status[job_id]['status'] = 'Downloading'
            
            try:
                result = subprocess.run([TRANSCRIBE_SCRIPT, url], capture_output=True, text=True)
                if result.returncode != 0:
                    jobs_status[job_id]['status'] = 'Error'
                    jobs_status[job_id]['log'] = result.stderr
                else:
                    jobs_status[job_id]['status'] = 'Done'
                    jobs_status[job_id]['log'] = result.stdout
            except Exception as e:
                jobs_status[job_id]['status'] = 'Error'
                jobs_status[job_id]['log'] = str(e)

@app.route('/')
def index():
    return render_template('index.html', jobs=jobs_status)

@app.route('/submit', methods=['POST'])
def submit():
    url = request.form['url']
    job_id = str(uuid.uuid4())
    job = {'id': job_id, 'url': url}
    job_queue.append(job)
    jobs_status[job_id] = {'url': url, 'status': 'Queued', 'log': ''}
    return jsonify({'job_id': job_id})

@app.route('/progress')
def progress():
    return jsonify(jobs_status)

# Start job processor thread
threading.Thread(target=process_jobs, daemon=True).start()

if __name__ == '__main__':
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    app.run(debug=True, host='0.0.0.0', port=5050)
