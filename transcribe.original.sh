#!/bin/bash

# Exit on any error
set -e

# === CONFIG ===
WHISPER_ENV="$HOME/whisper-env"
OUTPUT_DIR="$HOME/transcriber-web/transcripts"
WHISPER_MODEL="medium"  # or "small", "base", etc.

# === INPUT ===
YT_URL="$1"
if [ -z "$YT_URL" ]; then
  echo "❌ Please provide a YouTube URL."
  exit 1
fi

# === Ensure output folder exists ===
mkdir -p "$OUTPUT_DIR"

# === Activate whisper virtualenv ===
source "$WHISPER_ENV/bin/activate"

# === Download MP3 from YouTube ===
echo "📥 Downloading audio..."
yt-dlp -f bestaudio --extract-audio --audio-format mp3 \
  -o "$OUTPUT_DIR/%(title)s.%(ext)s" "$YT_URL"

# === Get the downloaded file name ===
MP3_FILE=$(find "$OUTPUT_DIR" -type f -name "*.mp3" -print0 | xargs -0 ls -t | head -1)
BASENAME=$(basename "$MP3_FILE" .mp3)

echo "📝 Transcribing: $BASENAME"

# === Transcribe using whisper ===
whisper "$MP3_FILE" --model "$WHISPER_MODEL" --output_format txt --output_dir "$OUTPUT_DIR"

# === Remove timestamps from transcript ===
TXT_FILE="$OUTPUT_DIR/$BASENAME.txt"
CLEAN_FILE="$OUTPUT_DIR/$BASENAME-clean.txt"

# Remove lines that look like timestamps
grep -vE '^\[[0-9]{2}:[0-9]{2}\.[0-9]{3} -->' "$TXT_FILE" > "$CLEAN_FILE"

echo "✅ Done! Transcript saved to:"
echo "$CLEAN_FILE"
