<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Local File Transcriber</title>
<script src="/static/local.js" defer></script>
<style>
  body { font-family: Arial, sans-serif; margin: 40px; background: #f7f7f7; }
  h1 { color: #333; }
  .nav-links { margin-bottom: 20px; }
  .nav-links a { 
    padding: 8px 16px; 
    margin-right: 10px; 
    background: #007bff; 
    color: white; 
    text-decoration: none; 
    border-radius: 4px; 
  }
  .nav-links a:hover { background: #0056b3; }
  .nav-links a.active { background: #28a745; }
  .upload-form { 
    margin-bottom: 20px; 
    padding: 20px; 
    background: white; 
    border-radius: 8px; 
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  .file-input-wrapper {
    position: relative;
    display: inline-block;
    margin-right: 10px;
  }
  .file-input {
    position: absolute;
    left: -9999px;
  }
  .file-input-label {
    padding: 8px 16px;
    background: #6c757d;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    display: inline-block;
  }
  .file-input-label:hover {
    background: #5a6268;
  }
  .selected-file {
    margin: 10px 0;
    padding: 8px;
    background: #e9ecef;
    border-radius: 4px;
    font-style: italic;
  }
  button { padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; }
  button:hover { background: #218838; }
  button:disabled { background: #6c757d; cursor: not-allowed; }
  table { border-collapse: collapse; width: 100%; background: white; margin-top: 20px; }
  th, td { border: 1px solid #ccc; padding: 10px; text-align: left; }
  th { background-color: #eee; }
  .status-Queued { color: orange; }
  .status-Copying { color: blue; }
  .status-Transcribing { color: purple; }
  .status-Done { color: green; }
  .status-Error { color: red; }
  #searchInput { margin-bottom: 12px; padding: 6px; width: 300px; }
  .upload-info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 15px;
  }
</style>
</head>
<body>
<h1>Local File Transcriber</h1>

<div class="nav-links">
  <a href="/">YouTube Transcriber</a>
  <a href="/local" class="active">Local File Transcriber</a>
</div>

<div class="upload-info">
  <strong>Supported formats:</strong> MP3, WAV, M4A, FLAC, OGG<br>
  <strong>Max file size:</strong> 500MB<br>
  <strong>Note:</strong> Files will be transcribed using Whisper and saved in the transcripts directory.
</div>

<div class="upload-form">
  <form id="uploadForm" enctype="multipart/form-data">
    <div class="file-input-wrapper">
      <input type="file" id="fileInput" name="file" accept=".mp3,.wav,.m4a,.flac,.ogg" class="file-input" required />
      <label for="fileInput" class="file-input-label">Choose Audio File</label>
    </div>
    <button type="submit" id="uploadBtn" disabled>Upload & Transcribe</button>
    <div id="selectedFile" class="selected-file" style="display: none;"></div>
  </form>
</div>

<input type="text" id="searchInput" placeholder="Search in queue..." />

<h2>Transcription Queue</h2>
<table>
  <thead>
    <tr>
      <th>Job ID</th>
      <th>File Name</th>
      <th>Saved Filename</th>
      <th>Source</th>
      <th>Status</th>
    </tr>
  </thead>
  <tbody id="queueBody">
    <!-- Filled dynamically by JavaScript -->
  </tbody>
</table>
</body>
</html>
