<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Local File Transcriber</title>
<script src="https://cdn.tailwindcss.com"></script>
<script src="/static/local.js" defer></script>
<script>
  tailwind.config = {
    theme: {
      extend: {
        colors: {
          primary: {
            50: '#f0f9ff',
            100: '#e0f2fe',
            500: '#0ea5e9',
            600: '#0284c7',
            700: '#0369a1',
          },
          gray: {
            50: '#f9fafb',
            100: '#f3f4f6',
            200: '#e5e7eb',
            300: '#d1d5db',
            400: '#9ca3af',
            500: '#6b7280',
            600: '#4b5563',
            700: '#374151',
            800: '#1f2937',
            900: '#111827',
          }
        }
      }
    }
  }
</script>
<style>
  .status-Queued { @apply text-amber-600; }
  .status-Downloading { @apply text-blue-600; }
  .status-Transcribing { @apply text-purple-600; }
  .status-Done { @apply text-green-600; }
  .status-Error { @apply text-red-600; }
  .status-Copying { @apply text-indigo-600; }
</style>
</head>
<body class="bg-gray-50 min-h-screen">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Local File Transcriber</h1>
      <p class="text-gray-600">Upload audio files and convert them to text transcriptions using AI</p>
    </div>

    <!-- Navigation -->
    <div class="mb-8">
      <nav class="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <a href="/" class="px-4 py-2 text-sm font-medium rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-50 transition-colors">
          YouTube Transcriber
        </a>
        <a href="/local" class="px-4 py-2 text-sm font-medium rounded-md bg-white text-primary-700 shadow-sm">
          Local Files
        </a>
      </nav>
    </div>

    <!-- Info Banner -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-blue-800">Upload Information</h3>
          <div class="mt-2 text-sm text-blue-700">
            <ul class="list-disc list-inside space-y-1">
              <li><strong>Supported formats:</strong> MP3, WAV, M4A, FLAC, OGG</li>
              <li><strong>Maximum file size:</strong> 500MB</li>
              <li><strong>Processing:</strong> Files are transcribed using Whisper AI and saved to the transcripts directory</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Upload Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Upload Audio File</h2>
      <form id="uploadForm" enctype="multipart/form-data" class="space-y-4">
        <div class="flex flex-col sm:flex-row gap-4 items-end">
          <div class="flex-1">
            <label for="fileInput" class="block text-sm font-medium text-gray-700 mb-2">Choose Audio File</label>
            <div class="relative">
              <input
                type="file"
                id="fileInput"
                name="file"
                accept=".mp3,.wav,.m4a,.flac,.ogg"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-3 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100 file:cursor-pointer cursor-pointer border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>
          </div>
          <button
            type="submit"
            id="uploadBtn"
            disabled
            class="px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            Upload & Transcribe
          </button>
        </div>
        <div id="selectedFile" class="hidden p-3 bg-gray-50 rounded-lg text-sm text-gray-600"></div>
      </form>
    </div>

    <!-- Search and Queue Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
      <div class="p-6 border-b border-gray-200">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h2 class="text-lg font-semibold text-gray-900">Local File Queue</h2>
          <div class="relative">
            <input
              type="text"
              id="searchInput"
              placeholder="Search files..."
              class="w-full sm:w-80 px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
            />
            <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Mobile-friendly table -->
      <div class="overflow-hidden">
        <div class="hidden lg:block">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Name</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Saved Filename</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody id="queueBody" class="bg-white divide-y divide-gray-200">
              <!-- Filled dynamically by JavaScript -->
            </tbody>
          </table>
        </div>

        <!-- Mobile cards -->
        <div id="queueBodyMobile" class="lg:hidden divide-y divide-gray-200">
          <!-- Filled dynamically by JavaScript -->
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div id="pagination" class="mt-6 flex justify-center"></div>
  </div>
</body>
</html>
