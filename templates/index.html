<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>YouTube Transcriber</title>
<script src="https://cdn.tailwindcss.com"></script>
<script src="/static/app.js" defer></script>
<script>
  tailwind.config = {
    theme: {
      extend: {
        colors: {
          primary: {
            50: '#f0f9ff',
            100: '#e0f2fe',
            500: '#0ea5e9',
            600: '#0284c7',
            700: '#0369a1',
          },
          gray: {
            50: '#f9fafb',
            100: '#f3f4f6',
            200: '#e5e7eb',
            300: '#d1d5db',
            400: '#9ca3af',
            500: '#6b7280',
            600: '#4b5563',
            700: '#374151',
            800: '#1f2937',
            900: '#111827',
          }
        }
      }
    }
  }
</script>
<style>
  .status-Queued { @apply text-amber-600; }
  .status-Downloading { @apply text-blue-600; }
  .status-Transcribing { @apply text-purple-600; }
  .status-Done { @apply text-green-600; }
  .status-Error { @apply text-red-600; }
  .status-Copying { @apply text-indigo-600; }
</style>
</head>
<body class="bg-gray-50 min-h-screen">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">YouTube Transcriber</h1>
      <p class="text-gray-600">Convert YouTube videos to text transcriptions using AI</p>
    </div>

    <!-- Navigation -->
    <div class="mb-8">
      <nav class="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <a href="/" class="px-4 py-2 text-sm font-medium rounded-md bg-white text-primary-700 shadow-sm">
          YouTube Transcriber
        </a>
        <a href="/local" class="px-4 py-2 text-sm font-medium rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-50 transition-colors">
          Local Files
        </a>
      </nav>
    </div>

    <!-- Form Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Add New Transcription</h2>
      <form id="urlForm" class="space-y-4">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <div class="lg:col-span-2">
            <label for="urlInput" class="block text-sm font-medium text-gray-700 mb-2">YouTube URL</label>
            <input
              type="text"
              id="urlInput"
              name="url"
              placeholder="https://www.youtube.com/watch?v=..."
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
            />
          </div>
          <div>
            <label for="filenameInput" class="block text-sm font-medium text-gray-700 mb-2">Custom Filename (Optional)</label>
            <input
              type="text"
              id="filenameInput"
              name="filename"
              placeholder="my-transcript"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
            />
          </div>
        </div>
        <div class="flex justify-end">
          <button
            type="submit"
            class="px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"
          >
            Add to Queue
          </button>
        </div>
      </form>
    </div>

    <!-- Search and Queue Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
      <div class="p-6 border-b border-gray-200">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h2 class="text-lg font-semibold text-gray-900">Transcription Queue</h2>
          <div class="relative">
            <input
              type="text"
              id="searchInput"
              placeholder="Search transcriptions..."
              class="w-full sm:w-80 px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
            />
            <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Mobile-friendly table -->
      <div class="overflow-hidden">
        <div class="hidden lg:block">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Filename</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">URL</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody id="queueBody" class="bg-white divide-y divide-gray-200">
              <!-- Filled dynamically by JavaScript -->
            </tbody>
          </table>
        </div>

        <!-- Mobile cards -->
        <div id="queueBodyMobile" class="lg:hidden divide-y divide-gray-200">
          <!-- Filled dynamically by JavaScript -->
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div id="pagination" class="mt-6 flex justify-center"></div>
  </div>
</body>
</html>
