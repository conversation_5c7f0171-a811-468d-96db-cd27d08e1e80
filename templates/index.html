<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>YouTube Transcriber Queue</title>
<script src="/static/app.js" defer></script>
<style>
  body { font-family: Arial, sans-serif; margin: 40px; background: #f7f7f7; }
  h1 { color: #333; }
  form { margin-bottom: 20px; }
  input[type="text"] { padding: 8px; margin-right: 8px; }
  button { padding: 8px 16px; }
  table { border-collapse: collapse; width: 100%; background: white; }
  th, td { border: 1px solid #ccc; padding: 10px; text-align: left; }
  th { background-color: #eee; }
  .status-Queued { color: orange; }
  .status-Downloading { color: blue; }
  .status-Transcribing { color: purple; }
  .status-Done { color: green; }
  .status-Error { color: red; }
  #searchInput { margin-bottom: 12px; padding: 6px; width: 300px; }
</style>
</head>
<body>
<h1>YouTube Transcriber</h1>

<form id="urlForm">
  <input type="text" id="urlInput" name="url" placeholder="Enter YouTube URL" required style="width:400px" />
  <input type="text" id="filenameInput" name="filename" placeholder="Save as filename (optional)" style="width:200px" />
  <button type="submit">Add to Queue</button>
</form>

<input type="text" id="searchInput" placeholder="Search in queue..." />

<h2>Transcription Queue</h2>
<table>
  <thead>
    <tr>
      <th>Job ID</th>
      <th>YouTube Title</th>
      <th>Saved Filename</th>
      <th>YouTube URL</th>
      <th>Status</th>
    </tr>
  </thead>
  <tbody id="queueBody">
    <!-- Filled dynamically by JavaScript -->
  </tbody>
</table>
</body>
</html>
