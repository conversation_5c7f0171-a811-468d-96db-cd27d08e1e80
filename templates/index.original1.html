<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Transcriber Queue</title>
    <script src="/static/app.js" defer></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f7f7f7; }
        h1 { color: #333; }
        form { margin-bottom: 20px; }
        input[type="text"] { width: 400px; padding: 8px; }
        button { padding: 8px 16px; }
        table { border-collapse: collapse; width: 100%; background: white; }
        th, td { border: 1px solid #ccc; padding: 10px; text-align: left; }
        th { background-color: #eee; }
        .status-Queued { color: orange; }
        .status-Downloading, .status-Transcribing { color: blue; }
        .status-Done { color: green; }
        .status-Error { color: red; }
    </style>
</head>
<body>
    <h1>YouTube Transcriber</h1>

    <form id="urlForm">
        <input type="text" id="urlInput" name="url" placeholder="Enter YouTube URL" required>
        <button type="submit">Add to Queue</button>
    </form>

    <h2>Transcription Queue</h2>
    <table>
        <thead>
            <tr>
                <th>Job ID</th>
                <th>YouTube URL</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody id="queueBody">
            <!-- Filled dynamically by JavaScript -->
        </tbody>
    </table>
</body>
</html>
