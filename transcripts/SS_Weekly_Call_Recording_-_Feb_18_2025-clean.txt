What's up, everyone?
Hello.
What's up, <PERSON>?
What's up, <PERSON>?
What's up, <PERSON>?
What's up, <PERSON>?
<PERSON>?
I did not butcher your name, man.
No, actually, I'm using my Canon,
just because, yeah.
Hopefully, it doesn't go up
because it has to pause after 30 minutes,
just because I go pure USB.
Hey, <PERSON>ab.
What's up, man?
All right, let's wait for everyone to come.
I mean, we have a value-packed session today.
Yeah.
What's up, everybody?
Hey, I'm <PERSON><PERSON>.
Hey, <PERSON>.
Hey, <PERSON>ab.
Hey, <PERSON><PERSON><PERSON><PERSON>.
Hey, <PERSON><PERSON>.
I hope, again, I said your name right.
Hey, <PERSON>.
Hey, <PERSON><PERSON><PERSON>, man.
How you doing?
Hey, <PERSON>.
What's up, <PERSON>?
There's a lot of new faces.
Hey, <PERSON>, man.
How you doing?
Hey, <PERSON>.
Hey, <PERSON><PERSON>.
Good to see you guys.
Yeah, it's funny because now,
there's a lot of new faces,
yeah, it's funny because now there's a lot of new people,
new people welcome,
and there's like people that I see every week,
and it's like, now it's like,
if I don't see you, I kind of like feel bad.
Hey, <PERSON>.
Hey, wait.
Wait.
All right, everyone.
So just wait for everyone to come when we get started.
So we're gonna have like a 60 minute
where we go over a lecture.
Yes.
And then we're gonna tackle all you guys' questions, right?
So I'm not gonna leave anyone's question until next week,
so we're gonna answer everything today, okay?
Also, so what we're gonna do basically,
the lecture of today is gonna be,
we're gonna tackle everything,
like from basically the mental
to practical tips about practical tips.
So everything at its end.
We're gonna also talk about limits and beliefs.
We're gonna talk about basically how,
what I would do, how do you start with case studies?
How would you compile case studies for your first clients?
You can basically like upsell them and get new clients.
So yeah, let me know when you guys are ready
and we can get started.
And then what we're gonna do basically
is we're gonna have like an interactive chat
where we can, basically I'm gonna talk to you guys
and then you guys will answer me in the chat.
And then, so it becomes like more interactive, right?
I think it's better that way.
So like me just presenting,
so we have like a more of interactive one-on-one.
What does that sound?
Nice.
Hey James.
All right guys, let me just.
Hey Nicole.
All right, let me just share my screen.
All right, check this out.
Now.
Hey.
So let me show you guys. I think some people did see the screen right here. So I used to
actually, I did print this. I actually just put this in my room when I first got started
just because I had these exact opposites of these limits and beliefs, right? So let's tackle mental
unconscious limiting beliefs, right? So the worst thing we can do guys is going over four or five
days doing our wish consistently, then trail off into something else or changing our approach.
It could be because unconsciously we are trying to avoid the pain associated with direction,
judgment, sales, delivery, and all the issues that come with actually having clients.
Let me tell you guys a story. So when I first got started, right before I first hit my 10k month,
I would actually get a positive lead in my campaign. And when they are actually like,
you know, they're willing to hop on a call and I would have this unconscious belief of not
actually booking the call. Like actually having this weird feeling of like I would have like this
unconscious feeling of not actually wanting to hop on a call with that person, even though they
are interested. So that's even worse. Yes. No, not just you. No, not just you. I would get scared.
And I came up from a bouncing, you know, background. So like, why would I be scared of this?
So yeah, so I had this exact feeling like I would feel like I would feel afraid
on jumping on a call with someone I wouldn't like, I don't know. Especially for me, like I'm an
introvert, which is weird. I'm like, I'm talking to you guys. Yeah, it's important. Yes.
A lot of these things, like I would get scared. Yeah, like I wouldn't like, why am I getting
scared for the thing that I am trying to achieve? So this is an unconscious belief, right?
So what I've done, right, is I just put all my unconscious belief in a Google Doc, like
be honest with yourself. Really be honest with yourself. Be like, what are the triggers that
make me feel this way, right? And it's usually multiple things. Like we talked about, such as
rejection, fear of rejection, the actual having clients. So what I've done is I switched my entire
belief system to this. Having clients is easy. Sales is easy. I love outreach. Outreach is fun.
I get outreach. Everyone responds these days. The market is abundant, right? I know the right
approach. I should get the copyright second. I'll just do double today. Nothing is more important
than outreach. I always rather do outreach. I'm not spamming people. I can genuinely help them,
and it's my duty to do so. And I'm going to give you guys an example. So here's just an example
about follow-ups. So we have this little cute little email. So we have a follow-up. We have
three people. So we have person A, person B, and person C. And before we even scroll down,
who do you guys think is going to have the client? Who's going to get the clients? Let's make it a
game. Which color is going to get the clients? Let's make it a little bit of fun. Is it person A,
B, or C? Blue. The person who does the...
Okay. Gold. B, C. Whoever is actionable. Yes. Okay. All of these are good answers.
Okay. So we have the person A, and the person A says, follow-ups are annoying. They'll book
that call if they really want. I don't want to bother them. And that was me too. And probably
one of you in this chat still thinks this way. I mean, we all do. And then we have person B.
So person B, I'm going to say, follow-ups can be really annoying, but they do improve the
chances of responses. Okay. Now, the person C. Well, who said the person C?
Person C is going to be like business owners, love and respect, persistence.
And following-up is within their best interests. So who said C? Yeah. Joelle. Yeah. Yeah. Nice one.
Winner, winner. Yeah. So we have three different paradigms, right? So we have person A, person B,
person C. Those three people have three different unconscious beliefs and ways of thinking.
So who do you think each one of them is going to do? Now, person A is going to say, I said no
follow-ups, meaning zero times. Person B is going to be, I'll try a couple of times, maybe two times.
And person C is going to say, I'll keep trying until the supplier will sign me to fuck off,
meaning eight times plus. So who do you think is going to get the book meeting?
Exactly. I can't show you guys how many times I actually followed up with leads that I eventually
closed. And they were one of the highest deals that I got. If you guys been following kind of
like my journey, like the deal I've closed was about 10K, right? From a, I think it wasn't a
retainer actually. It was a Microsoft affiliate, right? There was like a big company. And I remember
I was talking to that founder. It was like, you just kept following up. I kept following up with
them like nine times. And eventually he's like, okay, let's just hop on a call. And I eventually
closed that guy, right? And now looking back at them, like if I didn't follow up, like that,
you know, that cash collected, right? That cash collected that I, you know,
managed to double down on my outreach, I wouldn't be here and not talking to you guys, right?
Okay. So now that we understand that there is mental things that we need to tackle,
right? And we need to basically start a paradigm into basically a new identity, right?
The identity is the root of all mental. Once we understand this, then what are the practical
things that we want to do or we should do to actually achieve our goal? So I want to share
with you guys one of the mental models that I use. Basically, it's going to help you, not just in
sales systems, it's going to help you in general. And in our case, it's really going to help us
as a sales and agency owner. So first of all, back into mental. The second thing is I want
you guys to define your outcome. Like what are you actually trying to achieve currently?
Do you have like a, you know, like a number in your head? Like how many clients? Like how much
how much money do you want to make? Is it 10K, 15K? Like what is your guys' outcome? Like how much
you guys want, like are looking to make per month? Just be realistic, okay? I think it's better this
way. What is it you guys' goals? Like what is the actual outcome? 25K? Yeah. I think most of us want
to get to like be 25K, 20K. Yeah. I think most people would be happy with 20K. I think 20K is
like because most of us don't want to be billionaires, right? I mean, geez, these people
don't have time to do any relationship. No fun, nothing. GD said, how wide can we go?
Yeah. Jonathan said first client. Yeah. That's reasonable. First goal is 5K. Most I can leave
the army to go back home and focus all time on this then scale. Yeah. Yeah. You remind me of myself,
man. Yeah. First clients for now. I think getting past 10 will prove to myself the sky is the limit.
Yeah. The first 10K a month is like the hardest because it's all, you know, it's all really hard
to work. And then after 10K, it's all systems. Trust me, guys. First 10K, once you get the 10K,
that feeling of being actually like, you know, that feeling of that's going to go away.
100K a month by the end of this year. Man, that's a really good one. Yeah. 10K a month. Yeah.
But I assume most people would want to go from that 10K to let's say 50K a month, right?
Right. Let's assume that is like the average, like 10 to 50K a month. Yeah. Okay. For sure. So.
So, you know, since within the outcome, why are we trying to achieve? The first thing you want to
focus on, right? So, since we have multiple people here that want to get their first customer,
since you get your, once you get the first customer, right, you have now unlimited motivation,
right? You understand now that your system is working and whatever you are doing works.
And you can scale this, right? Once you get your first client, and a lot of people actually in
the community now got their first client, which is, I mean, it makes me extremely happy. So once
you get your first client, it's like a different, like a different game, right? You have now, you
know that it works, right? That stress will go away. Now, assuming we got our first client,
how do we actually eliminate the tasks that take longer to succeed or not continue to our end goal?
You should ask yourself, how are you currently spending your time? Are you spending time on
low ROI activities? Are you on analysis paralysis? Do you have an actual outcome,
like how much do you want to make per month? Like what is the actual goal? So we can start
reverse engineer it. I want you guys to list them all in a Google Doc and be honest with yourself
and treat it like a journal that no one can read. Then you'll hire yourself. So once you list them,
typically you're going to find that you are switching copies, switching niches, going on
YouTube, looking what are the best copies, then try to tweak on delaying gratification by waiting
for the out leash like to stabilize. Not sending the first place setup. Does anyone here still on
analysis paralysis? Yes, a lot. Most definitely. Yeah. Okay. Yes. Dude, just call me out of my,
yeah, man. Yes. Is anyone here switching niches?
Yeah. Okay. Hopefully. So I see that a lot of people, oh, you have. Yeah.
Does anyone get a little bit scared when they first started the campaign
and they don't get replies within the first few days? Yeah. Yeah, that's a huge one, right?
Yeah, I know. Yeah, I've been through all of this, guys, but I'm here to help you guys and
essentially so we can all crush it. Justin said, I think I'm switching copies to off.
Yeah. First thing you can do is you start your campaign and you switch your copy while
campaign is running. Yeah. Yeah, I haven't gotten much replies.
No. Yeah.
Yes, I feel you, Roger. Yeah. Yeah. Thought about switching on the copy, but I've hold.
Yeah, the key here, guys, is think about it this way. Do you guys really think like,
since here we implement something called system thinking, right? So anything that you try to do
has system, right? Because your cold email system or your cell system doesn't operate on itself.
We have multiple factors that interact with each other. So if you change something, right?
Let's say you change something like your copy. Now you do not know if someone is going to respond
to that initial copy. Now you don't have like, you know, metrics to measure, right? So what's
what doesn't get measured doesn't get improved. Especially if you
you are then let's say thousands of emails, right? Well, let's say you just started your
campaign and you send like 700 emails and the first few days you didn't get any replies.
There's multiple aspects and multiple reasons why you didn't get replies. Multiple things, right?
And sometimes you won't get replies for the first few days and they just go woof.
And I still have this still this day.
Right. So you have to wait for the outreach lag to come up.
What she do is to you to track. Yeah, I'll send you a rule sheet after the call.
Yeah, by the way, guys, James is a really great copywriter. It's even better than me.
OK, let's finish this. OK, now. Since you're going to list all the things that you're doing,
right, that are not conducive to the end goal. Or take longer to succeed.
Now, let's define the task that take you directly and easily to the goal.
It's called practical tips about practical tips. It's kind of like I'm just I just stole this from
like one of my favorites role models, Charlie Munger, and he talks about practical. Practical
thought about practical thought. You guys want to read that book. It's amazing. It's called
Poor Charlie's Almanac. It really helped me like with basically developing multiple mental
models that is going to help you and basically in your own agency.
The most straightforward way to get your first client in the following picking one B2B niche,
scraping five thousand leads off Apollo, validating using Nails.so and generating
one line personalization. That's it. That's how I got my first client.
So I wouldn't worry about other things, right? I will just do this exact thing. I'll scrape
twenty five hundred, put it in our lead cracker. Basically, we have a new Cushita in the classroom.
If you guys notice, there are some changes in the classroom structure. I'm making it more
structured now just because when we first started with this community, well, a lot of people like
asked me about this community. I was like, whoa, like, let me just dump everything that I have
in my computer for everyone. But sometimes you get overwhelmed. So what I've done is I basically
compiled everything and I took a few days and I just compiled everything and I'm in the middle of
basically. But all the middle home blueprints, right? And what we're going to do, we're going
to add something in our classroom where we have every industry on the planet Earth. We're going
to have a copy for it so you guys can be inspired with a specific offer. So you're just going to be
like plug and play, right? Right. I think I think that's going to help you guys a lot. What do you
guys think? Yeah, we're going to have like basically. Every single industry on planet Earth,
we're going to have like real estate, IT, SAS, whatever. We're going to have like an entire
goddamn list and we're going to add it there. And if you guys checked again, the classroom,
there's new prompts there. So multiple prompts about basically market research, how I do everything,
how to figure out pain points, basically no bullshit things, right? Instead of just, you know,
not giving you guys what you need because I hate like I hate when people do that.
Don't give me the bottle down truth. Just give me the truth. The real one. Yeah. Yeah. So expect
that I'm going to constantly be adding things in the community because I like when people like
just make a community and then they just put up a course and then that's it. I mean, the community
has to be updated constantly with new information, new offers. Yeah. Okay. So
so it's quick 2500 you add the URL of the Apollo scrape in the Google Sheets. So you can just go
ahead and click on it and then the filters are going to be applied automatically. So you can
know what filters you previously put in your search. And the next day you're going to scrape
2500 again. And then we're going to use our copywriting formula and then we're going to
reply quickly to least within 10 minutes max. And then we're going to fall off relentlessly
with any lead that shows responses because at the end you are getting their money. So Paul
we are basically deciding our own future. We operate an internet business where people pay us
money and we just hop on a call and they pay us money. So make sure you follow up with them
all the time because at the end you're going to get their money even if they're annoying.
I mean, you're still getting paid. Your first client offer would be a paper lead model
to get you on a sales call. Then an upfront fee typically 30 to 40% upfront. Right. So you'd have
like a first client would be like 1500 as a self fee. And then maybe like 40% just so you know,
interfere with the markets, understand client management, it's going to help you a lot.
Then now your job is to deliver. Guess what? Deliver here in community is offered. I just
went on a call with, I can't really, I can't just pronounce your name then. Shout out to you.
But I hopped on a call with them and basically onboarding new clients and it was good.
Good. I'm always going to help you guys with delivery. Right. So we're not going to be on the
dust. So now once you get case studies, so your first clients, I want you guys to interview your
first clients, add the video video, the video file to your site and repeat the outreach process
for 5K new leads. Okay. The worst thing you can do is worry about fulfillment
and not having a consistent pipeline of leads. Right. So you're, you're made a bottleneck. So
think about your business as a bottleneck. Right. So you have a bottleneck and then that bottleneck
your cold email, right. Your sales system that comes from here. And there's all these conversion
tools until that lead becomes a client. You want to figure out where there's a bottleneck
and that pipeline. Right. And then the flow of the pipeline of leads come in.
This should be the most important thing. Right. Not for me, like for me, all the way from zero
dollars, 25K. I always, I always focused on my pipeline coming like leads every single day
and my fulfillment. I would just leave it in evenings. So my highest ROI task would be
all of the people sending more emails, basically in my mornings, basically during the day until
the evening. And I started like, you know, with the client fulfillment. This is what we recommend
you guys always fulfillment last worry about fulfillment last. Right. How can they choose you?
We'll give you, we'll give them their money back if you don't deliver 30%. Yeah. You're going to,
you're going to refund them. Well, obviously you're not going to, you're going to deliver.
So this is a, you're going to deliver. Always say this. So this refund thing we say, it's just
because we want to get ourselves in the door. Like we, we will deliver. Right. There's no way
we won't deliver. We will deliver. Right.
Yes.
Well, obviously when you're going to hop on a call with them, um, you're, you're going to
come from a consultative point of view. You're going to understand their business. And basically
you're going to show them basically this, your fancy presentation, your, uh, the way you speak,
uh, how your system is going to help them. Right. Yes. Yeah, exactly.
I mean, think about it guys. Like, you know, how much, how much they, most companies like
spend to acquire one client. I mean, just think about it. Like thousands of dollars.
And there's really like not only many, you know,
I'm going to, right. At least PPC, there's referrals where the mom
or like big companies that have personal brands. Right. There's already, there's already a lot of
companies. PPC, there's referrals where the mom or like big companies that have personal brands.
Right. There's our each. Okay. Now, assuming you have your first line, now what's
how can I scale past first clients all the way to 10 to 20 K a month?
Where we're entering something called skill. Now people try to make it extremely damn hard.
What is scale? But now with introduction of systems, that's all. So now your first
clients, you're going to understand that, okay, when I onboard them, I'm getting this issue.
When I talk with this client, I noticed that my communication is not the best.
So how do I apply systems to make this seamless? Now we're talking project management tool.
Now we're talking client touch boards. Right. Now we're talking, maybe if you want to send more,
or hire a VA, that's going to help you do that. Right. There's multiple things to the first same
page, really relentless follow ups and outreach. And it's going past 10 to 20 K is all systems.
Trust me. And those systems are going to constantly change. Even for me now, I constantly
change my systems. All right. One keynote. Like I said, your pipeline must always be full before
thinking about scaling. Your bottleneck will always be pipeline until you get to 20 K a month.
This means your entire time will be sales calls, sending emails and building system. That's really
it. Profilements always come fast from my $0 to 20 K. My performance was always in the evenings
or whenever I finished my high ROI task for the day, which was outreach plus building system.
Okay. Now how do we actually scale? We apply volume by scraping more leads now. We double our
outreach. We use the cash collected to invest more in scraping more leads, more enrichment platforms.
We might hire VA's to scrape leads. Get more inboxes and more throughout wasting the onboard
and seamless. Record your sales scripts. We find and listen to them for feedback.
Okay.
Okay.
Okay.
Okay.
Okay.
Okay.
Okay.
Okay.
Okay.
Okay.
Okay.
Okay.
Okay.
Okay.
So I wouldn't worry too much about it. Okay. Now, second question.
Do we really apply within 10 minutes for the campaigns too or do we have their employees do
it? Well, that's a really good question, but it depends on the clients. In my experience,
the real value comes when you reply for them. Employees sometimes mess up and sometimes they
lose qualified leads, which can be shitty. You can let their team handle replies or give them
access, but it depends on how much it takes. You want basically, and you want to deal with it.
So what I would recommend is replying yourself because it's high. You're delivering ready leads
instead of them relying on them to nurture. So I'm assuming you know how to nurture leads,
right? And get them on a call. So I think the real juice is you delivering the real,
you know, the qualified leads directly to your calendar, just because sometimes they're going
to mess up. Sometimes, I mean, think about it. They hired you for a reason, right?
If they didn't, they wouldn't, they would just, you know, they start their
tells us and themselves and just reply to the
handling those replies, especially as your first points, right? And then you would basically just
nourish them and talk. And then you have now more perceived value. Okay.
What's the best filters in Apollo when you're just starting out as you don't want to go to companies
making more than two, three millions. There's little chance of them getting out in bed with you.
Yeah. And B, you want to start small and make your small mistakes there. Yeah. So to filter
by going to be one to 10 startups and revenue from zero to a hundred K a month, or maximum
one million. Don't, don't ever go for companies making less than a hundred K a year. It's not
worth it. They're just going to shoot time. Cause I mean, if they're making less than a hundred K a
month, you know, how much are they going to pay you? A hundred K a year. So always go from a hundred
K a year minimum, right? Cause they want to be, if they don't make less, they don't have the cash.
Even if they like work, it's not a million. And typically these are going to be startups.
Right. And I love working with startups just because you get to develop like a relationship
with the founder, especially there in their early stages. And what is the most straightforward way
to bootstrap your company? If you don't have any investors, no money, no cash cashflow, no,
you know, nothing is called outbound. Right. Okay. We'd love to know how you build your copy
and personalization for your various clients. Uh, this question from, oh, first of all,
I skipped your question. Let me go back to James and then I'm going to go back to you.
Well, what's your favorite generation system? So my favorite one currently is this one here.
It's the method one blueprint, just because it's very scalable, man. Like I, I, it's just so easy.
I can essentially just scrape and then just put in the data set ID here, remove the limits,
right? And just start my own campaign. It's the easiest about it. And using mails,
that's all you guys notice there's this new model, right? Instead of making an request.
So I can just use mail data. So it's just pretty nice. Right. And then I'm clean company name,
performing a surgery on my personalization, less headaches, right? I can scrape more volumes.
I can just scrape Apollo, take in the data set ID, paste that in here, add more leads, add more
leads, add more leads. So this is like my favorite one now, because I want to like tackle the highest
ROI tasks of my day. And I want this to be, take me like a few hours and then I'll start my campaign.
Okay. Now, I would love to know how you build your copy and personalization for your very
very well. This is huge guys, which is why I include those questions in the onboard in the
questionnaire. So whenever I build basically offers or like,
write copies, I always have this formula. I would have a personalized line,
a clear offer and a clear rally proposition. Now where this is where the real juice is
like, you want to speak to your client. Hey, what do you actually sell? Like instead of saying we
implement lean, agile marketing strategies, like nobody talks like this, like this sells the prospect,
nothing about a business. Like when you wake up and sit down and start to go to your clients,
like the SEO, email, sales, et cetera, you know, the offer shouldn't sound like a
politician, you know, talking, right? Does that make sense guys?
Yeah. So it's kind of like frame it this way. Let me just give you guys an example. So you guys,
when you send me DMs, like you'd say, Hey, Todd, I wanted to pick your brain or like was wondering.
So the same tone, do you guys speak to me in the end? Yeah. I'll encourage to your prospect
and then maybe up the tone a little bit, not too casual, but like in that middle.
Yeah.
Okay. Now we have a prompt that is added to the classroom. Let me go to another tab.
And if I go to classroom resource library, we have a new prompts.
And then, so here's the one that I added for you guys. So clearing the offer plus your clients
offer. So we're going to put in the offer here, right? So it's going to be rewrite the following
offer in clear and compelling way using this framework. So it took me a lot of time to actually
come up with this prompt. So what does the service solve? Example, lack of leads, time consuming,
manual processes, poor engagements. What is the service or solution provided? Example, automated
lead generation system, full service email marketing, strategic SEO implementation. What
is the specific result or outcome? Increased revenue, improved lead quality. And we'll give
a few examples, right? Again, you can fine tune this as you guys want. Did the script build in,
market research, obviously the company name formatter, job title, cleaners,
recent achievements, the end results, generating a PS line, clean dates, clean names,
generating rates, personalization. Again, second market research, pain discovery. These are all
prompts you guys use. Pre-draft them in one-liner and basically a prompt template from founder to
founder, which is this one that I currently use. This is the best one, right? It's like an insider,
you know, graphic message from company. It feels personal and credible, right? So the tone that I
always want you guys to go for is founder to founder. One little hack to do when you are using
chat GPT to like pre-draft the copy. Just add in founder to founder tone conversational with a
Spartan tone succinct with no frilly language. I know this sounds long, but I think I'm just
going to include it in the classroom, right? So you guys can just go ahead and use it. Okay, so
let me know if you want to have a follow-up question or something. And then I have a case
study plus a soft CTA. Basically just asking them a question, not getting them on a call first.
Okay, as for my question, how do you keep everything neat? Joelle, yes. How do you keep
everything neat and organized with all your diverse content? Running mailbox triggers and follow-up.
Yeah, so at first I'm going to give you, I'm going to give you then like my,
what I've done essentially. At first I used to do everything, like every single thing, right?
Well, a couple VAs, I called VAs that handle some of the replies, and so I got to 40k, then I started
hiring, right? When you get to, now what I would recommend, don't do what I did, because it's really
like you're not going to have a life, right? So I didn't have a life at that time. Like I only had a
couple VAs, right? And then essentially what I've noticed is that my founder time was getting killed,
right? Like I wasn't able to focus on systems, right? And how to streamline processes. So I would
actually get, me getting on sales calls. So it was basically the, the face of my process, which I'm
trying to not do currently. So you need founder time to focus on systems rather than admin work
and get on calls. Unless it's a bigger deal, I have to be the face of my process instead of just
sending the sales reps, right? Thank you, Rise. If you guys don't know, Rise is a time tracker.
I would encourage you guys to track your time and know what you're doing on your computer. Okay.
Offer building. I would love to see some of the raw content of you building offers,
targeting specific audiences, how you go about research, how you choose which information to
trust and build the offer of. Yes. Yeah. So I'm going to give you no bullshit, no fluff advice
in terms of that. I use something called mental models. And let me know if you guys want to include
this in the community. Basically I have a document where I used to read every single day religiously
of a couple mental models that are weird. Like, let me just fix this first. Always raw.
Mental models are always like a way of thinking that help you simplify complex problems and make
decisions faster. It's kind of like shortcuts for your brain to navigate tricky situations.
So think of them as tools that help you understand how things work without needing to know all of
the details. So for example, there's something called simple thinking. So the way I've managed
to come up with that offer, and I know it's a pretty smart offer of basically representing
your candidates in front of their ideal candidates. So this mental model that I apply is called
your big things down to the most basic truth and then build up your solution. It's like asking,
what's the real problem? How can I fix this without all the extra stuff? Right. So think about it.
Just think about it logically. Like a recruitment company, like, what do they actually do? Like,
they have candidates, right? If you just give us about like two minutes, you're like, okay,
they have candidates. So who needs candidates? Who needs job roles?
Who needs sales persons? Probably people or like companies, founders that are hiring for sales,
just because that means that's a signal for growth. Right. What does that make sense?
Let me know if you guys want those mental models and just include them.
It's like, because once you understand that, you can apply anything to those mental models,
like you can apply them to anything really, like, and they're really going to help you,
agency. So there's like, so first thing, when I first or like learn about these mental models
is from the book, Charlie's Almanac, I would really recommend you guys use like read that.
Right. One of the best books I've read in my entire life. Yeah, it's called principle thinking. Yeah.
First principle thinking, there's a law of senses of dependence of initial conditions. Now,
if you want to sound smart, you want to say this, if you want to just be normal,
just say it's drift. Basically, if you apply time, right, to a cause and effect relationship,
the smallest marginal change is going to have the longest, the largest impact over the longer,
which means if you're given the blueprint from the start, there's also exponential rather than
trying to figure out on yourself. Does that make sense? Just think about it like, like a ship
that's going right into the ocean. If it's going for like, I don't know, six months,
even the slightest drift,
it's going to give you, it's going to probably get you to Australia.
Australia is just an example. Yeah, it's called poor Charlie's Almanac. And then there's
there was, so you only need to know like a couple, yeah, a couple, you know, mental models.
And then you start, you start to basically figure out like solutions for your problems
on yourself without hiring anyone, right? So maybe we can add like a book list. I don't
know if you guys want to do that, just because I would really encourage you guys do that.
Let me give you guys like a secret. Most people that, you know, are really good
at, they really, they really like to digest information. They really do.
Trust me. Most people, you know, they do. And they just recompile it into a digestible way
for you as a YouTube channel or as a YouTube content, you know?
So learn from the basics. I would encourage you guys to read books.
And then you would basically start to develop this mental model. It's called, I forgot
the word. Yeah, basically you start to compile like a bunch of
mental models that you're like, okay, once you encounter like a problem, immediately,
immediately, unconsciously, you're going to notice patterns in your brain.
And it's going to enhance your problem-solving skills. Maybe we can add that. Let me know, guys.
Okay. We have another really good question. Okay, first, Hamza. Thanks. My personalization
seems very, you're not getting results. We'd love to review that. Yeah, sure. Send me the
blueprints and I'll help you out. Nine out of 10 times is going to be basically,
you're given more examples to the prompts. So you're going to put in the prompts. You're going to
include two to three examples called the three-shot method. And then the AI is going to be more
accurate. Right? Okay. Yvonne said, this is a really good question, guys. They're really good.
And Yvonne has been crushing it with their campaigns, guys. Like he's really, he has 0.8%
reply rates. 31 interested leads in three weeks. 31 interested leads. What's like that? Look at
this. I don't get 31 interested leads in three weeks. I currently have one business, mortgage
brokerage, on a 2000 monthly retainer, and I'm delivering amazing results for them.
My goal was to use these results as testimonials and get more clients. Great idea. I went on three
sales calls, but the price was the objection of all of them. Yeah. I'm thinking of switching to
setup plus paper meaning booked instead of a retainer. Okay. I'm thinking of charging $500
or $1,000 for a setup, but I don't know how much should I charge per appointment. It also
differs on the niche I'm serving. How much would you charge a client who has a PPC agency per
booked appointment? Average retainer is $1,600. How much per appointment for mortgage brokerage,
PTP affiliate deal, that value of close deal sex? Okay. Okay. Would you even bother even
working with a business selling a service that costs $200? Yeah, that's quite tough.
Well, admit we're not a good fit from this chart. Okay. Also, big question. How would you get paid
for results on Upwork? Oh, man. Is it weekly milestones, bonuses, or something else? Okay.
Let me just tackle this one by one. In terms of the average retainer, $1,600, that suggests
probably 200 to 300 per booked appointments. My rationale behind this is if an appointment
just converts 15 to 20% of the client's customer acquisition cost as well within the
acceptable margins given the retainer value. If they even basically convert 15 to 20%,
you're pretty good. It's up to 300 per booked appointment. It makes sense with the
average retainer, which is $1,600. In terms of the mortgage brokerage man,
I would also suggest a little bit higher because the close is like 12K. I'd suggest between 500,
600, maybe 400 if you want to go a little bit down. My rationale behind this is let's assume
there's a conversion rate of 10 to 20%. Each appointment can generate several times
$1,000. So charging this is still a fraction of that potential revenue. I would still include
more. I would still go for 700. So 400 or 500 to 700 per booked meeting. In terms of the
next question you have here, would you even bother working with businesses selling a service that
costs $200? Since I know you're really good at delivering, even if you booked many meetings,
the ROI for your client could be low. So I'd be more selective. If I were to pass on these deals,
it will clearly define a model that works. For example, something like volume-based pricing.
So if they want high transaction volumes rather than forcing something that leaves both of you
and the client unsatisfied. Don't be afraid to say no to low ticket clients if it doesn't work
because in your specific case, you're really good at delivering. So there's no reason why
you should work with low value clients. Well, not low value, but clients that don't have the money.
Getting paid per results on Upwork. It's the most important thing. I make sure I put this on
Basically, most important thing is clear communication with the clients.
Because Upwork is kind of tricky. There are some shitty clients on Upwork, which is why I'm
not really a big fan of it. So what we recommend is you charge a one-time fee. You'd say,
hey, I'm going to show you what to say to them. I've just drafted a copy. So you charge
$1,000, maybe in your case, maybe $750, $800 for a campaign setup. And then you'd find a
booked appointment. So clearly example of like 15 to 30 minutes confirmed call with a decision
maker within a specific timeframe. See, this is what I mean by cleared. Define what is a
booked appointment. I'm going to put you in a call with a decision maker that is interested
in your current offer. And I'd get paid for that. And then you'd buy weekly milestones.
So we'd have milestones tied to delivering a set number of verified appointments,
each with X amount of weeks. So you'd say, hey, in two weeks, I can deliver this amount of
meetings. And then you would have basically a paper lead model. But the main thing is you need
to basically align a census. It's really important, guys. So assume you're going to hold on a call
with them, obviously. You're going to say, hey, good call. I can see there are many sale opportunities
down the line. Could you do the following? Send me a fixed price offer. So you would have the
fixed price. And then for each booked appointment, the charge will be the amount. And say, hey,
let me know if you'd work. We prefer this payment model for our ongoing work. Together,
pay per appointment as we process things. So this is what I would do. So make sure you align your
census from the get go and clear your communication. And don't start anything until they pay you the
offer and fee. Or at least, believe it or not, there's this thing called, they have to put
escrow. Oh, yeah, escrow. Yeah. So they have to put money in escrow.
OK. Let's go back.
Could you speak to AI models, please? Chad, GPT, Claude, DeepSeek, and Grok,
all of these are lingering on the horizon or here already. The current specific use case
and possibilities in our space are a bit unclear. I know we have to pay to play.
But it would be good to get a view as to roadmap and where we should be putting our
automation dollars. What I would recommend is to have, there's this new model that Nishal actually
posted about, which is Gemini AI. And the quality is absolutely amazing, guys. So I think it's even
better than Claude. And it's significantly cheaper. But what I would recommend is having two things,
having Claude and having Gemini. So because Claude and Gemini are really good at text-based
copywriting, in terms of generating high-quality personalization, etc. But in terms of complex
tasks such as cleaning company names, basically those really, really long prompts, like intensive
prompts, OpenAI is just the best. They have the biggest knowledge base installed in them.
Right? I like DeepSeek, but I'm a little bit skeptical about it.
Sometimes it puts really scary personalization, sometimes it does not. But what I would recommend,
put your money in, two things basically, two AI models. Gemini AI, which is the cheapest.
And then I would go for Claude. Claude is basically the best. If you see, whenever I even record
videos, I always use Claude, just because I like how it outputs the emails. Okay.
How could we explore how to add more value and possibly more booked calls
during one phase, or when the campaign is up and running for the client? Yeah,
that's a really good idea. Just give me an idea now. So this could be ensuring the website's
socials are even far optimized for the influx or traffic they receive from cold emails.
I'm currently working on updating my LinkedIn profile. Yes, I'm creating an offer specific lead
magnets for inbound calls from my ICP. While my emails warm up, this could also be beneficial
for us agency owners to get more exclusive to our offer. I get booked calls sporadically from
both my YouTube channel and LinkedIn. But no, I can get more with the right lead magnet slash
funnel. Yeah. Ironically, my SaaS beta launch in next month, fingers crossed, actually helps
create lead magnets for this exact... Oh, okay. I really want to try this. Maybe I'll be a customer
when this comes out. But I'll actually have to create this lead magnet to old fashioned way until
it's functional. Okay. I'm just wondering if you can do this on your journey during the 2021 period
before instantly warm up emails. So what you can do for me, I never had good results with...
I never had amazing results with LinkedIn outreach. I tried maybe a month or two,
but I just didn't like it. So I stick to what I would recommend. Since I think you have the
skills to do this. I mean, how about you guys do this? I think it's got work wonders. So we'd
include... So what you do is you set up basically a template, right? Basically like a template for
each client that you onboard, where you have basically like a notion doc of best practices.
And you could just do a little bit of research about this. There's multiple people that talk
about this on how to set up an optimized LinkedIn profile and an optimized phone.
You could say offer while the warm up is getting done. You'd say, hey, this is going to give you
like a lot of perceived value, right? Just like clients like those dopamine hits. When you say,
hey, you would get a ClickUp dashboard that auto-populates with interest of leads and a
notification to your Slack or email. They're like, whoa, this guy is actually like...
You know, he's going to like... Because no service provider actually does this, right?
So you'd say, hey, maybe in the next two weeks while the warm up is...
Our domains are being warmed up. Let's hop on a call and let's discuss how we can
basically optimize your LinkedIn profile. And this is going to help you a lot with two things.
First thing, it's going to help you with delivering booked meetings, just because
their LinkedIn is optimized, right? So their LinkedIn is optimized. And then another thing,
let me fix my camera, guys. So it's annoying since it has to stop after 30 minutes,
just because the limit of any camera is like 30 minutes. So I have to...
I'll see if you get the czar. I'll look for a better name for cameras. Just let me know.
Okay.
Okay.
Thank you very much.
Thank you very much.
Thank you very much.
Thank you very much.
Thank you very much.
Thank you very much.
Thank you very much.
Thank you very much.
Thank you very much.
Just because when you think about it, old applications means that they are still hiring
and they might need it more than newer ones.
I think they indicate stronger needs, but fresh new jobs might be more in mind, yes.
I would still expect companies to post more than a month, I'll still do it.
I still got pretty good results from that.
Having issues with qualified leads, yes.
I'm delivering on meetings, any experience handling this?
Oh man, that's a really tricky one.
So you're sending them the qualified leads and they're not closing anything?
And I'm assuming you have like a pay-per-close, right?
Yes, my apologies, I need to specify.
And men get notified as soon as someone posts about a job opening an end feed.
So you want to like scrape, you know, basically link the feeds?
Yeah, pay-per-close.
Yeah, man, you really need to align the incentives really hard on this one.
Yeah, I would get them on a call and say, hey, we should re-discuss the pricing structure, right?
Just because, is there a problem?
You're getting them pre-closed, it's not your fault that they are shits at ConvertiMove.
Yeah, I'd introduce like a new pricing strategy.
Yes, yeah, because you're just shooting yourself in the foot, man.
You're delivering so much value, but you're not getting paid for that.
But I guess, I mean, think about it.
I think, did they pay like an upfront fee?
Okay, how much did they pay?
Yeah, and then how much of it actually you've used to acquire clients?
Did you actually burn through that?
Okay, perfect.
I'd still sync up with them and I'd say, hey, you know, I wouldn't, you know, push too hard,
like 50% used.
Yeah, I'd say, yeah, I mean, I would really emphasize that cash collected from your initial
investment, you know, is being used to delivering pre-qualified decision-makers,
but seems like the conversion rate can, you know, requires a little bit more optimizations.
So how about we switch to a pay-per-lead model and I would get paid, right?
You know, per per-qualified lead, right?
Because, you know, you're delivering them book meetings, it's their fault that they're not closing.
Yeah, but in terms of the other question you said, like, I want to get notified as soon as
someone posts about a job opening the feed, not posting on LinkedIn.
I believe there's a scraper that does this, I'm not really sure.
Yeah, that's a trigger one.
I've never scraped LinkedIn feeds, but maybe there's, obviously, there are methods.
I'm just going to have to just deliver a decision and I'll let you know if I found something.
How quickly did you go from getting your first client, building a system to ensure a good client
experience and get to 10k a month?
So what I've done, I got my first clients.
I didn't interview them.
I said, hey, can you basically record a video?
Or like, let's just hop on a call.
Can I record this and just be honest?
What was your experience working with me?
Like, you know, was I responsive?
Like, what would you rate this, right?
And then I would have recorded a video and then I would use that really hard.
My next campaign, say, hey, can I send you a video testimonial
of X company saying this about our service?
It should help.
And they'd be like, okay, send.
And then I send them that video, right, of me talking to that person.
And I would just basically edit out the most compelling part
when they say good things about me.
Yes, we assume you don't get any, yeah, the same ways.
I'm pretty sure you're using an epiphy, right?
Oh, yeah.
So one method, man, let's just use this, the offsets.
So let me explain to you what offset means.
So this is one limit, right?
Hopefully this still works.
Yeah, and the data set is not okay.
So let's just say, okay, I'm going to send this video.
So let's assume you're achieving one record, right?
So you get in one record, right?
And now, okay, how do you not?
It's the beginning, right?
So you would put in two and you just put in one.
So one is the numbers of items to skip.
That make sense?
It's a really loud car.
Sorry, guys.
I meant another time, for example, you want to get more leads.
Oh, from a niche three weeks from now.
Yeah, so what I would recommend always, always, always track
your lead list URL, either scraped from Apollo or LinkedIn.
Now, obviously, sometimes you're going to get some duplicates,
but this is where instantly just takes care of it, right?
Just because no matter how targeted you are, or no matter how targeted we can be,
the data is just, you know, data.
Sometimes you're going to get duplicates.
But the thing that we want to emphasize is not having crazy amount of duplicates.
So if you have like maybe 50 or 60, I wouldn't worry too much about it
because you're scraping, let's say, two, three thousand.
So I would always, always, always make sure I track the search URL, right?
In the Apollo.
And then that's pretty much it.
That's what I still do.
And sometimes you're still going to get duplicates.
Don't get me wrong.
Yeah, so I add them to Google Sheets and go to data and remove the duplicates.
I do this before I have to snare.
So I think these are all the questions we have, guys, right?
And yeah, I think that's pretty much it.
We had 75 minutes.
So let me know if you guys like this type of, you know,
this type of, you know, lectures.
So maybe have like a weekly one where we basically come up
and every week we tackle something new.
Right.
Maybe go in details, systems.
I know what he has.
All right, everyone.
I'm looking forward to the...
Yeah, the great interactive session is the best.
Yeah, I don't like when people like do this, you know, this suit and tie, you know.
I mean, we're all the same here, right?
We all want to like grow and make money, et cetera.
It's the entire why we just built this community.
All right, perfect, guys.
Awesome.
So anyways, guys, we have to do this today.
Maybe just all of us gather.
It's like a hunter gathering.
And then we just tackle one thing.
So I'm excited for the next week.
And then obviously, two days before the lecture,
I'm just going to post and just post any questions
and then I'm going to answer them.
Okay.
What about the super duper spread?
Include a couple of things in the classroom.
Let me know if you guys want, you know, specific things, right?
Like things that you guys want to see.
Maybe we can build like a project management template and click up.
That's just for us.
I was planning on selling this hands-off,
but what I'm going to do, just because I'm pretty honest,
I'm going to have a template for us, everyone in the community, right?
It's going to be for everyone here.
Just because everyone in the community, you know,
everyone is going to get consistent, you know, templates, prompts, et cetera.
Whatever I'm doing in my own agency,
just because I like always being in the agency space.
Yes.
Because you want to practice what you preach, right?
How to calculate the important metrics.
Yeah.
I would always look at the reply rates, right?
I would always look at the reply rates.
If it's within KPI, which is 2%, right?
Then look at the positive reply rates.
And then I'll start reverse engineer from the end.
I think we should add a video about this in the classroom
now that I'm thinking about it.
How to calculate and view metrics.
Sure.
Let me just write this in my notes.
All right.
All right, guys.
Thanks so much for coming today.
I'll see you guys in the next week.
And then obviously.
And for you guys' evenings.
Yeah, there's a getting dark here.
All right, everyone.
See you guys next week.
And cheers.
Onyc.com
Onyc.com
Onyc.com
