recording in progress
hello
hey <PERSON>, hey <PERSON><PERSON><PERSON>, hey <PERSON><PERSON>
what's up man, how you doing, what's up <PERSON>
hey <PERSON><PERSON>, hey <PERSON>, amazing questions man
<PERSON>, the note takers
good to see you guys
how you guys doing
hey <PERSON>, so let me just admit everyone is going to come
and wait a little bit for them
hello everyone
yeah I can hear you, what's up man, how you doing
yeah I'm doing great but my campaigns are, as you know, not great
AI agents, what's up man
let me just admit <PERSON><PERSON>, how you doing my friend
<PERSON><PERSON> has been crushing it with his campaigns lately
we have a packed house today
hey <PERSON>
yeah
and we also have new members actually, hey <PERSON><PERSON>
hey <PERSON>, how you doing my friend, good to see you
of course, of course, we have really good questions
and we have a variety of good questions actually, like sales questions, make.com questions, mindset questions
alright guys, so I'm just going to go ahead and share my screen, let me know if you guys
you guys can hear me well, perfect
okay awesome
so I'm just going to go ahead and share my screen, I'm just going to get started
perfect, can you guys see my screen
perfect, awesome
alright, let's dive in
so, the first question we have from our lovely friend Zach
what is the reasoning behind the high intensity sending schedule
and benefits over just sending a set amount of each day
yeah I'm going to explain this to you in very very simple terms
I think it's going to be basically very very easy for everyone here
to understand basically the reasoning and the thought process of me
going for the high intensity sending and why I teach it
so the second question, what is the reasoning for prioritizing new leads over sending scheduled follow ups
is it right that I've been sending for a few weeks on my campaign now but only sent out email one and not the rest of the sequence
is it right to leave such a large gap in between emails
I've been getting this issue with my company name cleaning personalization
where company name includes a return in the end
meaning that it starts a new line, yes yes
you know my friend, it took me a few hours to fix this
the first time when I encountered this issue
so I'm going to give you step by step
after company name, see screen shot, yes I can see
if you guys can see the screen here
the company name just outputs like a new line here
which is very annoying with AI models
but fortunately there is like a very simple fix
I think I'm using the recommended prompt without any changes
see screen shots, what's the issue here, how do I get access to a client CRM
so I've been taking some notes here and it's going to be a value packed session
okay so first of all, what is the reason behind the high intensity sending
and benefits over just sending a set amount of each day
so I'm just going to give you like an analogy
so imagine your inbox is a person at the gym
so day one, or let's say you go to the gym
day one you do 20 push ups, no big deal
you feel fine, okay
anyone can feel fine when they do 20 push ups
the first day they go to the gym
now day two, they do 40 push ups
still feeling strong, right
they just increased 20 push ups
now day three you say, let's shell today
take a break and stretch out
and day four, now we're back at it
now we can do more push ups without getting hurt
well that's exactly your sending schedule
you push a bit, then rest, then push more to avoid injury
aka spam flags
so my thought process when I came up with the high intensity sending
is just that I wanted to leverage speed and safety
so when you send out 20 emails per inbox per day
let's say you have like 15 emails
let's say you have 15 email inboxes
and you're sending 20 emails per inbox, right
so you are at 300 emails per day
next day you bump that to 40
now you're sending 600
and you're doubling your output fast
so the idea here is you want to remove all that outreach lag
because if you guys don't know, there's something called outreach lag
so when you send out an email
typically it takes a little bit of time
just because people need to see the email
they need to click on the email
it takes a little bit of time
so it's called outreach lag
so what we're doing here is we're sending let's say 300 emails today
the next day we're increasing our volume
just to remove all that outreach lag
and remove that gap from people to reply to us
and then we're resting the third day
and we're essentially just letting all the email inboxes warm
so this is kind of like the thought process
you want to send at a high speed
but still be in a safe spot
so this is essentially what I've done
so I send like 300 emails today
I increase it like crazy the second day
and I just rest and let the inboxes rest
so that way I'm not sending like
because think about it
you're not sending 600 emails every single day
so the total will be a little bit higher
but still you're in a safe spot
so does that make sense Zach?
yeah
so this is why I call it the high intensity sending
you send 300 emails today
the next day you ramp up your email sending
and then the third day you rest
so in that rest day you're not sending anything
you're just letting the inboxes rest
and then you're making sure the reply rate is 100%
that way all of the warm up emails that instantly send
your email inboxes is going to reply to them
so it's 100% reply rate
now if you want to send 40 emails from the start
inboxes are sensitive, we all know this
so think about it like another analogy
think about it like gym newbies
if you push too hard in day one you risk injury
aka getting flagged and spam listed
but if you do a smart warm up
then push harder the next day
you're more likely to stay healthy and still grow output
so the idea here is just want to maximize our output
while still minimizing the risk of getting spam
so again if you want to send 20 emails per day
it's safe but it's slow
you'll take weeks to hit serious numbers
that's cool if you got no rush
but if you're building like a pipeline
and you want to test out messaging first
you want to get a client as fast as possible
I would 100% go for the high intensity sending
because I can offset that later on with more inboxes
because I want to get up and running as quickly as possible
I want to get my first client as quickly as possible
I want to get the first clients
I want to get the clients as quickly as possible
so this is why I can offset that later on with more inboxes
and it's also good because you can test different offers quickly
and interfere with the market so you get more feedback
and you can iterate quickly
and you can basically finish a campaign
like 5,000 leads in like two weeks
so it's like the high intensity method
so it's like the high intensity method
yeah probably
there's like a video specifically in the called out mount masterclass
where I explain the high intensity sending
it's called I believe it's called lunch in your campaigns
it's called I believe it's called lunch in your campaigns
so this is kind of like my thought process
of the high intensity sending
and your second question is
and your second question is
what is the reason for prioritizing new leads
over sending scheduled follow up
is it right what I've been sending
for a few weeks on my campaign now
but it's not on the email one
the rest on SQL
yeah so
good thing that
instantly is built in prioritization
it's set to prioritize follow ups
over new leads
so even if it's prioritize new leads
over sending scheduled follow ups
instantly right
sends in emails
in a certain order each day
so let's say like instantly wakes up
instantly wakes up and has let's say
40 emails to send per inbox
it goes who needs follow up today
so we're gonna send them that email
and then so the first
the first people we're gonna send them the email are gonna be the follow up leads
and then if there's any
room left we're gonna send
email one to the new leads with the leftover
slots right
so even if you make sure
even if you toggle prioritize
new leads instantly
is still gonna send the follow ups
which is why I like instantly
now if you wanna use smart lead
or I think lem list or something
they don't have
this built in feature
that checks if there are any follow ups
in the campaigns
and then sends the emails
and then if there's room left
then it sends the
emails for the
then this is the initial email for the new leads
so make sure you always prioritize new leads
over sending schedules follow ups
always make sure you toggle
this option
no worries man
so now let's fix the issue with company name cleaning
personalization so I'm just gonna go to make
and let's say we have
for example this is like an apify data set
so I'm just gonna put one
and I'll click on save
and I'll just retrieve the data
for example I have a company name
this long name
now let's just use the prompt that we have
company name formatter
and then just paste it here
so normalize the company name
so I'm just gonna put the company name
and I believe
that we have the domain here
so we don't have the domain, that's okay
so by focusing
company
well let's just put
focus on the most distinctive memorable element
let's delete everything
the prompt is still gonna work
because we don't have the domain in this case
so if I click on save
and let's say this is like
my dream email
I'm gonna put tools, sets, variable
and it's gonna be example
and let's say noticed
the response
of the company name, okay
now let's just see
it's still cleaned
right?
but let's say hey
for example Zach
notice company name
is higher end
for an SDR
we can connect you
with three to four ideal clients
purely on performance
will that be helpful for
company name
so if I run once
I believe we're gonna have a space
yeah
so you see this
little right here
so this is the issue here
this space
you see it?
so the AI models
sometimes would include
an additional space
so the simple fix is
just edit the prompt and say
output the finalized result
in the correct
JSON
as
result
and now
you're not gonna have that issue
so only add like a parseJSON
here, so parse
parseJSON
and just map
the string here
okay
run once
now you won't have any issue
there's no space, you see?
there's no space
now we can directly
map it
so the simple fix
is just to turn it into JSON
and parse it and you can add it
that's the only way
to fix it
and you don't have to deal with AI models
like Gemini or
Perplexity
or even like Cloud to give you all these
new lines and all these spaces
so now we can just map
the results here and click on run once
and now you're not gonna have that issue here
see?
it's way different
so now it's perfectly formatted
so always just convert that
into JSON
and then just map the results from JSON
does that make sense?
yeah, pretty easy, right?
trust me, it took me a few hours
to find this out
the first time when I had this issue
okay
so how do I get access
to a client's CRM?
yes, okay
so I've been taking notes here
so ideally
or what I would recommend you do
and this is essentially what I do
is during the kickoff call
you'd say, hey, so for me to sync everything properly
with your CRM
like logging replies, updating lead statuses
or pushing qualified leads
to the API access
super quick on your end
so depending on what CRM they're using
maybe they're using like ClickUp or
Airtable or like PipeDrive or something
yeah, for sure, man
yeah, yeah, 100%, let me see
so it looks like
there we go
yeah, nowhere is my friend
yeah, so here's what to do
so during the kickoff call
you would say, hey, for me to sync everything properly
with your CRM
like logging replies, updating lead statuses
or pushing qualified leads
I'll just need your API access
super quick on your end
so depending on what CRM
like I said they're using
they're either going to share with you the API key
or they can just invite you
to their developer portal
just using your email
and then you give them like two options
so if they're using PipeDrive
at API level or like Monday.com
you'd say, hey, perfect, once we're up at the call
I'll shoot you a short message with exactly
what I need and where to find it
so this way, even if you don't understand
how to connect the APIs, because truthfully
like these APIs just change every single day
it buys you a little bit of time
to go ahead and read through the documentation
and then you just send them like
a step by step, you can even use like chat GPT
right, you'd put in the link
of the API documentation, you'd say, hey
find me the best way or like
the most straightforward way
to link
the APIs or use the APIs of
whatever CRM they're using
and then you just send them like, let's say you're
using Slack, right, to
communicate with them, or like
using email, well I would recommend you use Slack
it's way better than email, okay
and then you just send them like a text message
you'd say, hey, here is
the step by step on how to actually
connect your CRM
to my API, and then you just
basically just integrate it
and then you're good to go, okay
now the reason why I always say
just shoot them a message instead
of trying to figure that, just because sometimes
that might be using, I don't know
like a CRM that you don't know how to use
okay, so that way
you do a little bit
of research and you just give them the step by step
okay, so you're explaining the why
so it's not just a random task
okay, and you're making it
sound super simple, and you're positioning it
as a standard, not optional or negotiable
okay, and it also gives you that
it implies intelligence
it just frames you as that growth partner, not some
random lead generations
like lead generation provider
or like lead generation service provider
okay, because it's super super
important, okay
the next
question, do you use sub-sequences
to automate follow ups
I've tried the
sub-sequences instantly
but I just don't like them
man, just because I much
prefer have a make.com
flow that watches any instantly
replies, and then I can just
use AI to reply to them, right
I find that having like
a generic sub-sequence
sometimes
like, it would
be like a reply that's
that
you can see
that
sub-sequences that I'm using
is not specified for that entire offer
that this client is trying to reply
with, you know, so whenever I'm using
the
AI inbox manager, it's way
better, right, because AI can read through
the entire email and reply accordingly
and if you're using the
AI inbox manager that we just put in the community
it's 100%
superhuman, right
I think, I believe it's Junior
he managed to book a call
just using the AI inbox manager
so I wouldn't use it, right
just because, like I said
the real juice is using
AI in this flow, right
because AI can output like
crazy responses, especially if you're using
cloud, right, and sub-sequences
are kind of rigid, right, it's hard
to customize based on reply type
and time zone and interest, right
so, again, if you really
want to use it
what I would recommend you do is have
like a basic reply
so I used to run this offer
whenever like a lead
replies, there's an automated message using
the sub-sequences and it's like
hey, thanks so much for getting back to me
you'll have the loom
explaining what I do in my current offer
in like a few hours
right
so that's even better
so that can be standardized
because any lead
is going to be like, yeah, sure, send me the loom
so I hope this makes sense
yeah, good luck, Ibra
okay, so the next question
how do we
book the meetings for our clients
do we get access to their Google calendar
and stuff when charging based on a
meeting set, do I charge the clients
immediately for the meeting or after a few
meetings or whatever we agree on
should I send looms to my clients
prospects to increase the likelihood of them
scheduling a call, or is the
offer supposed to be good enough
so the way you're going to book meetings
for your clients is just to basically
make a duplicate of
their calendly or call.com, okay
so just make a duplicate
and then use that to schedule
the interested leads in their campaign
okay, so this is the best way to do it
just because you want to track
the booked meetings from the campaigns
if you use their initial
calendar link, now we don't know if it's
a lead that came from the
campaign, or just like a warm lead
or like an inbound lead, right, so make a duplicate
and
you'd want to discuss this during the onboarding
call, you'd say, hey, it's going to take us 5 to 10 minutes
let's just duplicate your calendar
if you don't have a
booking tool, and
99% of people
have a booking tool like
calendlycall.com, especially if you're working with B2B
unless you're working with real estate agents
they like
just phone calls instead of having
zoom meetings, but I'm assuming you're working with
B2B, right
so the only thing I would
do is just create a duplicate
of that event, okay
and then just use that to schedule
the calls with whatever
lead that was interested
in that campaign, okay
so when charging based on
meeting sets, do I charge the
client immediately for the meeting or after a few
meetings or whatever we agree on
it really depends on
how you want to do it
but the way I used to do it
and have always done it
is I would always just charge
them for the total
so I would get them the 5 booked meetings
or whatever we agreed on, and I'd just
send them an invoice for the total
of the entire meetings
I think that way is better, instead of
charging them for each
booked meeting, you just charge them for the entire
total
so this is
what I would recommend, again, depends on
what you agree on with the client, and
what you prefer
if you want to charge them
as quickly as possible, so whenever
you book a meeting, you just
basically have either
a Stripe notification or they
can add you as a viewer
in their Stripe dashboard
or you can just send them an invoice
the easiest way is just send them an invoice and they have to
pay it using Stripe, quick and easy
or you can just charge them for the
entire booked meetings, so whatever you
agree on with the client and whatever
you prefer, okay
should I send looms to my
client's prospects to increase the likelihood
of them scheduling a call, or is the offer
supposed to be good enough? Yeah, I would highly
recommend you use looms, man, for
a variety of reasons, okay
just because, when
you are using looms
like, leads can actually
see you, okay, so leads can actually
see you, so it builds trust
it also builds reports
and now, you're not just someone like a random
person who's just emailing them, okay
so definitely looms increase
show up rates, they increase conversion
rates, so let's
but my recommendation here
is never send the loom
like, in your initial email
so, let's say in your initial copy you would
say, hey, is this a line we're
trying to achieve right now, if so I can just send
over a loom
and it would be basically an overview of what we do
right, and
if they reply, then you'd send them a loom
so that way you're not tanking your deliverability
and you're still squeezing
as much juice from the looms, okay
okay, so
we have a really good question from our friend
Mehdi, he said, how can I overcome
yes, of course
I believe there's an
entire thread on this
by this guy
called Jesse, right, I'm going to link
you the thread after the call
so it's essentially
just like 2-3 minutes
of you going, just going, like
giving like a little overview, obviously
you do not want to like blow everything, you know
you just want to give a little overview
because you want to spark curiosity
so they can get on a call, right
quick overview, just going over
like, basically the basics, etc
what we do, and then
it would be booking a call after that
okay, how can I overcome the fear
of getting into sales calls, oof
that's a big question
because I'm a non-native English speaker
the fear of getting stuck
at some point in the call, not knowing what to say
or just showing, worrying my tongue
will fail me, yes
I know that the first experience is not always the best
but I haven't been using my language
much, also does the language
affect your credibility or your lead
leads pers- pers- pers-
perception of you, perception of you
or is it just your message, understanding, clarity
that matters most, so you essentially
answered the question, my friend, here
but, let me give you
actionable steps, okay
so I've been taking some notes
on this
and let me just walk you
through it
so
what you need to understand
that
sales calls or
you know, sales systems, etc
you need to
have a mindset shift, okay
and I always say
no better is better than you
there's just been doing it longer
so, the fear
of sales calls, etc
I used to have that too, but it's literally just
reps, that's it
that's it, it's just reps, okay
so, what helped me
early on
is I would make conversations with myself, right
so, as silly
as it sounds
I want you to talk to yourself out loud
I want you to make a phone call, right
so, I used to do this like every morning
like 5 to 10 minutes, and I would just
basically imagine myself on a sales call
what I would say
it simply wires your brain
to feel smooth when it's real
just because your brain doesn't understand if it's
illusion or reality
so, I would always
always recommend you practice your sales calls
do that like every single day
for like 30 days
and you're gonna get better, okay
so, the best way to just overcome
the fear of sales calls
is just literally hopping on more sales calls
right, so if you wanna ride a bike
and you wanna get
good at riding the bike
you wanna ride the bike every single day
you know, Michael Phillips
the greatest swimmer
you know
internationally known
what made him the best swimmer
literally going to the pool every single day
you know, so you need to
get your hands dirty
and literally get in reps every single day
and you'll be great at it, okay
and one mindset shift
that also helped me
is the pain of you
not achieving what you wanna achieve
has to be
like, it has to be more painful
than the fear of rejection
or the fear in the sales call
once you understand this, everything
just fades away, you know
because when you see the Stripe invoice
and you see literally clients paying you
now we wanna hop on a call
right, that fear just disappears
because there's one thing
that most humans are passionate about
and it is winning, okay
so once you see
the couple wins that you're gonna get
from your sales calls
and literally clients paying you
that fear is gonna fade away
trust me
so just put in the reps every single day
right, and make sure to
just try to have
like a couple conversations, try to
basically, if you don't have a script
just use the script that we have in the community, right
and then
just put in the reps, right
so what I used to do
I used to basically just
every single morning like I said, I would just
try to imagine myself in a sales call
and I would say, hey Peter, yeah great to meet you
so before we jump in, can I ask you a quick question
what made you jump on a call with me
you know, I try to
play that in my brain, in my head
and once it's
once I'm in a sales call, right
those nerves will calm down, okay
again, like I said, you can prep
the hell out of it
have a simple script, write down your openings
few questions and you close
not word for word, but just bullet points
I would still do this, right
I would have like a paper
and I would basically have like a couple bullet points
and trust me, all these speakers
like even TED speakers
they have bullet points, right
before they
before they go into the stage and speak
you know
also, practice out loud
I would also want you to record yourself
on your phone or like a Zoom practice
and you will get used
to how you sound
okay
another thing
nobody speaks perfect English
even native speakers fumble, okay
so just keep that in mind
and
clients care only about your confidence
listening skills and the value you bring
so obviously, whenever you are in a sales call
you don't want to be the person who actually does the talking
you know, you just want to ask questions
come from a consultative point of view
and clients are going to
answer your questions, okay
and once they are
answering your questions, you just listen, okay
and even if you make a mistake
it's okay, let's say you stumble
just laugh it off and say
hey, let me rephrase that, okay
that's totally cool, trust me
many times I've blown so
many sales calls, okay
so
TLDR, it's literally
just reps and
just have a little script
couple bullet points
that you're going to look over whenever you are on a sales call
obviously, do not copy
word for word
and practice, you know, practice out loud
record yourself on your phone or like a zoom practice
okay
and then you'll be fine, okay
but the main thing is reps
that's the only thing
that's going to move the needle, okay
doing more
okay, so I hope this helps, man
okay, so
Hesad, I have quickly
realized when building discriminant
personalization systems, it can be quite
operationally intensive on MIG.com
as is normal, and is there something
that can be done about it
also, could you please do a video on how to do
batches of leads at a time
so that the scenario doesn't go for too long
or time out some of the modules
yeah, yeah
no worries my brother
no worries my friend
but I think, see the thing is, I know
if I talk to you, I know you have a good
English, you just have that, you know, that
limiting belief, you know
it's just the limiting belief
trust me
and like I said, like, you don't have
to be passionate about sales calls
right, because I want
you to be passionate about what makes you money
because all humans
are passionate about one thing which is winning
period, you know
so once you
start getting some reps in
hop on a couple sales calls
once you see
a couple clients coming in
you see the money coming in
you're going to start to love
sales calls
okay, so
it can be
quite operationally intensive on MIG
is this normal, is there something that can be done about it
yeah man, it's totally normal
just because think about it, you're personalizing
an entire campaign
you can never do that manually
just because there's multiple modules
like company name cleaner
you're literally like
scraping the web, you're using rapid
APIs, you're using like
multiple APIs at the same time
you're doing like what an SDR would
take like a few weeks, like a month
you know, so
obviously it's going to be
operationally intensive on MIG
but you can use NA 10
right, and obviously a lot of
people, I know a lot of people love
NA 10, and here's the person who loves
NA 10 the most, Sammy
so, yeah
my thought process here
is split the scenarios
so think about it
is there anything that can take you
2-3 minutes
right
if it's going to take you 2-3 minutes to just export
a lead list and put it in like a
email enrichment platform
and just export it
I think it's better to run the entire scenario
and use like their API
in my opinion
if you really are worried about make
operations, you know
if it's going to take you like 30 seconds
just export, put it in Google Sheets
and put it in like
an email validation tool like
mails.so or something
and it's going to take you like 5 minutes
to export it again, now you've
split the system in half, now we can just run
the personalization, okay
and if you don't want to deal with
rate limits with instantly
here's what you can do, you can just run
the personalization and use
add row and just update the row
and make, and then you can just
download it at CSV and just upload
it instantly, okay
so when it makes sense
to automate, you can automate
but if it's just going to take you 2-3 minutes
right, and it's going to save you
like let's say a couple thousand operations
I would just go for
the manual way, you know
the cool part is
Sami is building the
dashboard for
the email finder tool, so now
you guys can just upload the
lead list as a CSV instead of
just using the API
which is way better, you know
exactly, and
about yesterday I was working
in the code, that's why you had
some errors and everything
yeah, well I mean
people in the community were like sending
80,000 requests in a second
which was crazy, so we had to basically
change the server and transfer that
in a bigger server, right?
and if someone is sharing
the IP, now I
share the IP
I'm tracking the IPs guys
yeah, that's the thing
so it looks like a couple
I believe people who came
to the community and then they
just got the API key
and for me like I
I'm not taking notes
of the actual members
because there's new people that come in every single day
and there's a new API
key that gets generated
some of them maybe shared an API
and this is why
we had
that issue
so someone maybe would have been using it
or maybe someone shared it
but now we fixed that issue
now all the API keys
were updated
so the best way to do it is just go to
find your API key and just put in the
email account of your school community
associated with your school community
and then you're going to get your unique API key
are there any
limits on using the email? No, there are actually
no limits
that's why it makes SSM the best
you know?
there are no limits
but obviously we don't have to go crazy
you know?
yeah
okay, currently waiting for my inboxes
this is my friend Pablo
he said, currently waiting for
my inboxes to warm up
on instantly, what's the best way
to be spending the time as a
wait? Example setting up automations
yes, getting CRM set up, website
done, okay, proposal is automated
start scraping for leads etc
so what I would recommend
you do, always have your CRM ready
contrary to popular belief
I'm not going to say
don't have a CRM
because what if the campaign
does well and now you have
no idea where to store all these leads
so have your CRM
ready, just have something very very simplified
you know? because you don't want to like
go crazy with it
my recommendation would be just using ClickUp
ClickUp is just easy and straight forward
again you can use whatever CRM you want
a website
done, okay great
and you'd also have the
a couple automations that we have in the
classroom
which is essentially, whenever
you finish a call, let's say
a sales call, you can just update your CRM
to like an email
and then it just triggers a
email sent to that person who you
just had a call with
saying hey thanks so much for the call
I'll send over
like a proposal or contract
in like a few hours
right? so you can just
click one button
and then you can just send them that email
which is thank you so much for the call
you know I had
I really liked
going
deep dive
going on and understanding your business
etc. so you would
essentially just, you can even
like use AI to do this, right?
you could say hey thanks so much for the call
and then you would say
for example, in a couple hours
I would send over like a contract
or proposal with whatever
you guys are discussed
during the sales call, right?
yes
but again you don't want to have like a super
cluttered CRM
because some CRMs
will have like a hundred
steps
you know you want to minimize the steps
in your CRM
right? so the CRM that we're building
is like, it has like just a couple
steps, you know? meeting booked
awaiting proposal
proposal sent and deal
closed, okay?
so you don't want to deal with tire
kickers, you know? and you also want to make sure
your CRM is pretty lean, you know?
just because you can identify
the bottlenecks pretty quickly
you can understand where
are you losing the leads, you know?
if you have like a hundred steps
it makes it even harder to spot
the bottlenecks, you know?
what would your daily work routine
look like as a total beginner
to maximize momentum? in other words, what would you be doing
every day just sending monitoring campaigns?
so what I would recommend
and basically my routine from
zero dollars all the way to 25k
is essentially the highest RY
and basically the
highest leverage that you have
and what is the highest leverage
task that you could be doing?
following up with the people
scraping more leads and building out more campaigns
that's literally all you have to do
that's the highest RY thing you have to do
just because if you're scraping
if you're constantly scraping more leads
enriching data and constantly
having a campaign running, right?
that means your pipeline is always
filled with new sales calls
because no sales calls, no deals
no deals, no money, right?
so the highest leverage is outreach
every single day I would do
like it would be just outreach
and at the end of the day I would just
handle fulfillment. I always say this
fulfillment always comes later
and outreach
like the first thing you have to do
every single morning would be outreach
following up with people, right?
if someone just has the slightest
interest to reply
reply to them quickly and then you keep following
up with them until they book a call with you, right?
because this is essentially what moves the needle, right?
the only thing you have to be doing is sales calls
sales calls
making sure your copy
targeting is on point, personalization, etc
scraping more leads and adding more volume
and then at the end of the day
you can worry about fulfillment
and fulfilling new clients, okay?
just because you want to have your pipeline always filled
with new clients coming in
don't do the mistake of having
your first client and then putting all
your time and energy in fulfilling
because that's how you lose momentum
you know?
I've made this
mistake before where I would just get
a couple clients and I would just stop my outreach
and now I've lost my
momentum and I forgot that
my outreach is what
got me the client
in the first place, you know?
so your outreach is what got you the client
so you need to keep
going, you need to keep sending emails
what was your best
performing campaign?
so the best performing campaign I believe
was with
this company is called
Kinect Group, which is a recruitment company
I believe we had
like 22%
reply rates and it was like
at this point it's like a year ago
22% reply rates and we've managed to
place like 22 candidates
in like 12 weeks
and they've made more than I did
they've made like 105k in like a quarter
just from placement
how many
inboxes do you recommend?
we start with as many as we can
afford, I have three at the
moment, be open to purchasing more if it means
I can move faster, yes
obviously if you can afford
as many email accounts as
possible I would definitely go with that route
just because you can now send more
and if you send more, you get more
replies and if you get just
2% reply rates
let's say you send like 2000 emails
and you get 2% reply rates
even if like, let's say you get 40 replies
obviously
not all the 40 replies are going to be positive
but let's assume just
15 were positive
oh my best
outreach campaign, yes, okay
so my best outreach campaign is definitely
companies hiring for SDRs, yes
yeah
because you know
nobody is actually
targeting companies hiring for SDRs, this is a cool
angle, it's a pain sniffing tactic
you know just because when a company
is hiring for a sales rep
that means they want
more sales, as simple as that
you know, or like BDRs
marketing manager
yeah, it's companies hiring for
companies hiring for sales
I've always had better
yeah, I've always had, now that I'm
thinking about it, I've always had better
responses, better
results in general with
companies hiring for sales
but
I don't want you to hear this
and say well, I'm only going to be
targeting companies hiring for sales
I also had campaigns where I was just targeting
random B2B niches
and I still got results, you know
hiring for sales was
the best campaign, you know
so I hope this helps, man
so in your case, man
I would definitely get
more than 15 email
inboxes, yeah, just because now we can
send more, I believe you already
understand
the entire system
and how it works, you know
so if you can send more
you'll get more replies, and let's say you
only get like 40 replies
and then like 15 replies were
positive, obviously you're not going to close
15, nobody can
unless there are Alex Hormozi
okay Axel, good luck
unless you are, you know
Alex Hormozi, like I said
but let's say you close just
3 from 15
and you pitch them like 2k
that's already like 6k, which is awesome
I think many people
would be happy with 6k as
you know, just 2000 emails
can we get a quick
list of all your automation that you use
just an overview, example Lead Finder to get clients
proposal automation, then actual
client fulfillment automations, let me know
if there's a video for this
so this is part of
the client fulfillment parts
in the classroom, so
people that basically get their
first client, they get access to that part of that
course, and the reason why
is because I wanted to
avoid overwhelm
for people who
haven't landed their first client yet, you know
so once someone gets their first client
they just send me a DM, and I just
give them access to the
to the client
it's called the
client fulfillment classroom
client fulfillment course
and it has
all these systems that I'm using
but, let me just give you
an overview, so
I don't currently use the
Lead Finder, I've used
it before, and it's
simply like a type form
and what I would do is just
write
the
first
type
right?
and
the system
is going to take in all of the
all of the data from
ApiFi using the URL, right?
just because it's going to run like an
actor in the back end
and then it would just auto populates
everything instantly, right?
in terms of
the second automation that I use
AI inbox manager like I would highly recommend you guys use it just because
it's I put a lot of time and energy in that in terms of like the prompt
engineer and etc into that and it just works you know just because we're
feeding Claude like the right information about like a cell systems
etc and the replies are absolutely crazy you know so the AI inbox manager the
lead finder I also use like a simple the one that I mentioned earlier the thank
you for the call right and in my CRM and I also include another thing which is
pre-meeting reminders so I have a reminder 40 hours before the call 24
hours before the call 2 hours before the call and 5 minutes before the call so
this is the this decreases like all the issues with show up you know it just
like all that friction from leads not showing up in the sales call okay so
always have pre-meeting reminders 48 hours you can even include like let's
say they booked like for next week you can have like a seven days before 48
hours before 24 hours before and five and two hours before and five minutes
before when I built this automation like my show up rate just increased
dramatically another thing that I do is I have like in the back end of the
automation I have like a make calm flow that checks if there are any new booked
meetings and then I have like a extremely personalized copy for that
lead where I also include in the email the meeting link so the only thing they
have to do is just click on that meeting link and they go straight to the
sales call you know so all the automation that I'm trying to build and
I'm using I lead in me to the end goal yes there are there is a video in the
classroom yes you know all the automation that I'm building I lead in me
to the end goal which is the sales call you know because there's the highest
leverage that I have you know
yeah no worries my friend I don't know why but the SSM email finder does not
work I believe now it works because I DM'd you so let me know if you still
have any issues so just check your DMs my friend and I just like a fix the
entire flow for you and now it's just working perfectly okay have you checked
out their stack for finding job postings yes I find job boards have a pretty poor
job posting to that email ratio unless you email multiple people from said
company also how do you do that what are some good campaign fulfillment ideas for
recruitment clients that work what is the what is the protocol on the
Microsoft delivery issue I don't think Microsoft inboxes is the sole answer
okay so let me give you step by step on how she do it so if you guys don't know
there's this platform called their stack and it's essentially an API that pulls
all the job listings from all the platforms that you guys know LinkedIn
indeed glass door dice etc and they have like a like a like a big API that just
pulls all of the these job listings into like a like a dashboard just kind of
like Apollo but the thing is with this is extremely expensive in terms of the
credits right so I know a couple people that are using it including Vishal so
for 1,000 company credits it's like a hundred bucks if you want 3,500 it's like
300 bucks right so it's quite expensive but the data is definitely better you
know the data is definitely better just because they pull data and they remove
duplicates for you okay so your question is here is have you checked their stack
yes but my idea here and if you want to skip their stack there's a couple
platforms and there's a couple epiphy scrapers that can do basically the same
thing and remove duplicates for you right and I would recommend you guys use
this one so this is a new scraper it's called LinkedIn job scraper remove
duplicate jobs so this one handles all of the duplicates okay so let's say
there's a company that are posting like multiple job listings right if you feed
in the search URL you're just gonna get one right because let's say for example
a company is hiring for sales and maybe they hire for an SDR and they have like
another job listing that they hire him for like a BDR or something or like a
marketing manager right now if you type in let's say SDR in the LinkedIn search
LinkedIn job search this scraper is only gonna retrieve the SDR from that company
okay so make sure you use this I'm just gonna copy this let me just put it here
so make sure to use this this is the one that I'm currently using and it just
removes all the duplicates okay what are some good campaign fulfillment ideas for
recruitment clients that work okay so I went ahead and just compiled some of the
winning campaigns that I had and what I used to do is I would use hiring signals
right so let's say you are working with recruitment clients typically they will
have a couple candidates in-house that they want to place so what you do is you
look for companies hiring for those exact roles and would be LinkedIn glass
door dice there there's also like a couple databases so for example let's
say you are working with like a health care recruitment company so there are a
couple databases if you just go to apify and you type in this is the hack for you
guys just type in like a keyword like health you're gonna find a couple
scrapers right of any offer that you currently offer okay any offer just type
in keywords for example help or like for example visa okay you can type in for
example startup so there are multiple scrapers okay there's YC well-found NDA
green there's Twitter there's there's even like Y combinator extractor which
is I've used this before you can scrape startups with this okay there's also
so my idea here is just type in like any keyword right and in this case you want
to target a new question do we need to ever pay for apify well obviously if we
want to scrape in volumes right but let's say if you want to scrape just a
couple thousands you can just create multiple accounts you know and I'm
pretty sure you're gonna do the latter yeah you can you can create yeah you can
create multiple accounts and if you want to bypass the IP like if let's say they
block you just use VPN or like just use incognito right or use a different
browser and now you got your golden you can scrape another like 20k 30k if you
really don't want to pay for epiphy but again if you're like scraping in volumes
you can just get I mean I would definitely just go for the paid plan
it's a way better just gonna remove all this issues of like accounts etc etc you
know you don't have to basically create and every like a like an account every
two days you know so I would basically scrape companies hiring for those exact
roles right and then my positioning would be hey saw your hiring for role
we just placed some someone similar at similar company in 14 days okay you can
also do something that I used to do I would have like a very very soft CTA and
I would say hey can I send you a quick win and it's basically a PDF or vetted
or vetted candidates so you talk with your clients and you'd say hey do you
have like a like a PDF or like an overview of the candidates you guys have
right and I can share it as a lead magnet okay so that way the lead can see
those candidates right and you can just send that to them after they reply you
say hey can I send over a list of pre-vetted candidates no strings attached
they will say yeah for sure it's just send it and then you can just send it to
them and you say hey would a quick call make sense okay and then you would push
for the call okay obviously you want to nurture if no response we would add like
weekly value case studies etc so let's say you send them the PDF and they let's
say they forgot to reply you'd add value by mentioning a case study and you keep
following up until they book a call you know you can also say can I send you
pre-vetted candidates by Friday you know and again you'd send the PDF so these
are all angles you can use when you have like a recruitment clients okay you can
also say just helped company fill the same role you're hiring for right and it
would be like just a variable would be like job title here okay just a
variable you know so I hope this helps man okay so the issues with Microsoft so
the best way to just tackle this is just to get emails off go daddy right just
get the you know just because you're getting him from Microsoft directly okay
and I would highly recommend you guys use this platform in boxology just because
they provide reliable Microsoft inboxes right I've been using it and I had no
issues okay they handle everything for you right so essentially you just pay
them and then like two hours they link everything to your instantly accounts
they enable warm-up they set up SPF D mark DKM etc okay and then you would get
up and running as quickly as possible and it's extremely cheap let me go to
pricing so there we go they include us send any IPs which is crucial so I would
just recommend you guys use in boxology or just get them let's say you don't
want to like deal with SPF DKM D mark just get them from inbox ology or if you
really want to pay for your own ones just get them straight off go daddy you
know but keep in mind when you get them off go daddy it's gonna be a bit
pricier so just get them from inbox ology okay I'm using this currently and
I believe another person to come here is called bizarre yes he's also using him
and he has no issues with deliverability okay so use inbox ology okay so I think
we have a couple more questions what a bit what is the best way to set up an
email scrape of a list like this Google Sheets run it through command center I'm
sure you have a workflow already done the community can you pin point me to
that one I'd like to use an attend if possible I'm having issues the command
center what should be the what should the NA 10 look like to scrape an email
from the command center okay okay so we have the full name first name last name
company name city state okay so the best way here is to use rapid API to retrieve
the company's websites and then feed it to the command center to to find the
emails so essentially you'd feed in the full name and then you'd feed in the
website that's gonna be retrieved from rapid API and is essentially just going
to be a Google search so Google search company name along with websites you'd
say plus website URL or website comm right and then you would just use a
autostructure that data and you just feed that to the command center or you
can wait just a couple days I believe maybe next week and I think we're gonna
have like the option to just add the company right instead of just using the
website URL so that way even people are using LinkedIn sales navigator they can
just feed in the company name and they they don't have to use like the website
URL that of that company you know so this is what I would do so I would just
have like like a rapid API flow right that's gonna be company name plus
websites comm and you'd see that that results into like an AI model like a
cheap AI model that's gonna retrieve that results and just structure it into
like a URL and then you'd feed that URL to the command center right so this is
what I would do man okay Sebastian said thanks in advance few instantly
questions that came up how do we ramp up the number of warm-up emails from 5 to
50 over the 21 initial emails specifically what settings to use for
the increased per day settings any issues with going higher than the
recommended one per day how to use warm-up custom tags at what email inbox
health should we pause sending from that account and park it in the warm-up so
obviously if your health score is like 80% I wouldn't I wouldn't pause the
campaign unless it's like 70 72 when you start when you start like entering the
territory of like 60% 70% then I would like I would pause the campaign I was
just let the emails like warm-up if it's like 90 88 78 I wouldn't worry too much
about it okay you're selling you're still gonna hit the inbox okay how do we
bump up the number of warm-up emails so I have already like a video on that and
it's called deliverability 101 okay so your send is gonna be this is using the
high intensity send them so first day after 21 days you'd send 20 emails per
inbox you'd keep the warm-up emails at 10 and keep reply rates 200% second day
you would pump the volume to 50 emails per day and keep the reply rate 100% and
then you'd pause for 24 hours let the inbox rest and keep reply rates 100% and
then you would repeat the same process okay now and this question how do we
ramp up so essentially it doesn't have to be you know it it's not like a
specific number okay so let's say for example like you buy the domains today
okay let's say you buy domains today you would send 15 emails along with 10
emails like a 10 warm-up emails every three or four days you would add 10 to
15 emails okay and then the you know the over the course of like the 21 days you
want to slowly just increase that okay it doesn't have to be like a specific
number like I said just because it's like it doesn't really matter you know
like one email or two emails it's not gonna make any difference you know so
just don't ramp up your volume like fast and just be
reasonable you know like first week I would just ramp up for like I don't know
like 15 to 20 emails max and then the second week I would add like another 10
just because I'm preparing my inbox yeah for sure man let me just link it here
there's a post on in community there we go
so I just sent it here so I wouldn't worry too much about it just at the end
of the 21 days the last day before you start your campaign you want to have like
100% reply rates along with like the the 50 emails are you gonna be sending okay
and the next day you just lower right and you'd send like 50 emails and then
the next day you would use the high intensity sending how to use the warm-up
custom tags I think this feature is not beneficial at all I've tried using it
before and I just didn't get any like I like I didn't get any enhancements when
it comes to my deliverability if you guys don't know the custom tags is
essentially just those like a couple words that these service providers like
instantly use to write those those emails you know let's say the warm-up
emails are going to be like hey Peter thanks for the call the other day and
then they would mention like something you know like about their dog or like
any like any subject you know and they use like a random words now we can
essentially just warm up these these words right you can essentially just
warm them like genuinely just warm those even those words just because when you
want them now they can be rotated right so yeah see you soon my friend good luck
we have a ton of people that have their sales call today good luck my friend so
I wouldn't worry too much about the custom tags as long as you have
basically your SPF DKM DMARC and you're warming up for 21 days and you're
slowly ramping your your emails your warm-up emails you're good okay okay we
still have a couple questions here hello Saad I have been running a campaign for
cybersecurity companies for a few weeks now my first week I did get some
interests one company was not ready and the other two ghosted me after a few
calls after a few emails I have sent 26 18 emails this last this last week I had
no interest and the only replies are out of the office the first sequences have
all been sent about 800 of the second and 700 and the final third email have
not been sent question is should I wait for the final results from the three
sequences before making any changes that should be the end of the week also
these are all Microsoft boxes I have ran email messages traces on them and I do
see them being delivered to the sender's mailbox I will be working during the I
will be working during the call but I will be listening thanks for your help
yeah the best way man is just make an offer audit make a post about it and I'll
just have to tweak the copy you know because if you've sent like more than
2000 emails there's definitely like a copy issue so make a post with copies
I'll audit and refine so that's pretty much it um Kai okay hi sad thank you so
much for all the helpful things you've shared with community nowhere is my
friend I have one question about checking for duplicates emails using the
Google Sheets yeah so first I'm scraping job listings for my recruitment clients
and often the same recruiter post multiple jobs so I ended up scraping
many of these listings tied to just one recruiter and I only get one unique email
so yeah man I the simple like fix for this is just to use a better scraper you
know instead of like trying to build like a scenario that's gonna like piss
off all your operations your make a calm operations I would just recommend you
use this one that I just mentioned now it's called the LinkedIn jobs it's
called LinkedIn jobs duplicates which is yeah this is the one yeah this is the
use remove duplicates so this one is gonna save you all the hassle is only
gonna retrieve one recruiter so you don't have to deal with it at all you
know so let me just link this to you here and there we go so now you don't
have to deal with anything you can just scrape the exact listings so let me know
if this helps if you have any follow-up question in the chat
okay perfect man no worries my friend okay what should I propose for it to do
in this situation I was thinking to ask for a setup fees regardless with a 500
per meeting charges with a percentage of the profits sharing per client they
close what do you suggest big brother no worries my friend thanks for sharing
the material cross we have discussed this internally summary below we are not
looking for any partnerships with me fixed fees we are open to explore a
paper lead model within with anything less than 500 per qualified lead yeah so
here's what I would do this is no fluff here's exactly what I would do I would
still pitch for an upfront fee but I'd lower the the the paper lead model so
instead of pitching like 400 I would go for like 200 per qualified lead and I
would still pitch for like let's say 500 or like 400 as an upfront fee you know
that way you're still covered so now they're happy just because they it's
less than 500 bucks you know you can even go lower than that if they still
push you can go for like 170 bucks for like per qualified lead right and then
you'd still push for like an upfront fee I would always recommend you guys go for
like an upfront fee even if you can get like as little as like 500 bucks I would
still take it you know so just lower the paper just lower the lead model here
right so the paper lead model so I wouldn't go for like 500 bucks or like
400 bucks I would go for like 170 that way definitely cash flow yes because you
want to use that money that they're gonna give you to run the campaign
instead of like reaching your own pockets yeah you know because uh cash is
what makes any business alive you know got it I pitched them $880 per lead as
their LTV is 100k and they raised 20 million okay so these are these are cheap
clients man they raised 20 million yes if they raised 20 million I would push
for more than that man and they're and yeah they're they're pretty cheap I can
tell this client is pretty cheap yeah so let me know how the sales go go man and
I believe you're gonna I believe you're gonna like close just because I know
you're really good at sales if you guys don't know how free it is like really
good with sales
okay Nicole said instantly has now ran out of their pre-warmed up email so I
was wondering if you have any recommendations for other providers that
are good quality just to disclaimer guys never use instantly pre-warmed up
emails it's expensive and their delivery is not the best always use
puzzle inbox inbox ology or premium inboxes if you want to get pre-warmed up
emails just get them off puzzle inbox and in fact I just wait let me go to the
main domain I just DM them and I said hey I run a community of like a 200
people and they want to use pre-warmed up emails can we get a coupon so maybe
we can get 60% off instead of 30 okay maybe we can get like a code SSM or
like puzzle SSM etc it's gonna help the people a ton obviously if there's a way
I can get a coupon from any platform I will still push for it just because it's
gonna yeah okay would love if one could if one of you could share a sales call
recording there are already like a couple sales recordings in the group my
friend 100% yeah so I was thinking about this maybe we can add like a call where
we basically just roleplay sales calls I don't know if anyone is up to this idea
yeah maybe we can have just you can just like you know just roleplay sales calls
and we can make it like a even like Rob Robin when every week we have a
different person yeah you know and then we'll make it we'll make it like
interactive and fun you know so someone would like just roleplay as a client
right and we'll pick like for example like like let's make it fun we'll have
like a spin the wheel and then we pick an industry right and then every
industry would have like a couple questions and then each week we have a
different person yes okay perfect no judgment of course yeah of course my
friend yeah yeah it's obviously gonna help a ton of people because now like
obviously they're not gonna be judging because I believe like it's it's gonna
remove all that all those nerves because it's people that we know you know it's
like a people in the community like a family you know so there's no there's no
judgment there's nothing there is none of that you know because we all want to
grow you know everyone wants to grow so yeah I would use puzzle inbox Nicole
don't use that instantly just use a puzzle inbox and can use the 30% off I
believe they have like oh okay so it's only valid for the standard inboxes okay
if you want to go for the pre warmed up emails if you go to get started and
hopefully they don't charge me okay so there we go if you go to total let's
say you want to buy 15 okay and you update that's like 40 bucks like 42
euros which I believe it's like 45 dollars or something and if you choose
like if you want to use instantly this is gonna be like 70 bucks right so I
would just use a puzzle inbox so I hope this helps I'm also oh wow so you you've
tried the puzzle inbox and they went to spam that's interesting
where were they Microsoft over there like googled like a workspace Google
workspace right maybe you were listening to Microsoft
yes interesting yeah check out inbox ology have you ran any in box placements
yeah okay so I would I would check with inbox ology that's that's interesting
because um Ibra me and a couple people in the community are you current we're
currently using it and it's like the delivery is pretty good I'm curious did
you verify the leads that you scraped using mills that I saw or like the
command center hmm yeah that's interesting yeah they recommend to only
send 15 emails per inbox per day yeah that's that's definitely interesting
yeah okay so you're also going to set up some of more of your own inboxes yes
when is the earliest I can start sending emails on a slow ramp up like one to two
days to it to a day so I can start sending out before the three weeks if
possible yeah you can you can basically start sending let's say you buy domains
today if you want to really get started you can send like one to two emails a
day and then the only thing you have to do just lower the warm-up like the the
the warm-up emails okay so instead of like warming up emails like to eight
emails per day you'd stick to two and then you just basically just send the
one or two emails per day but I would recommend just waiting like a week at
least a week okay because you don't want to burn those those emails
like it's just seven days you can start sending like five to ten emails okay
because I know you want to get started ASAP okay how do you go about
discovering new ways to scrape leads for example the well-found method could you
shed some light on the process yes that's a really good question that will
be $9,000 okay so it's essentially just yeah it's called a pain sniffing tactic
which is I look like at the market and I see like who is currently feeling the
pain that sales systems can solve you know because the first thing like the
first thing you need to do before you like try to find like ways to scrape
leads you want to like just like identify like a target that is feeling
like a pain you know for example this video that I recorded and it's like a
very successful campaign I targeted companies hiring for customer success
roles you know the position in here is that they are hiring for customer
success roles because they are experienced in churn but the best way to
basically handle churn is just to have more clients you know yes yeah it's a
like it's a pretty like you can get a pretty good idea if a company is hiring
for customer success roles right that they're dealing with churn they want to
have like a better onboarding but your positioning could be well I can just get
you guys new clients instead of trying to deal with churn and trying to keep
our existing clients you know because the main hurdle trust me like from all
like even like six to seven to eight figure companies the main hurdle is
always going to be clients coming in you know it's always going to be clients
so I would identify like a target and then I would try to basically find ways
to scrape leads for example if I'm let's say I want to target startups I know in
my head it's going to be crunch space or ricey combinator okay and here's the
hack you guys can use go to chat gbt and type in where can I find like it lists
me 10 to 15 platforms where I can find this target step two is you go look
through a pfi and see if there is any scraper that a developer has made that
can scrape that exact platform right because sometimes there are multiple
platforms that we don't know about you know there's this platform it's called
pitch book Angelus and these are platforms that nobody in the legion
space actually scrapes you know well-found like I've never seen anyone
that scrapes well-found on YouTube or even like even 100k a month like legion
agencies you know and I the way I was able to find well-found is I was in a
call with a recruitment company and they just mentioned the well-found they said
hey we use this platform to find candidates and I was like you know what
let me just take notes of this and I went ahead and I researched well-found
and looks like well-found is a platform where hiring managers or recruitment
managers or recruitment like agencies gather with startups right so look for
platforms right you can just use chat GPT for that and then you would do a
little bit of research to find these platforms and then you check epiphy if
there is any like scraper that scrapes those leads you know another way you can
do is you can just use yeah zoom info yeah yeah a great way to find investors
find real estate agents etc
you
okay so I believe these are all the questions that we have we've already at
one hour and 25 minutes which is not a one-hour weekly call because you guys
know even if there are any questions that I don't like letting any questions
hang in this thread it just annoys me okay guys so I believe that's pretty much
it these are all the questions you guys have yeah no worries my friend thanks
me thanks Kai thanks Sam Nicole Wells Victor Pablo yeah no worries my friend
always providing value if you guys see here in the command center we see we
if you saw this it's delivering actual value I remember I was talking to Sam I
was like what you had here and I was like let's type in deliver an actual
value yes for sure my friend yeah so there are multiple people who actually
recorded their sales call right and I'll just link you like a couple ones in the
threads and hopefully that would help and there's also like a video with like
a person in communities called Vishal he has like 15 years of cold calling and he
essentially went ahead and showed us like an ROI calculator how he presents
presentations etc etc yeah so that would definitely help and check out the
last weekly call where I went ahead and I showed my entire script of like my
entire sales call right and just to give you like a TLDR what I do like
personally I have like a different approach right and it has worked for me
and I see many people that like are using it in the community right now and it's
working as obviously when I hop on a sales call right I like I build reports
and then I just go like two to three minutes overview of what the system is
gonna do etc benefits over features and then I would pitch the price right away
that way I can handle like the remaining 40 minutes of price objections handling
objections just because now it acts like a pattern disrupts just because most
leads are so used to a salesman or like someone a consultant who provides
all the value and like explains the system and before that five minutes
before the call the big the big question the pricing now I front load that in the
first like the first 10 to 15 minutes now I can basically handle that like
handle all the entire thing and basically now we're gonna get down really
into it and now I'm gonna answer his real questions or her real questions you
know again it doesn't have to be just this you know like there are multiple
ways to approach a sales call but I think I believe this is the best way
because when people you know the best way is just front load the big question
now there are no nerves okay nobody's scared about the price and because
everyone is like well what was the big question is gonna be what can I pitch
the pricing you know so you front load all of that in the beginning and now
we're really getting down into it now I can really understand your business you
know so just make sure you watch that weekly call it was last week and I was
just went in in detail and I explained my sales script and there's also like an
if you guys don't know this is like a draw that I use okay and there's like
basically the script that you can just copy paste okay so I hope this helps
man okay guys so thank you so much for coming today really had fun talking to
you guys I love interaction with you guys in the chat it's way better more
natural I don't like just reading the questions and just answering them I like
to speak to you guys that way it's more natural you know and you know it's just
better that way because it's a community it's not just say you know okay guys so
thanks Kai thanks Pablo thanks Mehdi thanks Sam make sure you guys give Sammy
his flowers all the time our core developer Nicole Daffy up and kill
Nathan Ahmed and AI agents Victor all of you guys love you guys and I'll see you
guys in the next weekly call cheers
you
