What's up, guys?
Hey, <PERSON>. Hey, <PERSON>. What's up, everyone? Hope you all doing well. Hey, <PERSON><PERSON><PERSON>. What's
up? Hey, <PERSON>. Hey, sir. How are you? Sorry? Am I audio? Yeah, can you guys hear me? Yep.
I can hear you. Yeah, perfect. Let me just mute you guys so everyone can hear. What's
up, my man? What's up, <PERSON>? <PERSON>? Kai? Let me just admit, we have a very good session
today. And actually, I actually just built the system that you guys want, the one that
sends pre-meeting information about each lead. And it's actually amazing. Hey, <PERSON>.
Hey, <PERSON><PERSON>. Hope you all doing well. Let me just admit everyone just come in. AI agents.
Need help with NA-10 with command center setup not working. Which one? I believe you have
like an issue with the API or something. Is it like an API? Yeah, just make a post about
it. And I'll tag <PERSON> for that. If it's an API issue. Yeah, if it's an API issue, I can
just generate a new API key for you. Yeah, the cool part, <PERSON>. Yeah, the cool part about
is now you guys can integrate it with this pre-meeting system that's going to send like
basically I can overview to each client as part of the deliverable. Yeah. So I don't
want to like I don't just want to tease it before I show it. I show you guys the system
here. Yeah, it's probably going to be like an like an issue in your NA-10 workflow. Yeah,
definitely. Let me just admit Sam. He can even like help you live today. Saves you the
hassle of recording like alone. What's up, <PERSON>? Actually just got a positive reply
targeting companies that recently launched a new product, I believe. Yeah. Hey, <PERSON>. Hey,
what's up, man? How you doing? Good. If y'all doing well. Okay, great. So what I'm gonna do
now is let me just admit everyone that's come in. Okay, so I'm gonna share my screen now. I'm just
gonna go ahead and answer all of you guys's question. Obviously, the first 45 minutes are
going to be questions mentioned in the thread. And then the last 15 minutes is gonna be questions
in the chat. So if you have any miscellaneous questions, just post them here and then I'll
answer them. Okay, sounds good. Perfect. So let me know if you guys can see my screen. Perfect.
Audio is good. All good. Perfect. Okay, so the first question we had from a whole can you please
provide recorded demo video? Yeah, man. In fact, the today's session, we have really, really good
questions from people here in the thread that talked about, they basically asked questions about
sales calls. And we're gonna go deep. And we're gonna do like a deep dive into like basically,
what kind of the best way to approach a sales call, etc. It's gonna help you guys a ton. So
that's what we're gonna do. So the second question is, do Microsoft Outlook emails have also poor
delivery to Google or is just as Google to Microsoft? Actually, the only thing that we the
only issue that's currently is Google to Microsoft, right? So and the best way to tackle this is just
to get the email inboxes from inbox ology. So I was just in a call like a first client reward
with Yeah, it was bizarre. And he also what he's done is he essentially just bought emails off in
box ology. And he's not having any issues with that. So what I would recommend you guys to just
bypass this bullshit is just to get the email domains. Let's say you want to let's say have a
list. We have multiple Microsoft inboxes. Let's say your ICP has like multiple inboxes that were
hosted in Microsoft. So I would get like a Microsoft inboxes off inbox ology. And I will just get the
Google inboxes from premium inboxes or like puzzle inbox. I've been using premium inboxes and puzzle
inbox for like two months now. And they're great. Amazing. Especially inbox ology. They basically
just set up every single thing for you. They set up SPF, DMARC, like DCAM. And they even like link
it to instantly with the best cold email practices. And they also like turn on the warm up. And they
do it in like two hours, you know. So that's the cool thing. Puzzle inbox do the same thing. So I'm
not having any issues with delivery when I'm purchasing emails off these two platforms. So TLDR,
Microsoft, you want to get like email inboxes, just get them from basically inbox ology, right in
box ology.com. And if you want to get Google inboxes, just get them from premium inboxes or
puzzle inbox. Okay, these are high quality, you know, US, I like email inboxes, and you will not
have any issues with delivery. Okay, third question we have Ahmed said, I got a question about pricing
strategies. I have watched the video in the classroom. But if we work purely on performance
and charge one set one time set up fee, who incurs the subscription costs of the system? And how can
we track the leads are coming in the door, booked into a calendar? Do we need access to their
calendar software or something? Yes. So what I would recommend is during the kickoff call, you
would essentially just make a duplicate link of their, you know, their, their, their booking
software. So if they're using call.com, or like they're using Calendly, just tell them, hey, let's
it's gonna take us like five to 10 minutes, just make like a duplicate of that event, right. And
then you would integrate into your CRM. You can even integrate like a worksheet, if you really
don't want to use a CRM. What I would recommend just use ClickUp or like Notion just to track the
booked meetings, right. So whenever there's like a lead that books in that event, which is why I'm
saying, hey, it's going to be a duplicate events, not the initial events, right. You're gonna have
to receive it in your CRM and you can track it. The first question is, if you work purely on
performance and charge one time set up fee, who incurs the subscription? Yeah, that's the cool
part about working on performance performance. So the the way what the reason why we say it work on
performance just to get our foot in the door. Okay. So when you say work on performance, they pay
it like they pay you the one set up fee. Now your job is to just over the lever. And once you over
the lever, it's time to push for a retainer. Okay, so when you think you basically work for
performance, right, first specialist. Hello, this seal. How you doing today, sir? What's up, man?
How you doing? All right. Well, it's pleasure. Take a quick look here. It looks like they're reaching
out regarding your 2017 Lincoln. See here. What model is this? But yes, sir, it looks like you
guys opted in for them to assist you once the initial coverage had expired. But what I simply
do, sir, I help qualify that vehicle. From there, I'll provide you the details on all the benefits
that they apply for that early opt in. Oh, yeah, let me just mute. Yeah. Yeah. That's interesting.
Yeah, that's funny. But, um, yeah, I forgot my train of thoughts. So let me just go back.
Okay, so yeah, the cool part about, you know, working on performance. So that's a sidebar.
The cool part of working on performance is that once you start working on performance and they
pay you the set up fee, now it's your job to push for a retainer. And usually when you push for a
retainer, this is where they handle the subscriptions, et cetera, the make for the
personalization, et cetera. Okay. So the first offer, you know, the first, like the way you're
going to get yourself in the door is basically going to be the one set of fee. And then once
you over deliver, which is, I always say, Hey, over the lever, once you booked in the meetings,
then you'd upset hard and say, Hey, I can't, like, I don't see any reason why we cannot,
um, basically double or triple outreach efforts. And, uh, we usually, basically we usually like,
uh, have like a retainer for this and I'll just straight up just sign them for like a three month
retainer. Right. And in that case, either there's two ways, either you handle these subscriptions
or you can just basically include that and your next proposal, you'd send them a proposal of
retainer and they covered the subscription costs. And the only thing you do is you have to basically
do the work, which is a scrape and leads copy, et cetera, et cetera. And usually when you
deliver the first five book meetings, it's pretty easy to just ramp up the volume because you know
what offer works and what offer doesn't. Okay. So hope this helps, man. Okay. Uh, another question
we have is what do you think cold album will look into your school strategies, et cetera? Yeah. I
think that's a really good question, man. Uh, I think it's only gonna get better in the future.
Okay. Just because AI models are going to be even like crazier. They're like, we're going to have
like multiple, multiple tools now to make our outreach even more personalized. Think about it.
Now we have AI that literally just crawls the web for us. Just like, uh, six months ago, we didn't
have this six months ago. I didn't know about Exide and not about, uh, like, um, Vena AI,
all of these new tools. So more companies are gonna like build out all these softwares,
all these solutions for us that we can use and utilize in our outreach, which is going to make
it even more personalized, you know? So it will get to a point where there's literally like, uh,
like an AI SDR that handles everything for you, you know, like it would literally just like,
I think about it, like you can automate like 80% of the entire thing, you know, AI can like do
like a, you know, market research can generate copies, you know, right now we cannot generate
like full blown copy, but definitely in like a one to two years, we're going to have like AI
that generates copies. There's like this software is called VAPI and it's literally like, like,
it's just very human. You can essentially just train like, uh, like an AI agent or like trying
like an AI voice agent that just literally just goes into the sales goals and closes for you.
Right. But we're definitely going to lose that human touch. 100%. It's called VAPI. Yeah. VAPI.
It's a V-A-P-I. Yeah. VAPI. VAPI or VAPI. I don't know. Okay. Uh, now we get into the real juice
of this call, which is how many inboxes do you usually get per client? Well, this is,
this is highly dependent on the volume that I want to send for that client. Okay. So the usual
bar park as I get them, um, from eight to 10 domains, right. And, uh, two to three email
inboxes, right. Two to three email boxes per domain. I don't, don't go over three email inboxes,
never do that. Unless you are using pre-warmed up emails, then you can, you can, you know,
you can, you can send from five emails just because those emails, the pre-warmed up emails,
they've been warmed for like months, you know, and they have like high quality ESP. It's not like
they've been warmed for like just 21 days. Okay. So again, I would just get eight to 10 domains.
Again, it's very highly dependent on the client and how many like, because you'd be on a sales
call and the client would be very, very, very interested in working with you and they would
be very excited and they're like, okay, can I, can we send thousand email? Well, at that point,
I would just like, uh, I would just anchor my price. I would like up the pricing and I would
just get them more domains would be like a higher standard infrastructure. You know, it all depends
on the goals of that client. Okay. Like literally when you get on a sales call with the, with the
clients, like literally ask them, what do you want? You know, because the best way to like give them
what they want is just ask, ask them what they want. You know, this is a basic sales strategy.
You ask someone, what do you actually want? And you try to like basically, uh, get them closer
to that outcome, which is basic sales, you know? So I hope this helps. Uh, would it be possible
to build an automation for pre-meeting breathing for clients and the spirit of over delivering?
And yes, man, I've built this for you right now. And let me just walk you guys through it.
So are you guys familiar with the CRM feature? And instantly, if you are just, uh,
put up like a thumbs up in the chat.
Not really. Okay. That's interesting. So there's like a CRM integration and it's
sort of bullshit. Okay. So what I've done is, uh, yes. Yeah. It's, uh, it's an instantly
instantly. It's like the cold email software. So, um, the cool parts is what I've done
is I went through the rabbit hole and I've bought the CRM. You know, there's like a,
there's a new feature that he added. Well, not really new. It's been like six months now.
And, uh, what they've done essentially just changed the unit box and they gave it like a
newer UI. So you can essentially just reply to your leads from there. It's kind of like a CRM
inside of, uh, instantly. But what I've done is I went and I looked at the code
that they use to build out that feature. And eventually I've managed to steal it
in a sense. So it's definitely like a gray hat way to do things, but this is going to be
community exclusive guys. I'm going to make a post about it just after the call.
And what I've done is I essentially bought the CRM feature and I went and I looked through the
code and I was like, okay, when they, when essentially when they, uh, if you guys don't
know, just to give you guys some context. Okay. The CRM has like a feature, which is the AI
instantly. So whenever there's a lead that replies to AI, um, instantly has an AI, like an internal
AI inside and they can essentially give you the company description of that lead that just replied,
the ideal customer, competitors, et cetera. And I looked around and I was like, okay,
what is the URL that they call whenever there is a lead that like their response?
And I found it and it's actually their backend. So check this out.
So it's app at instantly AI back end. So this is their backend API that we're playing with
V1 companies. And this is the URL that they basically call, which is pain plus points.
And the cool part is you don't need to have a lead inside of that campaign to use this feature.
I can literally run this module only, put any email I want, like any website. So for example,
just put mine. And if I click on, okay, I can essentially get the pain points of my process,
which is definitely interesting, right? Again, there's another,
like a API that they call, which is company description. So they call instantly API,
backend API companies, company plus description. Now, in order for you to get this, uh, this URL,
you'd have to get their C or like their CRM feature and you'd have to duck like literally dig deep
into the code. And you'd find that, you know, that it's like a, it's like a, it's like a,
deep into the code. And you'd find that, you know, that, that URL that they called,
which is called hidden APIs. These are hidden APIs. Okay. So again, here's the parameters.
Guys don't change anything. Just change your API key here. Okay. And it's basically unlimited.
Okay. So essentially you're just pretending that you are using the CRM feature, but you're just
using their API. Okay. So now think about it. You can use this even in your personalization.
Okay. And this is, I'm never ever going to show this on YouTube or like, like write about it or
just because I don't want them to patch it. Okay. So it, this is just the inner circle.
It's definitely gray hat. So this is why I'm never going to, because if I share on YouTube,
like they're going to patch it and I just don't like it has to be exclusive. Another thing,
you can literally even retrieve the company competitors and you can even retrieve target
customer profiles. So it's always going to be customer plus profiles. So what I've done is I
notice the pattern as you guys can see, there's type equals customer plus profiles pattern.
Again, to type equals competitors, pattern three, company plus description. So I was like,
okay, so they just basically called this API's every single time. Now you can essentially just
build out a system that you can, let's say you have a client, you know, and you added them to a
Slack. Now, whenever there's a lead that is interested, you send it to the Slack. Okay.
And AI is like a, you're going to use their own API. So now not only you're not, you're going to
lower the costs. Okay. You're lowering the costs. We're not using any, you're not using any AI model
at all. This is going to take you like a lot of like a, it's going to like basically it's going
to eat up a lot of tokens. And another thing you're not using rapid API. You're not using
XI. You're not using any third party software. Okay. So you're using basically the same thing
that is pretty much $0. So now the cool part is you can either use this as watch leads. Okay. So
you'd watch a lead that responds positively, or you can, you know, discard this and let's say you
want to like, like literally like, like, uh, be the best at this, just unlink this and now add their
booking software. Let's say there's a calendar or call.com. You would have a web hook that watches
the booked meetings. And the cool part is this is the crazy part that I want you guys to understand
is that you don't delete doesn't have to be an instantly. So you can literally, okay. Um, uh,
guys, uh, someone puts, put up their email with their main domain and I'll show you guys something
like put up your website here, like your email.
Okay. Check this out. Let's just try harsh. Now check this out.
Okay. So we're going to find your lead. You're going to find your pain points harsh. Are you excited?
So, uh, these include costs and commitment to hire and hiring and staff. They're required
for in house hiring, offering a flexible cost-effective alternative onto the men
automation experts, efficient lead acquisition and business process automations. They aim to
solve lead acquisition challenges by identifying the perfect channel and automated business
processes to scale efficiently need for scalable growth systems.
So that's the cool part is that it doesn't have to be right in the instantly, uh, you know,
dashboard in the leads. Okay. Let's try the company description again.
Let's find your company description harsh. Again, if I just run this module only, I'm going to get
your company description. So now I can use it. So apparently you help B2B SaaS and other businesses
scale by providing growth focused system. Is that right?
Okay. Great. On points. Yeah. So now not only you can use this as personalization, right?
But think about it. There's literally like, I'm getting so many ideas at this. So imagine you
scrape a lead list of Apollo, you'd aim at the top of the list. You'd aim at the top of the list.
You'd aim at the top of the list. You'd aim at the top of the list. You'd aim at the top of the list.
So imagine you scrape a lead list of Apollo, you'd include the LinkedIn URL of that company.
Now we can also use the command center to scrape the LinkedIn page for even more accurate data.
And let me just show you guys something. So for example, um, now let's just use this email,
for example, just auto online this and let me zoom out so you guys can see everything.
So this is what I used to do with clients.
Check this out. So if I click on run once,
I believe we forgot to add, okay, we forgot to add the map in the email. So we're going to get harsh
website in the slack message. Yeah, let me just, uh,
add. So email save
and it would be, um, search leads, which is this for this guy.
Okay. Awesome. So let's just, uh, run once
and then I'll just save this and I believe
it's going to have to wait until we searched the lease. So there we go.
So not, not, not only that, now we have structural data, right? And we send it to slack.
Boom. So I have my process assistance, newly generated. We have the name of the lead company.
We also have the data applied detailed research overview. Now we have the research. So PDG
consultants appears to be a technology consultant firm focused on digital transformation and
this from transformation and enterprise software development, particularly serving the med
reported the med. Let me just remove this tab right here so I can see
serving the media and the entertainment industry. They seem to specialize in customer relationship
management systems and technology integration services. Key pain points. The company is likely
struggling with client acquisition in a competitive markets, managing complex digital transformation
projects and possibly experiencing employee retention. So again, competitive landscape.
You'd also have their target customers. So their ideal customers include media, executive, CEO,
CTO, seeking digital, transformative, and AI solution. Now just reading, reading this, I can,
like, I can pretty much show you what their ideal customer, and if you want to deliver for this
easy. So you target companies hiring for AI roles just because they're seeking AI solutions. That's
a pain sniffing tactic, right? So if a company is hiring for an AI developer or like a prompt
engineer, there's a pretty good chance they want to implement AI solutions. Marketing managers in
media, advertising needing fancy arm systems for better customer profiling and IT directors
broadcasting telecom looking for cloud integration business. So you send this to your client. They're
like, holy, what is this? So it's even like more simply over the lever. The cool part about this
prompt is you can further optimize this. So the only thing that I've done, it took me five minutes.
You can essentially just make this even better. It's just like an MVP. I'm feeding it. I'm saying,
hey, you are an AI assistant tasked with summarizing and presenting information about
a potential sales lead. Your goal is to provide a clear, concise, and conversational overview
of the leads characteristic based on the given information. Essentially, I'm just feeding in the
lead pain points, lead company description, these potential competitors, and they're the ideal
customers. And essentially, I'm just saying, hey, remember your goal is to provide a clear,
accessible overview that anyone in the sales team can quickly understand and see. Now again, you can
use this as just a positive lead that replies to the instantly campaign, or you can just not worry
about it. And like whenever a lead actually books, then you'd send it to the Slack. Okay?
So I hope this helps, guys.
Yeah, it's a really good question, Aaron. I'm gonna, yeah. I think you're familiar with hidden APIs, Sam, right?
Yeah, of course you do, man. You're better than I am.
Okay. So another thing is, let's go over, yeah, Apollo has a hidden API. Oh, man. You're just
speaking my mind. Yeah, they have a hidden API too. So if you guys don't understand, like these
platforms have hidden APIs, the only thing you have to know is which API they call. If you find
that API, you can use your API key and literally like, you can use your API key to find the API
using any feature, which is a grey hat technique, but we don't have to worry about it.
Sorry, I have to jump for a meeting with a client. Yeah. Good luck, man. 100%. Yeah, we have really
good, you have really good questions. So I'll answer them in details. Don't worry about it.
Good luck, man. Cheers.
Cheers. Okay. So let's go back.
I saw someone here mentioned that Inboxology inboxes might have been Microsoft are set up
with only 15 emails a day. Any thoughts on why is it risky to increase to 25?
What I wanna, so this is a really good question. I want you guys to have this way of thinking.
So when someone says 15 emails, it's not like a defined number. If you go over 15, let's say
send 16, now instantly your delivery just sucks. No, this is a general ballpark. So 15 to 25 emails,
this is a general ballpark. So if you send 15 emails a day, or you send 20 emails a day,
or 25 emails a day, that's virtually the same. It's the same. Just don't go over that 30 gap.
Okay. So 15 emails a day, if they set up at 15 emails a day, I would still increase it to 20 to
25. I wouldn't have to, I wouldn't worry about it just because currently I am sending for a client
and I'm like ramping up the volume like crazy. I'm sending like 1200 emails a day and I'm not
having any issues with that. So yeah, the general ballpark, it was just gonna be 15 to 25 emails.
Okay. Doesn't have to be just 15. And I wouldn't worry too much if you increase this to 25.
So to track leads and how many booked meetings you get a client and how you set up their calendar
and booking links and how do you invoice every week or month or day if the window per meeting.
Yeah, when I used to do this, I would just basically, I would have like a contract
because if you're gonna want to get paid per meeting, it has to be like a really good
polished contract, right? Where they add you as a Stripe, like a user, which is going to be just a
view option only. And it would be like a trigger, right? That whenever a lead books a meeting,
either one, you send it to Slack, right? And like what I would recommend you do is you'd
send it to Slack for the first initial five booked meetings. And when you want to upsell,
just because clients will get a little bit uncomfortable if you want to like dig down
in your systems at first. So my recommendation is just get them those five booked meetings
and just make sure they pay you through Stripe, right? So it will be like a recurring, right?
So once there's a booked meeting, they would just send you like an, you send them an, either
you send them an invoice, right? For like the total five booked meetings, right? And they just
pay you for the total. Depends on how you want to do it, but I would recommend just bash everything
else in one invoice, okay? And another thing is once you deliver the five booked meetings,
then I would go ahead and set up a Stripe payment with their systems, okay? It's a little bit more
complicated because you'd have to go on a sales call with them or like a weekly call. You'd say,
hey, let's integrate Stripe using your API. So whenever there's like a booked meeting,
like instantly like just triggers like an invoice that would be sent to you. And it's not just click
on pay and they pay you, okay? But obviously you want to build trust first. I would recommend you
do it unless you are transitioning after the five booked meetings, so like a retainer, okay?
But TLDR, just invoice them like at the end from the five booked meetings, okay? And make sure to
track them like a good CRM and make sure they add you as a view only in their Stripe, okay?
I was trying to work on this automation to isolate CEO name from a Google search of the CEO in a
company name, but it was a little tricky and the HTML wasn't getting processed well by Claude. So
the first issue here that I'm seeing that I'm still using, I'm pretty sure you're using like an
HTTP request and just Googling the web, which is definitely the wrong way to do it because you now
have to strip the HTML. So instead just to use Rapid API. Like a lot of people like actually miss
the value posts in the community. Like I would always recommend you guys look over,
like click on value posts, filter. You would see a ton of posts that you've missed like this.
It's just use Rapid API, right? And they provide you with the exact name of that
hiring manager or CEO, okay? Yeah, it's very easy, Axel. The only thing you have to do,
it's kind of like when you try to authenticate like a URL, the only thing you have to do is
add the authorization, the API and the headers. Yeah, I'll make a video on that.
Hey as well, now we're getting into the real juice again of the questions.
And I took some notes about these questions. So let me just go ahead and go to my draw here.
Yeah, you always do this, man. It's always like five minutes before the call.
I'm kidding.
Okay, so okay. One thing I wish someone told me before launching the first three campaigns.
Okay, so I took some notes of this. And the first thing that I would say is that you want to kill
anxiety with clarity, okay? So whenever like, let's say, okay, for example, let's say you're
working with a client, okay? And you want to manage their expectations, okay? See, you want to have
clear weekly or whatever you want, daily checkpoints. For example, let's say you
onboard a client today, you would say, hey, and like during the kickoff call,
lead sourced by X, copy ready by Y, and campaign live by Z, okay? So once they pay you during the
kickoff call, basically just tell them, hey, during this date, you're going to like at this
date, you're going to receive, you're going to see the lead source, okay? You're going to see
the copy, you know, the campaign is going to be allowed after 21 days, okay? So this way,
you're going to eliminate like 90% of the stress, just because in my experience, like some clients
will freak out when they don't know like what's going on, especially in the warm up phase,
like the 14 to 21 days, okay? So a simple click up or a notion board with like updates
will just save you the hassle. Again, another thing that someone, like I wish someone told me before,
is whenever I onboard a new client, I would use fewer variables. So this is huge, okay? So
one offer, one persona, one CTA, just because you will learn way faster, unless you want to go for
like two CTAs. For example, let's say you have a client that offers like, they have like some sort
of guarantee, or like they offer like some sort of lead magnet or something, okay? Then you could
have like two CTAs, but I would always, like always, like few variables, one offer, one persona,
one to two CTAs at max, just because you want to learn what works and what doesn't, okay?
So you can just discard it as quickly as possible, okay? Yeah, yeah, makes sense, yeah.
Another thing is how do I manage client expectations in the first two, three weeks?
Again, pre-frame it in the kickoff call, and I literally like just say to them, hey,
like I make it like funny and stuff, I make it entertaining, and I say the first two, three weeks
are like the gym, you don't see abs right away, you know, but the foundation matters most, okay?
And the way I say it is the first two, three weeks is always going to be like the initial phase,
like I would say like the pilot phase, you know? If you really want to be suit and tie, you want
to frame yourself as a growth partner, you'd say, hey, the pilot phase, we're just going to go ahead
and interfere with the market and see what works and what doesn't, and then once that's done,
we can just double down on the efforts and just scale, okay? Another thing I would want you to do
is over-communicate wins. So even if it's just an email warm-up going well or bounce rate looking
clean, keep them bought in. So what I do is say, hey, I just linked the email inboxes to instantly,
the warm-up is going well, looks like the delivery is going to be amazing for our campaign,
so looking forward to knock this out of the park for you. So you're making it positive for them,
you're over-communicating the win, even if it's just small wins, okay? I finished the copy,
finished the initial copy, or I used to do this back then, I would say, hey, I've sent your offer
to our copywriter and I didn't have a copywriter, I used to do the main thing because I'm a one-man
agency, but I'm just framing it that way so it gives me more authority, and I would say, hey,
I sent it to my copywriter and it's looking good. If you have any like questions regarding that,
just let me know, you know? And they love that because you're essentially just
giving them, like it was just over-communicating wins, even as just the smallest, tiniest wins,
okay? Another thing that I used to do is weekly loom updates. This is very, like I've never talked
about this before, so no matter what's happening, I send them like two to three looms showing
progress weekly and say, hey, I've noticed that when we use this copy, or I just showed them
instantly campaign and say, hey, when we use this copy, we're getting better replies, and now you're
planting the seeds to more of a consultative approach and helps you upsell for a retainer,
because now we're not just a lead gen service, we're not like a lead gen provider, you're
essentially someone who's a strategist, who's going to help them with positioning,
who's going to help them tap in other markets. So everything that I do, everything that I do
plans the seed for an upsell. Everything that I do leads to an upsell. Every single thing,
every conversation, every single thing that I tell, that I discuss with them has to bring me
to my end goal, which is locking them into a three to six months retainer. So does that make
sense? Yeah. How can we build our own pre warm up domain system so we can all send from
day one to clients? How can we build our own pre warmed up domain system?
I'm not really sure what you mean, Sam. Is it like having your own infrastructure or something?
No, no, I just mean like the same domains we bought for $16 from
instantly pre warm up. So how can we do this for the coming off?
No. How to do the, like how to set up the domains?
No, I mean, if we, for example, if we bought lots of domains in the recruitment niche,
we pre warm them as well. So everyone can use it or like instantly we buy pre warm
domain from instantly. They are expensive from instantly. Yeah, I wouldn't buy pre warmed up
domains or emails off instantly just because it's very expensive. Just get them off puzzle inbox.
Yeah, but we need to like take two or three weeks to pre to warm them.
The cool thing about puzzle inbox, let me explain to you how they work.
So the way that it works is that you give them your instantly logins
and they buy and they basically they have the pre warmed up emails like in house,
right? And they just link them to instantly, you know, so you don't have to do anything.
You just pay them and they do everything. Okay, so we can, we can start sending from day one.
Yeah, day one, literally day one. You don't have to worry about warm up at all.
Okay. Which is why this is the core, you know,
this is the main reason we are using pre warmed up emails because we want to send from day one,
you know? Yeah, but we still need to buy pre warmed up email from instantly for $16.
No, you don't have to. The only thing you do is just buy them off puzzle inbox,
give them the instantly logins. They essentially just link it to instantly account. And once it's
linked, boom, you can start sending. Oh, that's nice. Thank you. Thank you.
Always. Another question you had, Joel, really good question. And this is like,
this is going to help like a ton of people here, man. You always have the best questions here.
The biggest unexpected challenges during first two, three clients campaigns.
And actually, no, the, yeah, the first, yeah, the things that I wish I knew before. Yeah.
Clients ghosting after sign off. This is something huge, guys. Whenever you are on a sales call,
right? And let's say that client likes you, they want to work with you, et cetera.
Always, always schedule next steps during the same call. Let's say you want to schedule like
a follow up, literally say, Hey, let's go ahead and book our next meeting right now and literally
get them to book it live same day. This is will really help with your conversion rates
and to align incentives. You could say, Hey, um, like this is how I frame it. I say it's
pretty quickly in the sales call. So they don't even like, like they don't like,
it just goes on through their head and say, yeah, okay, so I'll just just book a meeting.
So what I do is I say, Hey, um, let's just go ahead and book our next steps
and let's just go ahead and like slash you in this day. Like, does that sound good to you?
And I say, Hey, uh, I'm going to send you like, uh, some industry insights
next following days. And then we can just basically got on a call. So let me know, like,
let me know if you had a chat, if you'll have a chance to look over them, read through them,
and we can just talk about them during our kickoff call. Okay. So they're like, okay,
oh, he's going to send me like, uh, some industry insights, maybe something that I would,
that would help my business, et cetera. And I just say it that way. Okay. If you notice what
I've said, I've said it in a way that's like, it's very sleek. So essentially what I'm doing
is giving them value. So it makes it easier. It makes it easy for them to say, okay, yeah,
let's just book a call, you know? So always, always on during the sales call, let's say they
are, yeah, let's say they're all, they're like, even like, if they're a little bit lukewarm,
I would like, I would book them in like a kickoff call right away. Okay. Because, uh,
what I would recommend you guys do whenever you are with a client, et cetera, you want to guide
them literally like, uh, you know, like a kid, you know, this is the best way to just explain it.
You know, we want to like take their hand and basically just walk them through your process,
you know? So always, whenever you speak to a client or like you discuss something,
always, it has to be like, and not like, and like the next step, right? The next step,
are we sending them proposal? Uh, are we paying? Am I sending you an invoice? Like next step,
next step, next step, next step, next step. You know, you don't want to be in that weird phase
where it's just frozen, you know, always next steps, always next steps.
And this is, this is what I would recommend you guys doing during the sales call.
Even if they show the slightest positive, uh, engagement, get them on a follow-up call
right away and say, Hey, let's just book our follow-up calls. We can go over the details
and, uh, if it sounds good, I'm just sending you a proposal. And, uh, once you look over the proposal,
just click sign and we can get started as soon as possible and we can start delivering the
meetings in like two weeks. And they're like, uh, okay, yeah, let's just do this. You know?
So you want to be like a shark. You want to be like, you want to strike whenever people
are hot, you just want to want to strike. Um, another thing, another, uh, thing that I had to
learn is that campaigns sometimes take longer to ramp just because I expected results in week one.
And sometimes it does happen. Sometimes you'd launch a campaign in like a few days and you
would get, you know, you'd get like positive replies within like 15 minutes and you'd book
a call. So it's highly dependent, but always go from that frame, always go from that, you know,
uh, state of mind. Okay. It takes a little bit of time of tweaking angles and being consistent.
Okay. Um, another thing, which is huge is clients not having a clear offer or knowing what makes
them different. So I used to assume that clients had their position in locked in and even like some
people, like some clients that were making six to seven figures and they still, like, they still
don't have their positioning grade. They're, they still don't, don't like, they still don't understand
how to angle that any cold email. So it turns out a lot of them are still figuring it out and that
shows and their weak CTAs are vague messaging. So they have to have like a, like, let's say they
had like other client acquisition channels, like a purple clip ads or like referrals, et cetera,
maybe like they have like a funnel, but they suck a cold email. Yeah. They'd be clueless,
which is why I'm including the onboarding, you know, the onboarding questionnaire that,
and we go over it like literally like during the sales call, let's say, Hey,
we're going to go over the onboarding call together and onboarding questionnaires together.
Okay. So we literally do it hand in hand and it makes them think, okay, when they read through
the question, okay, what is my ideal customer? What is my, what, like, what do they need? Like,
what do I sell? Like explain that to a teenager, you know? And I'm going to give you guys a story.
There's a client that I had before and they essentially, they, their cold email sucks
just because they, they didn't have like, they, they, they weren't doing like a good job or
explaining their offer. I always say this, if you can put the right people in the right room and
say the right thing, you will never be broke. So the same principle applies in cold email.
If you can take an audience, which is your ideal customer, and you say the right thing,
which means the clarified offer, very simple explains in like two to three,
like, like literally like in third grade, right? There is like simplify the offer,
simplify it, simplify it, simplify it. Now we don't have to do any, like, you don't have to pitch
hard, you know? So again, it, it frames you as a position is and someone like a, like someone
who's like willing to position them for greater success. Again, everything that I do plans to
seize for me to like be a more of a consultative person later on, you know? So I had to start
helping them shape the offer itself, not just read emails. That's when I realized like good
outbound is 80% offer clarity and 20% delivery. Okay? So if you have like a good copy is simply
a good offer. So I hope this helps you well. Yeah. Yeah. No worries, man. Okay. We have another,
like a lot of questions here that I'm going to go ahead now and tackle them. So hi,
Scott. Thank you for recent insider access only videos and the white label case studies.
Yeah. 80% offer and 80% 80% offer clarity and 20% 80% offer and 20% delivery. So thanks to recent
insider access only videos. No worries, man. And white label case studies. I have a lot of meetings
on my calendar and I'm stuck on a few questions below. After talking to friends, I lied. It's a
lot of questions. Hope you don't mind. Yeah. No worries, man. New clients offer. Don't know if I
offer. Don't know if I should take that offer or not. This client offers a peer to peer coaching
program to help HR and L and D leaders improve their communication and coaching skills,
especially when transitioning into leadership roles. They've also added an AI agent like chat
GBT that lets these leaders practice tough conversations with the bot. Interesting. It's
a brand new offer. They even have not priced it yet. No interaction. Don't know if it's a product
market fit and the business makes four to nine K a month, pretty new and small. Yeah, that sucks,
man. I'm not sure if I should run a campaign for them because their product is brand new,
unproven. Not sure if it works when we message it to the market. Should I write emails targeting
HR and L and D leaders or go straight to CEOs for this? I have no idea to find one ICP for this
offer. I'm thinking of scraping HR leaders or CEOs hiring for roles like head of people. Yes,
this is a pain sniffing tactic. Yeah, for sure, Axel. Good luck, man. You have a lot of people
that have their sales calls today. Good question, man. I don't know how to write the dream email
to speak to this pain points as it's not revenue or money driven. You can definitely like tailor
that into like a money driven revenue generating copy. It's more about making the HR leaders look
good and have a better management in the business. OK, so again, I took some notes because
it's a pretty hefty question. Now, should you run a campaign for them? Now, I would only,
so I would only one, I would only run this campaign. If the clients is fine,
that the first camp fine with the campaign being just like a test phase. OK.
Yeah, no worries. No worries, Jesse.
So just because in this for these clients, I believe the results are going to be unpredictable
and that's fine if they understand that this is a learning sprints, not a guarantee of meeting so
I wouldn't I wouldn't like I wouldn't pitch for like a like a guarantee of like five to ten
book meetings. I wouldn't do that at all. OK, I would just say, hey, just because the product
is still, you know, they just launched. They don't they don't know. They don't know if it's
going to work or not. So I would just always, always whenever I have these type of clients,
I would just work with them in a way where I don't where I don't promise any guarantees at all. OK,
so another thing I would do is I was asking these questions like what is the main pain point your
market has that your product solves? OK, you want to like really understand this and you want to
ask them, have you tested this offer in any way? And what would validation look like to you after
a few weeks of outreach? So let's say we run this this sales system campaign. What do you think like
and we get like a positive a couple of positive replies, do you consider that as validation
or do you need to basically tweak the offer to reach out to reach more like a like a better ICP?
Right. Just because when like there's a it's a new company, they don't know like they don't
know exactly if it's going to work or not. So if they say we're not sure the new campaign
should be performance based, frame it as helping them test message market fit and they have to
invest like an upfront fee just because, you know, you're not going to guarantee any like
you're not going to guarantee any meetings at first. She's going to do the work in your case,
man. Obviously, this is highly dependent for him just because he has multiple clients now.
So he has the ability to qualify like a bit harder than most people. So in your case, man,
I would qualify them hard. I would take some money. I would like I would have some cash
collected just so I can run them the campaign. I would frame it as in there's no guarantees.
We're just going to have to test this and we're going to adjust accordingly.
Now, who to target HR and LD versus CEOs? I would target HR and LD leaders just because they live
the problem. In my experience, working with these type of companies, CEOs aren't close enough to
feel the pain of bad communication or coaching gaps. They just see the symptoms like bad
retention or team drama. Okay. So to niche it even tighter, what I would do is I would just target
L and D managers, HR, business partners, and also target people development roles. I'll target
talent development roles and I'll also filter by companies hiring for head of people leadership
development, right? Just because it shows that they're investing in culture, which means it's
what? It's a pain sniffing tactic. Okay. Let me give you guys an overview of what that means.
So essentially just think about it. Whenever you have a client, ask yourself, what is the service
that my client offers that a company would hire someone in house for?
So we know when a company is hiring for sales or BDR and SDR,
they want more sales conversations, which makes it our ideal customer.
If a company is hiring for a copywriter or SEO specialist, that means they need help with SEO.
If a company is hiring for a marketing manager, that means they need help with marketing. If a
company is hiring for customer success roles, they're missing the main points, which is
they already have clients that churn and they're basically using that energy to hire someone
that's going to handle that churn and basically have a better onboarding.
But the real problem is they need more clients. Another thing is if a company,
let's say you have a client that provides an AI solution, a good signal to look for companies
hiring for AI roles, AI development, AI developer, AI software engineer, maybe a prompt engineer,
maybe an LLM engineer. So there's a pretty good chance that if they are hiring for that job title,
that they might be open to that service that your client is providing.
So again, it's a pain, sniff, and tactic that we do use. So in terms of dream email,
here's how I would frame it because you said, well, we cannot frame it in a way that's revenue
generated. Quick overview. Whenever you are pitching an offer, a copy, working with the client,
always make sure your offer is tailored to revenue and money. Even if the offer saves time.
Just because people care about money. They say they care about time, but they care about money
more. So here's how I would frame it. I would say, hey, quick question. How are your new managers
being trained to lead? Most HR leaders we talk to say the biggest hidden cost isn't bad hiring,
it's promoting someone great at their job, but never training them to manage. That's where things
break. Team morale dips, people churn and perform in tanks. Now I'm still framing it in a revenue
generating way because I'm mentioning like terms like churn, performing tanks. So this directly
impacts their bottom line. And then here's my offer. We built a peer-based coaching program,
now layered with AI role play to help HR and the leaders train managers before things go sideways,
faster ramp up, fewer resaniations and stronger teams. Is this something worth looking at?
See how I frame this in a way that's revenue generating.
So you can pretty much turn any offer. Even like, like I had some pretty hard niches, like
um, there's this company that I worked with, it's called PPC Gems. It's an SEO company
and they provide Google ads. Now Google ads, they don't necessarily provide clients. They
provide awareness, you know? So you need to frame it in a way that that awareness is going to give
them clients, you know? So I hope this helps, man. Um, let's go ahead and tackle the next question.
And it's fine if we go over in one hour. I'll just make sure you guys,
these questions are always answered. Okay. Red flag clients. How do you handle it?
Had a call with a recruitment client today that felt off. She didn't know how many clients she
gets per month, her LTV or any key metrics, even though she said that she's been in business for
10 years. That's interesting. She seemed controlling and unstructured. I'm pretty confident that working
with her would lead to burnout. I'll be dead. Do you usually just cut off these conversations?
How do you spot and politely disengage from clients like this? Now, again, in your case,
you have the ability to disqualify people. If there's someone that's just their first, first,
two, three, even four clients, I would still work with that person, you know, just because I want
to gather as many case studies as possible. Again, now in your case, I would just qualify harder.
Okay. Now the rules I go by is that if a client doesn't know their numbers and isn't coachable,
I'll just walk away just because you can't scale chaos. And if they've been doing it for 10 years
and they don't have a system that just gives you like, you just now, you know why they're basically
like a pain to work with. So the signs that I look for, one, they're vague about goals,
but want fast results. They don't listen or talk over you. They want control without accountability
and they have no understanding of their numbers, LTV, conversion rates, et cetera. And they also
have like, some clients will have like a blamey language about past vendors. So these are the
red flags that I would look for early. Okay. Again, if it's your first one to four clients,
I wouldn't worry too much about it just because you want to get as much cash collected up front,
as many reps as possible. And then you want to acquire as many case studies as possible.
And then you can start qualifying hard because now you are able to do it. Okay. It makes sense to do
it. Payment structure. Should we charge 50% upfront, then 50% after the campaign is fully
ran or just charge the 400% of the upfront fee up front? Well, it depends on how much you're
going to price. So let's say it's going to be like 5K. Okay. Then it makes sense to just have
like a 50% upfront. If it's something like 2K, 1700, 1800, I would push for the 100% upfront fee
that is fully refundable. So I hope this helps. Until you tap into the 3, 4, 5K, then I would
pitch the 50% upfront. Okay. And the cool part about this is you can integrate with PandaDoc.
You would have the full price, which is 5K. And you'd say, hey, 50% upfront and literally like
PandaDoc calculator just calculates everything for you. And the initial payments would be
split in half using Stripe's integration. Okay. So in the contract, it will be said
5K. They pay 2K now, but after the campaign is fully set up and like you delivered the meetings,
they will pay you the remaining 50%. And I've done it multiple times.
Puzzle inbox for instantly inbox for quality. Which has better quality for US, UK delivery in
your experience. I heard puzzle caps at 15 emails a day per inbox. Does that mean we can't run high
intensity ramp up schedules with puzzle inbox? The best one that I found is puzzle inbox. I
wouldn't use instantly. One, because it's pricier and two, puzzle inbox results are just way better.
I would just use puzzle inbox. You don't have to stick to 15 emails a day. Again,
it's just not like this is not something that you should be subscribed to. Okay. 15 to 25 emails.
It's not the golden number. It's 15, you know, it's 15 to 25. Okay. And you can run the high
intensity ramp up. Okay. So just adjust accordingly. Poor delivery to Microsoft inboxes. I see some
members mentioned poor delivery from Google to Microsoft inbox. Lately is the fix is to buy
Microsoft domain inbox from GoDaddy or ScaledMail and warm them for 21 days before sending. Yes,
I would get them from GoDaddy or ScaledMail. When you want to get them off ScaledMail,
we need to hop on a call with them so they give you like a proper code.
The best way just get them off Inboxology, right? It's their high quality and just saves you the
hassle. They do everything for you. They link it to instantly. They start warming up and you just
wait 14 to 21 days and then you begin sending. What's the best way to find the recruiter's name?
You recently use Rapid API to find recruiter's info, their name from job post. Do you think
it's better to use Rapid API or Excel or HTTP for performing Google search or perplexity module on
make.com? The best way to do this is to use Rapid API. Best way. Cheapest, most cost effective,
and the data is more structured. So essentially what we do is we just feed it into AI. AI just
takes in the full name, retrieved from Google and they in match patterns that with the link,
the person's LinkedIn URL. Because what we're doing is we're querying the internet to the
company name, HR slash let's say talents acquisition and we were adding the company name
and we're saying LinkedIn. So again, we're just we're also going to get their LinkedIn so we can
basically make, we can be extremely sure that this is their LinkedIn.
Okay. XI is a bit pricier when it comes to this. I would use Rapid API, super cheap.
There's another platform that's called I believe Gina or something like this.
I believe Sam knows about it. They give you like a 1 million API calls.
Yeah. Sales call flow. Okay, another question. So you mentioned after asking some qualifying
questions about the client's business, we will talk about what we do and show how our system,
but not our best. True. But then we try to sell them right there. Then we will ask,
they will ask us the right questions. Now we will show all the best answers for them. Yes,
that's correct. So what do we, what do you do to, what do you say to sell them right there
after talking a little bit of what you do? Okay. What if we help you get five to 10 book
meetings a call? Do you have a budget to invest? Yes, exactly. Something like this. So let me just
walk you through my sales skeleton. So my flow is not salesy at all. It's more of a consultative
approach. So the first thing I do is intro. So obviously your introduction, right? Which is
going to be building reports, et cetera. Light, friendly, set the tone, confirm the agenda.
And now what I want you to do is ask something like what made you hop on a call or why me early
on? Okay. Just because the best way to give someone what they want is to just simply ask
them what they want. And now we know what they want. So you can give them what they want.
You know, it's a basic sales tactic. You ask someone, what made you hop on a call with me
today? What made you reply to my email? Like what is, what, what do you actually need for me? Like
what is the ideal outcome of the call that you want? So I can basically use that information
and tell you exactly what you need to hear so I can close you. Does that make sense?
So once they answer, you can position the rest of the conversation around that specific outcome
they want. Makes the whole call feels way more relevant and tailored for them. Okay. Just because
when people like they start talking about this and the client will like, or like the lead be like,
okay, that sounds good, but I don't want necessarily want this. I want, I want to do this,
this way. Okay. So the best way is to ask whether, you know, what led them to this point,
like what are you hoping to achieve from this? What made this a priority? Okay. Yeah. It's very
smart. Could you share that on a chat? Yeah. Yeah, for sure. I'll leave you guys everything and,
uh, and like, uh, the, um, the resource library. Okay. Brilliant. Yeah. So like the best way to
just ask someone, like, what do you want? Like, it's so easy. Like, what do you want? I can give
it to you, you know, that's the best way, you know, another thing is like price anchor, which
means early on one to two minutes, just enough to frame the, the, the expectations. So if you guys
know, like my, my, the way I usually handle sales calls is I have the intro report and I go just a
little overview of what I do. And I pitched them right away in terms of the pricing, just because
it acts like, as like a pattern disruption, like think about it. People like the, like leads, they
want the intro sales call. They're so used that someone does the entire presentation and we know
like five minutes before the, the call ends, we're going to talk about the price. Oh, the big, the
big, the big, uh, like question, what is the price? And now they want to like, they, they're, they're
going to be like, oh, the weather or something, like something came up. Now we don't want to do
this. Now I want to get down to it at first, first 15 minutes. Here's the price. Now they will ask
you the right questions. Now I really, now I will really get down into it. Now I have like 30 minutes
like objection handling, you know? So instead of me doing this entire presentation and now at the
end, I'm getting like five minutes before the call ends, I'm just going to pitch you the price. Now I
have to deal with other objections that will just come up, that will, that will come up later on,
you know, let's just make any sense. So what I say is, Hey, most people will work with invest around
X for Y results. I'll walk you through what that looks like. And if we can see if it makes sense,
based on where you at, boom, pressure gone, pattern disrupted. Okay. So this is how the best sales
people do. Okay. So now the pressure is gone. We're, we're not stressed at all. Okay. We're
not stressed. The lead is not stressed. You're not stressed about pitching the price. Boom,
we're done. We're good. Now we can literally like, we can talk. Now we can have that conversation,
grab that coffee and we can talk. Then I go into their business model, pain points, current stack,
goals, et cetera. Now the price isn't hanging over their head. You flip it. You say, cool. Well,
before I explain the nuts and bolts, mind if I ask you a few questions so I can tailor it to you
properly. Again, I'm asking them what they want. Like, what do you want?
So I can give it to you. And then it's time to basically get down into it. Now I showed them
the best version, showing them this beautiful presentation. Okay. Now you can like, you can,
now you can get it into how the system works, et cetera. You showed them some live, et cetera,
but it's tailored to their word. Now, you know, you know what they want. Okay. You're not explaining
the features. You're showing how it fits their specific setup and solves their exact bottleneck.
Okay. So it's personalized for them now. So the cool part about this is that it makes you dynamic,
you know? So it puts you in a, this framework helps you to adjust depending on each client.
Okay. And again, I showed them the ROI calculator, right? I showed them, so after we talked the
numbers, et cetera, how much they're spending in Legion, et cetera, I showed them how much
they could be making if I just booked them like four to five meetings. So always frame everything
in terms of ROI. Never mentioned words like pricing. Okay. Investment, ROI, return investment value.
Okay. We never mentioned words like pricing, et cetera. Okay. Because it's an investment.
It's an ROI. It's a return on investment. Okay. And then it comes the objection handling,
which is like five to 10, maybe like 20 minutes. So if they bite, go deeper and start selling,
and it's time to close and set up a follow-up. Ideally, I'd want you to close on the call,
but sometimes it's not going to happen, but I would schedule the follow-up during the call. Okay.
So this is kind of like my framework whenever I
enter basically like a sales call. So again, let me just give you like a couple of details here.
Um, do you use only .com domains? No, actually I use .info.
You don't have to use .com. You can just get the .info, which is pretty much four bucks off Namecheap.
There's really no issues with deliverability. Yeah. Yeah. I'm just giving you the real truth. Yeah.
People who are like trying to make things like complicated, just don't listen to them, man.
Just don't listen to them, man. So here's like how I would actually say it, how I would frame it.
So when you want to price anchor, you would say, hey, most people we work with constantly land
five to 10 book meetings or like book calls a month with pre-qualified decision makers,
i.e. ideal clients. Is that something you'd be open to invest in? Okay. So you'd say, um, yeah,
sure. Yeah, we want to do that, but I don't understand how that works.
Okay. Now you'd give them a little overviews. You'd say, hey, we basically scoured the entire
internet to find people that, uh, are going to be interested in whatever offer or service or
product you're currently selling. And we essentially, the way we do it is we essentially just create
job boards and, uh, uh, we use our own AI internal system that qualifies leads for you. And the end
is a book meeting in your calendar. Okay. And if they say, well, how, how can you guys reach out
to these people? We'd say, well, we send them thoughtful messages that land in the inbox
every single time pragmatically. From there, if they're interested, you transition into pricing,
objections and next steps. So that's when they start asking questions like, how does it work?
How do you get those results? What's included? And what's your green light to now
reveal the full smooth confidence? Okay.
Uh, sir, I have a little question, please. Uh, for the recruitment system, we scrape the job
boards and we send emails to the hiring manager. Yeah. So, uh, this system could work for any job
seeker, not just for agencies. Yes. Yeah. A hundred percent.
So it could be a system for B2C. Yeah. It could be B2C too. Yeah. Okay. Yeah. That's it. Yeah. No
worries, man. Um, I believe that sits in terms of the sales call. Okay. I got a positive response
from a prospect enriched. I enriched using X up. I copied your methods where you were targeting
companies that recently launched a new solution. Of course you're going to get our positive
requirements. Uh, he agreed on a 10 minute phone call. What should I talk about in that
10 minute timeframe to close them? Let me read through this copy.
If you're focusing on cute, oh yeah. Hey, this is a winning copy. Actually,
of course you're going to get a reply. Yeah, this is, this is great. Um, yeah. So 10 minute call,
same thing that I've mentioned this and this, uh, and this, uh, and this call today. Um, I believe
since the, just thinking about it, like, just let me give you guys my thought process. So they
just launched a new product. They probably don't have time to go on like a full detailed sales call.
So again, I would frame it and benefits over features and I would front load the value up
front. So I would say, Hey, we can get you. Oh, okay. Here's what I would say. I would say, Hey,
since you guys launched a new product or like a new solution, um, how about we get that solution
in front of ideal clients or ideal customers that would, that would be happy to buy from you
and implement your solution as soon as possible. Right. And typically, uh, we've priced this at X
and our clients see a return investment of Y. And, um, if that makes sense, I'll just send you,
send over a proposal and an invoice right away. If it's thumbs up, we can just go ahead and get
started. You know, I'd always get that. I would front load that as soon as possible, you know?
So make it like a, like a, make sure it's brief, just because this is going to be a founder that
they just launched a new solution. They're pretty bottlenecked, but again, you have a pain points
that you can press since they just launched a new solution. What is the most cost effective
way to acquire clients? I want you guys to just put it in the chat. Okay. That's a very helpful.
I will call him straight away after. Perfect. Like what is the best way to acquire clients?
No, it's not reform. No, it's not paperclip ads. Yeah. It's called email. It's cost very, very
little to send an email and you can, and the ROI is basically like unlimited.
So I remember my first sales call, um, back then, I remember I, uh, I had like this lady,
which was, uh, it was a marketing agency. And you know, uh, as I, when I first got started,
you know, you still got nervous, get nervous, et cetera, et cetera. And you don't know how
to really navigate these sales calls. And I remember, uh, I said something that I thought
it was normal, but she got really, really pissed off. I said, so I was like asking her question.
I was like, uh, well, how do you guys get clients currently? And she was really proud of her job
at Facebook meta. She was like, Oh, I ran Facebook. Uh, like I worked at Facebook, et cetera, et cetera.
And, uh, well, I made majority of clients. We have our referrals. And I remember like
unconsciously, I said, well, when you only rely on referrals, that's a pretty big sign that you
don't have a business. And I said that like very like unconsciously, and I didn't mean any harm
or anything. I just said that. And, uh, it was very like, uh, very natural. Like I did not,
like it was very authentic, you know? And she got really pissed during the sales because she was
like, what do you mean? We've been doing this for years. And the cool part is I eventually closed
that call. Yeah. Um, when do you think this market strategy will get saturated or you don't think it
will? Uh, man, I think this will never get saturated just because think about it. Will a restaurant
ever get saturated? Will people ever stop eating? Never. Right. I just think about it. There's like
hundreds of startups every single day, just in the U S more companies are trying to implement AI
and automation and sales automation, et cetera. So it's even like, it's even going to be like
as many is going to like, we're, we're going to have more clients coming in, right? Just because
these companies will always need clients. That's the, that's the beauty about sales
is that it's very straightforward. You're not trying to come up with the next crazy solution
or like some crazy, uh, SAS that's kind of innovate or like this product you connect
and people that will pay other people money. That says,
so when you go to the restaurant, like restaurants are always going to be here forever.
Until the end of the earth, you know, it's because people always be always want food,
you know? And in this case, if you don't understand what I mean by food is clients, you know?
Um, okay. These are the last two questions. I believe we went over.
Yeah. 20 more minutes. It's fine. I'm scraping crunch base from my clients.
He says he doesn't target founders. How can I find the company's, uh, the context of hiring
managers on crunch base? There's a different section in crunch base and find, uh, contact
is blurred out. Company based only chance for his name is their way around this. Um, let me check.
Yeah. Yeah. You need to click on star free trial. First of all, and you need to click on actively
hiring, which is going to be, uh, it's going to be on top here. I cannot see it here.
And you just type in in the actively hiring has contacts, hiring manager.
But, um, I wouldn't, I wouldn't asylum myself to just crunch base.
I would also scrape Apollo and I would also scrape LinkedIn sales now. So diversify your lead
sourcing databases. Okay. And, um, pretty sure like there's a way you can paint sniff,
you know, you can find like a tactic of a company of a company signal, you know,
and you target hiring managers off that I'm not really sure what the clients offer.
So I can't really give you like a definitive answer, but you should, if you just shoot me
some couple, like a couple of bullet points in the community, I can help you out.
Um, how do you run so many operations and make, do you have a bunch of accounts? I have 40,000
operations a month for $29 odd plan and nowhere near enough. Is there a code for this?
This gives, uh, much more. Yeah. It's actually a joint secret.
So, uh, did you redeem the 200,000 operations of showing secrets?
Oh, so you've completely like, uh,
so you finished that plan.
Yeah. I mean, yeah. If you have a models with 2000 names, it's great. That's 16,000. Uh, yeah,
that's true. Um, I believe this is why you moved to an 18. You can create multiple accounts,
but, um, the only, the only reason I'm using make is just, you know, it's just easier for me,
you know, like it's just, uh, easier. Like I don't have to like, because I've built my entire, yeah,
I've built my entire business on make. So for me to like, just change all my system to any 10,
it's probably going to take me like two weeks, you know, just why I'm using make,
but I'm pretty familiar with an attempt. So like a course of anything might,
might be coming soon. May or may not.
Okay, guys. So I think we all were like, we answered all of the questions.
We've already passed one hour just making sure every, well, every question is gets answered.
Um, so let me just read through the last questions here.
Followup on domain. How about organization in boxology? Organ, uh, domain selection tool is not
very helpful. Refuses to show that info domains. Yeah.org. They could still work,
but not info is just way superior. Now again, like, uh, it doesn't matter. Like, uh, unless,
like I wouldn't just use X, Y, and Z, those domains. Those are sketchy. If you're using that
info.co, uh, or like even that me, you know, these could work, but XYZ is just a little bit sketch.
Uh, finally I'm getting positive replies, but clients are not booking the meetings. I would
look at the way you are basically replying to these leads. Again, like this is my followup
strategy. When someone shows interest in my campaign, I don't say, Hey, I won't, I won't
send them a link. Never do that. I would send them, I would say, Hey, thanks for getting back
to me. First name. I'd love to set up a time to talk, which day works better for you. Eventually
they appreciate that more and they say, Hey, okay, sure. Um, I'm available on X and Y. You'd say,
okay, um, yeah, I can do this. Um, do you have a calendar? Um, do you have a calendar? If you have
a calendar shooter over, I'll book a Google meets or like a zoom, or you can use mine. If it's more
convenient. Now I would send like, give them the, the link, you know, just because it's way more
natural. Let's imagine you talk to someone like, like think about it. Like when you meet someone
in the street and they're interested, you don't say, Hey, okay, here's my calendar. Say, okay,
when is a good day to talk? Cause you're caught, you're being like cognitive of their schedule,
especially when you talk to founders that start business owners. Okay. So always suggest a time.
The best way to apply to an interested leader is by suggesting a time. Okay. Even if you're,
it's your clients. Um, how do I get more comfortable on camera for my YouTube videos?
How do I explain better? Yeah, that's a really good question, man. I think you're just going
to have to get into reps because the first videos are going to suck. So you want to
use something called low friction setup. The first, first YouTube, YouTube videos,
because I'm assuming you want to use that to get some inbound leads. Yeah. So, um, the first videos
are going to suck. We just have to understand it. And, uh, every, like I want you to record a video
every single day, every single day. You want to put as many reps as possible and eventually you're
going to get better at explaining things, you know? And, uh, yeah, I would always recommend you never
worry about editing all of that. Just because in order for you to keep the same output every
single day and not burn out, you need something called low friction content, like a low friction.
If you guys like noticed my content is not edited at all. I will never edit my, like, I would not
add those flashy, like, like this cringe, uh, like, uh, sound effects of like, just because like the
audience that I'm talking to, people that watch me are people who want to like learn, like I don't
want to over one people with this ADHD, like crazy edits, et cetera. Then I get emails from editors.
They're like, can I make your videos better? And I'm like, no, like I want this long, long form,
unedited, just raw, like pure value explaining. Like I don't want to retain your attention by
giving you like edits, et cetera. Like, like the people that I, that want to learn and implement
the strategies are going to like, are going to follow along. Does that make sense? So you want
to optimize for people who actually will learn and eventually explaining will get better, you know.
Even in, on my 30 years video, I still suck sometimes. Yeah, this is called feedback loop.
So when you record a video, you don't just record like blindly, you'd say, okay, I'm going to record
30 videos every single day. You would record a video and you would hear yourself. You watch
yourself and say, okay, I messed up. I just shouldn't be saying this. Okay. I need to,
you need to have like a feedback loop and like, keep improving. Not just making like a, because
you know, you're past that point of putting in the reps. That's good. That's cool. You're putting in
the reps, but now you need to improve that, those reps. So every single day you say, okay, kind of
make this video better. Okay. In your case, because you want to get inbound leads from your YouTube
videos. Okay. So always make sure you improve. So that's my two cents. I hope you guys had a
value in this weekly call. I believe all of the questions are answered. So hope this helps.
Yeah, no worries, Joel. Yeah, it was a great call. Thanks. Thanks everyone who came today.
Thanks Ahmed. Thanks Ahmed. We have two Ahmads. Thanks Fabiana. Thanks Daffy, Harsh, Jeff, Max,
Sam. Make sure you guys please give Sam his flowers. I have never seen a smarter human being.
Like he's like one of the best members of like we've had in SSM. Think about it. He just built
our in-house tools that significantly just lowered the cost for everyone. Like any mail finder is
like 400 bucks a month. Now we don't have to pay for anything. Like literally that's like doing like
there's no community that offers this. So thank you, Sam. Thank you, man. Make sure you give him
his flowers all the time. And thank you, Victor. Thanks Joe, Sven, Simon, Joe. We're having new
people here. Happy to have you guys here. Love you guys and I'll see you guys in the next
weekly call. Okay. Cheers. Bye bye. Cheers.
