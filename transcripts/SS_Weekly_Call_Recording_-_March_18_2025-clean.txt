Got baby in it
Let's just admit
Everyone right now and then now we're gonna go ahead and get started with this
Okay, so what I'm gonna do now is I'm just gonna share my screen and
I'm gonna go ahead and answer you guys's questions
So the way this works for you the first 45 minutes. I'm gonna answer all the questions in the thread and then the last 15 minutes
It's gonna be more interactive in the chat so I can just answer you guys's questions
live, okay
So, let me just share my screen
There you go
So, let me know if you guys can see everything
Everything is going great
I
Can you guys see the screen
Okay, wait, let me remove the chats from here
And I believe all people now are here, okay
Okay, perfect. So let's just go ahead then get started with the first question that we have so Victor
He said what is the best value proposition? Yeah, that's a really good question, man
I have been taking some notes just to answer your question clearly. So
What I want you to understand is that the best value proposition you can use in any
Cold email copy or any cell system your own cell system or any client cell system has to answer two questions
The first one is how do I make their company more money?
And how do I make this specific person that I'm reaching out to is life easier or better? That's all right
So the best value proposition is just a clear specific offer that just answers these two questions. Okay, so when you're writing your email always
Think and writes from the perspective that how can I make this company more money?
Right, and how do I or how do I make this specific person that I'm reaching out to?
It's life easier or better. Okay, so this is like most straightforward way to think about it
Okay, so whenever you write in a copy whenever you are to when you're copying for you for yourself or any clients
Whatever whatever the offer is. Okay, make sure to always frame it in a way
That's gonna make the other party make more money. It was gonna make their life easier
Okay, so for example in our case as it sells some agency, right? How do we make their company more money is by
Connected them with ideal clients that are willing to pay for whatever their offer they have. Okay
How do I how do we make this specific life person's life easier is by automating their entire legion instead of them?
Basically manually trying to reach out to those people and
Relying on you know outdated methods. Okay, so this is what the best value proposition is
Okay, so how do I make their company more money?
How do I make this specific person's life easier or better?
Once you start thinking from this point of view any offer just becomes easy
okay, and it's gonna like it obviously you're gonna have to put on some reps right because
The first email that you're gonna write is not gonna be the best email, right?
So as you go in and you you know, write a couple more emails, right for multiple clients your own cell systems
It's gonna it's basically gonna become second nature. Okay, so just keep that in mind
Okay, this is like this the most straightforward the 80s
I call it the 80 20 of the 80 20 of the value proposition. How do I make their company more money?
How do I make the specifics person life easier or better? Okay
so looks like
Have to admit a couple more people here. Hey Andy
Okay
so the second question we have is
How long should an email be I'm currently trying to balance between adding as much value as possible while also not adding
The email copy while not making the email copy too long. Yeah, that's a really good question, man
So my general recommendation is don't go over 200 words. Okay, so
Don't keep it under 200 words all the time and use clear simple language with no
jargon, okay, the goal is to basically
speak your mind and speak the offer just
Clarify the offer in a way that you say it's in the most straightforward way with no jargon
You know, it's kind of like when you try to make a point with as little as as little word as possible
Okay, so he's a clear simple language with no jargon 200 words only focus on
Exclusively on the lead. How do I make their life better? Right? How how can I make the specific person life better?
Again, we're going back to the value proposition. It's all tied up in one thing. Okay
The last thing is benefits over features, okay, so
Our systems are crazy, right and they are personalized. It's that set up
But whenever you are trying to close the clients or whenever you are
Talking to us prospects on a sales score or like
In emails, right when you're sending an email always benefits over features
No, they like people don't care about the features. They only care about the outcomes. Okay, so sell them the outcome
That's all that matters. They don't care if it's we're gonna scrape Apollo gonna scrape crunch base use exa use this prompts the other prompts
They only care about am I gonna get you know clients down the line 12 to 16 weeks ago 12 to 16 down the future
Yeah, so there you go
So benefits over features always focus exclusively on the lead use clear simple language with no jargon. Keep it 200 words. That's it. Okay
So this question right here by Simon looking at your recent YouTube videos and recent instantly replies
I believe you're talking about the AI inbox
Auto reply I assume you recommend if one a creating a cell system set of scenarios and then Bolton on the replies automation
Dropped on the other day of an extra level
Hmm, I assume best to do that in multiple scenarios rather than the same. I'm unsure what limitations are on make. Yes make
makes timeout is basically 45 minutes and
Obviously the bigger the scenario the more
Troubleshooting gonna have to do so. Yeah, I would split the system in half
I would basically split the main juice, right and then I would just send it to a webhook and it's pretty easy
You're just gonna have to send an HTTP requests to an address in make
So you just go ahead and create a webhook, right?
And that webhook is just gonna catch a response using the make HTTP requests and then you can continue the flow
I've done it multiple times, especially in the
the
Growth system that we have in the classroom
So I like a make lock on system that basically cleans company name clean job titles hiring for SDRs
And Sarah and then send that finalized data into another scenario. Then again, we can send it to
Instantly, okay
Just because sometimes when you use mails at SO or when you use any mail finder or email any email enrichment platform
That takes two to three seconds
You can split the system in half and then just send it to another system and you can continue sending
data through webhooks and like
infinite
Scenarios right in make and how to gauge whether to split scenarios and how intensive is in there?
So, you know better you will max out. Yeah, so whenever you go over
Eight
Scenarios, I would split right so eight is the max so eight modules, right?
I would split and then send to the
to the
Following scenario. Okay. Again, it depends if
The requests that you are basically querying is taking like two to three seconds
Just like mails at SO or maybe any mail finder or like you have other things, right?
Maybe you are generating
Like one personalization for the first email second email, etc, etc. Then you might consider just splitting that. Okay again, it's really like
Scenario specific
What when do you recommend using a sleep as function of if splitting several scenarios? Yeah, I always use split asleep scenarios
Whenever I'm you know
enriching etc because it's gonna help you
With the time limits whether you are querying API like mails at SO or any API really so yeah like
Three to five seconds right it's gonna help so hope this helps man
Let me know what specific system you're talking about so I can just go ahead. Maybe I can record a video for you or something
Okay
The next question we have is could you please elaborate on the why and how what of the custom tracking system? Yes
So custom tracking domain or system and instantly you also set up your private VPS with orchestration software to host emails on
Kind of what you're using Namecheap private email for I believe yeah, this is a both really good questions, man
Okay, so let me see if there are other people that are being admitted
Okay, so looks like it's good
Okay, so the first question could you please elaborate on the why how what of a custom tracking domain is in instantly? Yeah, man, so
a
Custom tracking domain simply it just makes your emails look more trustworthy and professionals right so
Most people say you shouldn't add it or you don't have to bother like I can't stress this enough
Always add your custom domain tracking it instantly always because it helps your emails get delivered
Better and not get marked as spam and the way it works is that once you create a sub domain for example like track
Or for example, it's like ins at your domain comm which is for instantly and pointed to instantly it links all your emails
Right. So all the links in your emails will show up as coming from your own domain
So it's kind of like instantly forwards
This ince your domain comm from instantly to your own domain not a third-party service
It also improves how your emails are being viewed and tracked by ESP such as G suite and Microsoft and others
Okay, so always make sure you add a custom tracking domain, right?
It's just it links like all the links in your email will show up from as
Coming from your own domain not from instantly
So basically instantly just forwards them and it make it seem like it's coming from your own domain
Especially when you are sending warm-up emails, okay
So the second question which is interesting
So what I would recommend is just instead of using a VPS because I know many Legion agency that they tried
And they failed. Okay, it is first one. It's complicated and I mean think about it
Like Google and Microsoft's are the big players in the game for a reason, right?
So they have massive infrastructures everyone like everyone just uses G suite
Okay, so they've been they've been in a game for a long while and you know
A private of EPS doesn't really compare when it comes to scalability or reliability
and I'm assuming most people here wouldn't be wouldn't want to like
Deal with that because it's just gonna be a lot of friction and even like a lot of Legion agency that have the budget to do
That they fail miserably. Okay, because you know these services like G suites, you know Microsoft
They have like their own internal like systems that just work. Okay, so you cannot we can't compare with
Google or Microsoft or even Yannis or even private email, okay
So just use G suites a VPS
It's just gonna cause a lot of problems down the line and I would recommend it man
Yeah, just for just to save you a lot of headaches down the line. Okay
So we have another question from Vishal. Yeah one big question
Have you ever took a look into AI cold-colon? Yes Vapi and so on was looking into it
To dial booking rate is 4% and more look at very interesting. Yeah, so if you guys don't know Vapi is like
Really good AI
voice agents
So essentially if you ask me this question six months ago, I would say just discard this idea but like
They really sound human
Maybe we can try it. We can give it a try and let me know if you guys can hear
What's inside so I'm just gonna click on give it a try and you will see that it really sounds human
It doesn't sound like AI at all. So let me just give it a try and let me let me know if you guys can hear everything
Welcome to vapey. I'm an assistant that you can talk to like a person. So how's your day going?
Yeah, thank you. It's going well
Awesome to hear I'd love to dive into whatever you want to know but before I do
Would you mind telling me how you found out about Vapi?
So a friend of mine called Vishal just actually mentioned that in my community
Cool, thanks for sharing. What would you like to know about Vapi? Yeah, can you guys hear everything?
Well, kinda I can't hold it but I don't record it it really sounds human then
It's thanks I
I appreciate it. Glad you think so
Yeah, so
Yeah, that's amazing like imagine you would have so here's my like my idea and how to use it, okay
So, thank you so much
My idea here is to have like a personal knowledge base of your entire company
Basically, you would have like it's probably gonna be trained by an SDR or like someone who has knowledge about your offer
basically
It knows everything about you, right and then you just download that cheat sheet
right to this knowledge base of Vapi and then you're just gonna have all the cold cults for you then I
Assume it's gonna have like more than 4% booking rates. Alright, so this is like the next level of
Cold calling and it's even so if
for example
maybe if
Down the line we can use it as a salesman agency just like for an experiment, right? Like
We've gone a call with someone and then we'll just record it and then we just let Vapi just talk
Just for testing purposes, we're not gonna aim to close obviously
But I can see it's being like a super powerful way to close man, like it's just because it's the it just sounds superhuman
right
Yeah
In your case, man, I would definitely implement it. I would definitely implement it
Just because I think you have the resource
Resources inside like about your company etc. Right? I believe the company is Aurora
So we shall has like it's called Aurora and he has like multi-channel outreach so he does cold calling he does
call email and he also does
LinkedIn outreach, right? Just which is super powerful, too. Okay
So yeah, man. Um, yeah Vapi is great. I would also I would really recommend you
Implement it just because but keep in mind
It will work best if you have multiple booked meetings already
So if you have only like a few a week
Let's say like three or four a week
Then I would recommend just go ahead and like, you know get in on to the sales calls yourself
Well, but when when it's like, you know, it's like ten meetings a day then I would just do that
I would just use I would just use a Vapi and it also works for cold calling. Yeah
So hope this helps man, let me know if you have any more questions just send a make a poster like send me a DM
Well said can you do a walkthrough on how to set up your system in the most cost-effective way with the most up-to-date methods?
Yeah, a hundred percent man. How about I just go ahead and do it live for you
So what I'm gonna do is I'm just gonna go to make
And
Like assuming you have a lead list already
Okay
so the system is gonna be basically scraping Apollo use an epiphyte and let me walk you guys through a
Scrape that I just scraped this morning just to show you that Apollo scraping like works like everyone the same that
Apollo is gonna change their infrastructure. It's set up
That it's just sort of BS. Okay, like scrapers are gonna be here forever, right?
So I just scraped 2,500 easily right and this is just a free account on Apollo
Just a free account. So assuming you have a you know, a scraped data. She's gonna go to storage
copy go to make
And I'm gonna create a new scenario it's gonna take us five minutes, okay
So just go to this plus button right here and go to epiphyte get data set items
Just paste your data set id here
Limit just put one for one sample data click save and run once
Okay, great
Click on this plus button either use mails data. So zero bounce million verifier or this one that i'm playing with
Currently, it's email validation, right and then just map the email address
easily
Click save
Create uh, now we're gonna do is just basically clean the company name just use entropic clod or any
ai model
Click add a message
user
content type text
And then just go to the classroom. We have a document called seven figure prompts and then just go to
Company name formatter and it's gonna take us five minutes to do it guys. So that's how easily you can start a campaign
So just company name is always going to be organization name. Okay, so search items
Company domain so the company name so company name is going to be organization name
and domain obviously
Is going to be the result of
Domain so domain
So domain is going to be right
Here and headline is obviously going to be the headline of the person that we just scrape off as the name
Headline is obviously going to be the headline of the person that we just scrape off epiphy off apollo using epiphy and it's going to be
Headline headline headline headline
Let's just search items. So there you go headline
Person's bio or headline and just map headline click save. Okay, and then uh, just run once
So there you go, we clean the company name add the little filter here condition
Reason not reason actually it's states states equals deliverable
And click save now generate personalization using clod create a prompt easy go to seven figure prompts again
Pick one
Uh prompt that you like. Um, i'll just use one for ease of use from here. This one is crazy actually
It's really good. I'm just gonna copy it
Go back max tokens 3060 click on add message
user content
Type is going to be text paste the text here
And we forgot to add one more thing which is going to be adding exa. So exa
Perform a search and let me put you guys on game
You can scrape linkedin pages of the company or the person's company using exa
Exa just bypasses all the linkedin bullshit. Okay, so if I just map
The person's linkedin number of results just one so exa
It just doesn't scrape just the website
It scrapes anything like literally because they have one of the best scrollers on the internet and click on save
Now let me run once
And there you go
So text
There you go. So this is their linkedin page. Now you can't scrape a linkedin page
Just using the make htp request. We're going to use exa
So now you can basically just either scrape the website page or scrape the linkedin page and add the text
Okay
Just add the text here
And add first name pretty easily
And it's going to be the first name
All right, and then click on save and then just save this auto align this run once
Using exa scraping generate personalization and then let's just read through this maybe we can fine tune it in real time
Okay
Okay, congrats on scaling calmigo from concept to market ready neurotox solution adi
The mental wellness space is exploding but new founders successfully breached the hardware software gap while maintaining clinical credibility
Would love to connect about cell systems that could help you reach more of your ideal health care planet partners. So there you go
This is what I mean by a good
personalization and
We have another question by zach after that's going to explain this personalization. So, uh, are you guys following along with the
Everything is going well here
Okay
Okay, great so last step obviously is going to be
Okay, amazing so last step obviously is going to be just adding to you instantly, okay
Okay
Add lead to instantly
And that's pretty much it took us like five minutes go to campaign
I'm just gonna map a client's campaign
Um, i'm just gonna go to campaign id campaign test. For example lead email
Which is going to be the leads email, right? So email first name is going to be just
The person's first name which is pretty easy first name last name company name is going to be the results
Which is where's the results?
Um, wait, did we clean the company name?
Yeah, we did
Oh, yeah, here's the company name and personalization is going to be
The sex response and click save and now basically this is like what I mean by a really good system that took us like five minutes
So now all you have to do is just change the data set id all the time, right?
And it's just going to be consistent. Okay, that's pretty much it
That's like one of the most easy like the easiest straight more straightforward way to build a list in like five minutes
It's gonna take you
20 minutes to scrape apollo build a list just paste the data set id here and just start right
So, I hope this helps me this is like the most straightforward way which I mean
I mean like doing things that don't scale. Okay at first
Don't do things do things that don't scale. Okay first use this system right to get
First clients, right? This is like the the
The main juice. Okay. This is like the most
I would say the the basis right? So the basis is this system. Okay, always just use this one
Okay, it's gonna help you a ton like from zero to
15k a month
You can just use this okay
just purely off apollo or you can scrape other platforms such as linkedin or crunchbase and
The only thing you have to do is just change the data set id and just change the data set id
So whether you are using for example, i'm using here apollo. I can just go ahead and scrape crunch base. Okay, so crunch base
And it's gonna be
This one yeah
I believe it's this one. Um, actually it's not this one. So it's crunch base
URL
Yeah, this is the one
I believe this is the one
hmm crunch base crunch base crunch base, um, it's
By curious coder
So there you go, this is the one ultimate crunch base or scraper you can scrape up to 7 000 leads with this one
So once you scrape again, you're just gonna go to your runs and basically just
Copy the data set id again remap the connections and just add leads to instantly pretty straightforward. You're validating the emails
Okay, you are performing a search so you're having a great personalization and then you just add your leads to instantly again
If you're using, you know other platforms such as linkedin or crunch base, you're not going to validate the email
So you're just gonna go to any mail finder, for example
Search for a person's email
Unlink
And that's pretty much it
The results again condition is going to be
Email exists
Pretty straightforward again, you're gonna have to map everything and there you go. So I hope this helps man
Um, yeah question on this side. Are you directly pushing the messages to insulate you are not tracking that in any database
So that is that incorrect?
Yeah, you can so yeah, obviously you're gonna have to you know, um
Add all of your you know, uh urls that you scrape off apollo and actually
Let me just show you guys a like a live client
So
Let me show you wait so um in the morning I scraped
For this client right here. It's one of the best clients i've had
It's called companion
And all I have to do so basically lead scraped. I just have search URL one search URL two, okay, and then I have
Hiring for copy and content. So this is another search again. I have companies digital marketing agencies here
I just used xa. Okay, and let me walk you guys through it just because uh,
It would be helpful for you guys
So there you go. So for example, I was just using xa here. Okay, you can use any platform
Here's 1 000 company here and then 1 000 company
Right here. Let me just okay. There you go. So
Company name company name and here's a little hack for you guys
Use perfect search in your coupon when user in xa and thank me later
And uh, yeah, that's pretty much it
So, yeah, um, this is kind of like a lead list that I scraped too
There's just cleaned company names etc. So your question here, how long does it take to personalize 5 000 leads?
Takes I mean if you have if you really like put
A few hours it's gonna take you like four to five hours, right?
Because the system is automatic
The only thing you have to do is just basically scrape add everything air and just handle the troubleshooting make
So you would send either like 600 by batch and offset the second six six hundred just so you don't get rate limited
Or you can add like a sleep module right here, which i'm gonna do now, obviously
So I can add a sleep module right here so
A sleep
Module here that just sleeps like two seconds or three seconds
Okay
and again one one more thing is what i'm gonna add a
Break for claude
I'm gonna add a break for claude and under i'm gonna add also a break
For xa and make sure always
Click on save and in order for you to get access to the break module always make sure you go to scenario settings and it's
Gonna be allow storing allow storing of incomplete executions and click ok
And then just auto align this and there you go now whenever there's like let's say you get ray limited with xa after like
900
Results, it's just gonna pause and then it's gonna continue where it stopped
So I hope this helps
Okay
Again, you question andy there's a tracker in the classroom
Okay, let me go to classroom and you can see it
If you go to um
12 days to build yourself some agency lead scraping
So here's the video explaining how to track leads. Here's your track lead tracker
And it's basically super straightforward. I'm gonna put the search for our own
Date scraped number of leads scraped. Okay, this is how you track it
So I hope this helps, man
Yeah, no worries, okay, so
This is getting a lot. There's there's there's a lot of uh
Tabs here. Let me just remove them so you guys can see everything
correctly, okay
So yeah, well, this is uh, the answer of your question. So I would use method one, which is basically what i've done now apollo
the only thing you're gonna have to change is just change the
Um the platform you're gonna scrape okay, and then just change the data set id every single time
Uh, what are the principles of good personalization? Great question zach. Um, the principles of a good personalization are the following
The most
Effective personalization comes from inference and inference just means there's something just I came up with
um
Probably like eight months ago. So taking public information and connecting the dots to make an informed
Value adding observation about someone so an example would be a non-service level observation understanding the prospect's unique situation
And you can relate your service to their specific needs which is why I came up with the prompt that everyone is using here
Which is craft a personalized are we shoeline icebreakers based on the thoughtful inference? Okay?
The message should be directed to a potential client using publicly available information to connect the dots again. We're saying
Okay
Relate these insights to the service being offered. Okay
so
A really shitty personalization is hey saw your recent linkedin post about copyrighting. So this is not like it doesn't really matter
Okay, a good personalization just relates your service
to whatever
Uh data you're gonna scrape. Okay, so sales systems, okay
Okay
Again all of these I would just discard them and the cool part has mostly gen agencies and anyone is that that's the one outreach
Has the same level of personalization just like this. Okay, like you'd be surprised
Some even don't use personalization. It's how easy for you to just cut through the noise man
Okay, so these are all the questions that we have in this thread now we have
20 minutes more 25 minutes back and left in the call
So just go ahead guys and just
Posting your questions in the chats and i'm gonna answer them in real time
And it's just gonna be more interactive and fun. Okay
Okay, let's see let's let me read through the chats and see if there's something that I missed nothing here
Oh, hey sam. What's up, man?
Okay
Shitty personalization no more. Okay. Okay. Uh, jason said i'm running two campaigns for different regions u.s
And uh in canada and u.s
Same copy including personalization same niche just different leads one campaign is doing well with responses and the second one
The second campaign has nothing drastically different. Do you think it could be just
Lag in one campaign versus the other there's multiple aspects to this map. It could be lag. It could be that
It's a region issue i'm guessing the u.s. One is doing well and the canada one is not doing well
Other way, yeah, that's interesting
Yeah
Hmm. So same copy same niche different leads. Yeah, it could be different. It could be like, uh,
It could be you're not reaching to right people
So different leads i'm assuming
Okay, can a 47 percent reply rate wow u.s. Nothing. Hmm
What is the niche you're going for
Recruitment of course, yeah. Yeah, that's interesting. You know why because most people are not
Targeting recruitment in canada. So maybe you are the first one who actually like i've never targeted canada actually using recruitment and i've
targeted
A lot of recruitment companies in my career
Yeah
Yeah, just make a post about it maybe we can just tailor that to
Make a post about it, maybe we can just tailor that to um
To to this the u.s
Campaign, maybe we can tweak your copy
Yeah, just make a post about it in offer audits and i'm just gonna come in
And then i'll help you man
Regarding the private email on namecheap from which ip are they
Are they sending what is your experience with that? I'd be very i'd be worried that will hurt reputation
Here's a hack for you man back then when I was broke and I didn't have money to pay for emails
I would just use a private email and it was great and um, I would just use
Here's a hack that i'm not gonna tell you on other than the community
You can sign up to different accounts using namecheap and sign up
And they're gonna give you three email inboxes for free for two months. You can start your outreach get a client and then pay back later
And in terms of deliverability no issues i've had really good results with a private email just because it's untapped
A little bit underground not not many people actually just use private email. They buy the domains off namecheap
But they use g suite
microsoft
um
yeah
Hey sod, uh, did you try my linkedin free scraper? Yes, man. Actually you are crazy
You are one of the best
uh, if you guys don't know
let me go to
This thread right here. If you guys don't know sam is like
it's one of like uh
like it's he is very valuable to our community like
Whenever I am basically doing my community management every morning
I always see him providing so much value
like
Like uh men you are a machine and he just came up with the scraper for linkedin company.info
So it extracts company info company description recent linkedin post number of employees
And let's just try it
So this is the code
And you okay so you are
You're making a get request to this right here
URL actually just try this now
Man, you keep exceeding my expectations
Let's just try it
So click on this plus button go to make a request which is http make a request and I believe it's a get request and then
Yeah, so you go to
This url, right copy
Url and there's no heathers
No heathers, okay, you're just gonna go to to query string. Oh, that's uh, that's interesting
I'm curious how long they take you to build this out man. You're crazy
Come work with me my process when building the sequencer
I believe you can name this whatever you want
Is because I have a little bit of background and then uh, that's pretty much it I think
Once
Company data not found. Why is that? Am I missing something?
Oh, it's a company not linked. Oh, yeah company url
Oh, it's just a company so company, okay, so my process
Hi, uh, yes, you need to continue my process with http and everything. Okay, great. So let me just go to my
linkedin
That's interesting then
Uh, how many hours it take you?
Uh two hours last night
Do you have any background of uh coding experience or something? Yeah engineer. Oh, wow
Man
Okay, here's the company
Thank you linkedin
So you need to remove the other uh, the other from uh, the one what what should we add no no
the
Wait, let me send you
Okay
We have a lot of crazy, uh people in the community we have engineers etc. That's uh
That's a very that makes me really happy man
Um
Yeah, any company you're ready with your with your wish, um try this one, okay
And remove the the company url from the query
From the query
Wait, so I should add remove the http
No in the in the http get
You need to make this uh end point exactly
This is this is perfect this end point plus any
Okay, okay try try now
Oh, wow
Um man
Wow
Okay, let me just parse this
Yeah without cookies
Uh, what about rate limit?
No rate limit. No rate limit is no no. Wow
I've never seen this man. Uh, maybe we can scrape maybe we can come up with our own apollo scraper. What do you think?
Yeah, so wow. Holy
Look at this guys company name company slogan
Company i'm definitely gonna pin this right now
number of employees
This guy
I can scrape more info from it and uh, you can
And there is no cookies. So when there is no oh, yeah, there's no cookies. There's no written. Yeah recent publication
exactly what the
What the fuck this is excuse my french, but what the fuck yeah
The date
This is better than this like
Man, just just come up with your own enrichment platform at this point, man, honestly
Yeah
Um, I believe you're moroccan, right?
Yeah, obviously man
You too, i'm half moroccan. Yes. Yes half moroccan. Oh, yes. Yes
Okay, wow, that's that's uh, that's crazy, man
Wow, you can try with my let me try it so
This should be community exclusive man, yeah for sure
So I believe oh, uh, maybe we can let me see data so now I script the entire html, right?
Oh, maybe we can add another
So
Oh, yeah, I forgot i'm so silly, man
There is the company my bad
Uh, just how i'm excited man about this
Let me see. Let's scrape my own process
scrape
Run once on any way
There you go
Company info mire process we build sales systems for recruitment agencies
Uh get a proven system that predictably finds educates and I wrote this exactly probably like two years ago
Company address wow, so you guys know my address now
Recent publication similar companies. Okay, so even similar companies
growth partners down
Man this is uh, maybe we can like have like uh other things i'm curious like if we can um
If we can like have multiple tools like this, what do you think?
Uh, normally, you know the the tool i'm not finding cheap is any my any my any mail fender
Fine. Yeah. Yeah, how about we build our own email verifier? I think it's it's pretty easy to build, right?
Exactly what i'm doing. Yeah an email. Yeah an email verifier and we can say just goodbye any email finder or any mail
Um verifier we can use our own, right?
Exactly. It's easy to do something like wow, man. This is great work, man. Like
this is like uh
Yeah, that is incredible man, like um, that's mind blown great work, man
Great work. We we can use especially the last post from nicotine in the personalization
Yes, we can use their link. Yeah
So essentially what you've done is you have like a very simple way instead of using any other platform
so
how can like
That's like the beauty of being in a community is you there's like people that are crazy like
Sam right that just are really good at what whatever they're doing. It's amazing man like um
Good job really good job, man
Yeah, so
Let's see if we have other things here
There are any other
Questions, so it looks like there are no other questions so we can just kick it here
Okay
Wow, that's that's mind-blowing, man
Thinking about like building like some like apollo scraper just for us
Mickey may maybe may just make it paid for everyone in the in the ffi and then just
exclusive for our community
Yeah, yeah
So after this call i'm just gonna make a post about it i'm just gonna pin it so everyone can see it
and uh guys make sure to say thank you to sam like this is like
imagine like
Like if you want to scrape this
Here is like a step by step on how to scrape it. You're gonna have to pay for phantom buster
Yeah
Make sure you guys
Give sam his flowers. So you're gonna have to go to linkedin. This is my phantom buster account
solutions linkedin
so you'll have like a
You will have to have like multiple phantoms scrape leads from which one
Yeah, linkedin search exports and link. Yeah, linkedin company scraper
And then also linkedin. I believe there's a linkedin company follower collector
Yeah, there's other ones here
I'm not really sure where they are, but this gives us everything
recent similar companies recent publications like
Phantoms would scrape just a company info and then another phantom will just scrape their recent publication and now you're like wasting so many tokens
So this one just gets you everything and I love how he just structures everything in jason
I can just map everything just like it just makes my life so easy now. I can just have like a super powerful outreach, man
Wow
Man, you're the boss. Thank you. Thank you so much
Hi, you're welcome. Yeah. All right guys, so I believe that's pretty much it
Yes
Uh another thing said, uh, one question
Yeah
What what do you think if we we take like 100 of your best emails?
Like they have good respect and I find I fine tune a model
Do you know the fine tuning is just having the best model in earth with specific like doctor sad writing emails for us
So I would give you like a list of uh, like high performing emails
Yeah, for example the high performing emails and I can feed this to cloud to cloud or gpt
And have the best fine tune model in earth
Yeah, then we're gonna have like a like a like similar outputs of those emails, right?
Exactly something like this. So this is like this is essentially what i'm building now as a sequencer
So the sequencer that i'm building is going to be similar to instantly
Right, and i'm gonna launch it for the community for free
um
Obviously other people are gonna pay for it just because this is how it works
But for everyone in the community is gonna be for free
You guys can link your email accounts warm up just like instantly with the best high quality
ips us ips and then I have a sequencer that generates emails using if then conditions
So we're gonna pull data
So what i've been negotiating with exa is that they were gonna give me access to data through their api
And I can just pull data from there and I can just use my if then condition
So if company is not a recruitment is hiring I can just
Feed it to ai and generate me like a personalization of that
Right, by the way, I have made my own exa
But but uh, but yeah, but i'm talking about fine tuning models
Yeah, okay
So i'll send you like a list of really high quality emails of my campaigns that performed well and then you can we can we can build
that right
Okay, okay. Perfect, man. Yeah
Zach said what is the process updating the personalization simply run in the amg system again from the beginning
Is there an easy way to only update the personalization along with it without spending ops or redoing the rest of the system?
Yeah, and then you're just gonna have to
um
I think
I think we can find a way
So for example
Um, you would have a personalization right here. Let me just
discard this
So I can go back to the old scenario
So, okay, let me just delete this
And um, i'm just gonna map the email the person's email maybe I have the website their website you're all
Okay
I don't think I have it so uh, that's fine
So i'm just gonna split
Based of
An at which is an at
And i'm just gonna get to the second one so get
The second one
So I can retrieve the domain, okay
Click save and what you can do I believe
Just on top of my head
Um
Instantly add leads to a campaign
And you can add, uh, I believe it's not
Oh, yeah
Um
So the leads email has to be there
Yeah
Yeah, unfortunately, you can't do that, but it's fine
You can still just add the email again, right?
You can download the you can download the list that you have off instantly
Okay, so download it I believe you know how to do it so like you download it put it in a google sheet add another
Column which is going to be personalization
Okay, and then
Instead we're just gonna have a search rows here. Okay, so search search rows
So there's a hack you can do
So delete this you're gonna have a search rows
So search rows
And it's gonna have the sheets that are downloaded off instantly
Okay, and then you're just gonna generate the personalization
And then you're just gonna have to add it to instantly again, right?
You don't have to validate the emails. You can just add it as a variable. So yeah, you would have like the overdid emails here
So we'd add the email we add the first name last name
Uh, you don't have actually you have to add this the first name. You just add the personalization
Yeah, which is going to be the response here and click save
so now
Um, the personalization is just going to be changing. So once you go to instantly you're just gonna
Paste in personalization and it's going to be updated. Does that make sense?
Perfect
Yes, uh did jonathan release his epic system from a couple weeks ago, yes, um, i'm just gonna
Send him a message
And I believe he has been adding a couple more things
so after the column is gonna
Send them a message and then he can make a post about it. Okay. All right. All right guys, so
Great. Um, I believe that's pretty much it. These are all the questions that we have
Okay, perfect
Yeah, nowhere is jason, yeah always happy to help you guys
And uh, just not not just me like there's a lot of people here that I thank them to sam
um everyone here, okay, so
Yeah
Thanks, sam
You're the goat. All right guys. So thanks so much for coming in in today's weekly call. Thanks. Zach. Thanks sam
Thanks. Tia. Oh, I believe us. Uh, hopefully I said your name right man if I didn't sorry
Thanks, jason. Thanks zach again
Thanks. Um
Yannick, oh, yeah
Uh, yeah, thanks jesse. Thanks sam. Thanks brother. Um, yeah, this was really valuable. Yeah, man. Thanks nicole
And thank you everyone who came in appreciate you guys time
Um, make sure to whenever you guys get stuck or something just send me a dm. Okay, there's no va's
There's no success coach in a community
um
Shots fired
So, yeah guys, uh, thanks so much for coming and um, hope you guys have a
Great week
All right guys, so see you guys in the next weekly call
