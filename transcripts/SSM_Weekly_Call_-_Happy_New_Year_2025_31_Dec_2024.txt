Hello, hope you guys had a great week, how you guys are doing?
Hey <PERSON>, how you doing my guy?
Hello, hey <PERSON><PERSON><PERSON>, good start into 2025, hell yeah.
Yeah, looking forward to it.
Just wait a little bit for everybody to come in here.
Hey <PERSON>, good to see you man, hope you're doing well, hope you had a great week.
Awesome.
Alright, so we have a bunch of questions that I'm gonna go over and then once
we actually go over them together, guys please just post your questions here in
the chat. Hey <PERSON>, are we doing well man?
Alright, how are you doing?
Hey man, how are you doing?
I'm doing great man, hope you had a great week.
So yeah, after we finish up the questions guys, just put them in a chat here so I
can just go ahead and read through them and answer one on one.
Awesome, so the first question, let me share my screen for you guys.
Okay guys, so the first question that we have is from actually <PERSON>.
Now I did a little bit of digging and I'm gonna give you a few solutions basically to
find the least that you want. So I'm pretty sure that the campaign is going to be B2C, right?
Yes, that's correct, yeah, sorry.
Yes, okay. Yeah, so there are three ways on how I would approach this basically.
So the first way that I found is basically going to be the lowest hanging fruit for you.
So the first one that I would do, like if I were in your shoes, what I would do is I would join
like Facebook groups of homeowner communities, right? So once I join these communities,
what I would do is I would use like a scraper, right? So you would go to <PERSON>piphy,
so you'd go to store, and then what you would do is you basically scrape all of those people
in those groups. So these are essentially worm leads. So those leads are already in this group,
so the likelihood of you closing them is going to be much higher than reaching out using cold
email, right? So what you can do, so you can just go ahead and reach them out. So I'll go to Epiphy
and look for Facebook scraper. Yeah, so this is the one, ultimate Facebook group scraper. So this is
the one. So I would scrape all of these people, right? And I would essentially just reach out to
them. So I'll prepare a list and I'll just reach out to them. So this is the first way, right? The
second way that I found is basically using like another scraper using Epiphy, which is going to
be home advisor data scraper. Now the actress is under maintenance just today. Yesterday was alright,
so just wait like a day or two, just because they're performing some sort of maintenance in
the scraper. So what it does basically is just scrapes home advisor, home advisor. You can get
data from here and this scraper actually provides you with emails that signed up in this platform,
right? Home advisor. So there are multiple people that sign up in this website right here where
there's like, you know, you could filter by the exact server that you're providing. There's a lot
like appliances, clean and main services, architects and engineer, carpentry, and people
actually will sign up here. And this scraper can retrieve the email addresses, which is scary
actually. When I tested it, I was a little bit skeptical at first, but then you can find like,
let's say like 40% of the time, 50% of the time, you're going to find the email addresses of people
actually signed up here. Now the last solution, which is the last one that I would use,
is basically using a software. And I don't think anyone in the space actually talks about this
software. They're sort of gatekeeping it. And it's one of the ways that most companies that
I've consulted with get email addresses and start their newsletters. So this software is like a
gray hat, I would say, but most companies actually use it. So I wouldn't worry too much about it.
It's called, it's called Havage. So what it does is basically it's an open source software that
essentially scrapes websites and retrieves something called an SQL injection, which is basically just a
way to retrieve data from a website, just a programming way to say it, right? It's a nerdy
way to say it. You don't have to worry about it. It's essentially a little bit gray hat, but you
don't have to worry about it, like I said. So Havage basically is just a software that retrieves
email addresses, but I've used it multiple times in a bunch of campaigns, so I wouldn't worry too
much about it. So what you do, just input the URL, and then you're good to go. So what you do,
input the URL. So let me try to find the link of download. There are a lot. You can find it in
GitHub. So yeah, I would just use this right here. If you don't know how to, I'll just look up a,
I'll just see if I can record a video for you, like a Loom video on how to actually do this.
But yeah, this is like the last method that I would do. So I would just, the first thing I would do
is I would just scrape Facebook pages of the people that are already in there. Second thing,
I would use something like a home advisor data scraper. And then the last thing, right? The last
thing I would use is basically like a software, like an outsource software that scrapes those
leads, just because, you know, it's pretty hard to find leads as BTC, just because it's like people
that are like already live in, that it's very hard to find like even their email, their exact
email addresses, not like they have a company name that you can just look up to, right? So yeah,
so this is what I would use, man. So I hope you found some value in this answer.
So yeah, this question is going to be from Vishal. So the follow-up, so why do you implement
variants on follow-ups? So yeah, so I don't implement so many variants in follow-ups. I'll
just put like two, because I don't think you need that many follow-ups, honestly. I would just put
like two just for deliverability reasons, right? So my follow-ups would be always like adding a
little bit of value, something like, hey, a bunch of companies like your company
saw an increase in, let's say, in book meetings or in revenue in X after using our automated system.
And then I would just enter, I would just include like a case study. I would say, hey, example,
here's a case study of a company name that I've worked with that have seen like a significant
amount of results. And something like, hey, what's stopping you from hopping on a 10-minute call?
Right? So usually my follow-ups are always pushing for a call. So if you guys realize
and you guys noticed that the first email is always going to be some sort of just
tests in the waters, asking them if they want more information or like more information,
basically on what I do and what our company does. And the second email is basically going to be
like asking them for a call, right? Because once they read it, let's say they didn't see my first
email and they read the second one, they will go back to my first email and they will see that I
was asking them a question, right? It works like a psychological trick on each lead that I found.
So yeah, like I would just put two variants in the follow-ups. I wouldn't worry too much about it.
Sometimes I would just include one and I would just basically change the variants such as from
hey to hi or thanks for the time, thank you. That's pretty much it. Do we have a second
personalization for follow-ups? Example, refer to LinkedIn first. Personalization refers to company
blog and second. I don't think you need this. I think it's a little bit overkill.
Like I've implemented a little bit of that in the past. I haven't found any like value.
I haven't found like any substantial results from that. The most important thing is having
personalization in the first email. Yeah, that's all that you need. You don't need to personalize
the second email just because it's going to like, I don't think it's worth it. It's one of those
things that you're going to see like a marginal success with it. But in the grand scheme of things,
it's not going to help you close more deals or like get more replies. So personalization,
I have heard so far these input parameters for crafting AI personalization such as LinkedIn
about fiction, company domain content. LinkedIn activity posts have yet to work through all of
your modules, but which one of your experience works best? Do you have prompts for personalizing
and leveraging each? Which personalization would you recommend at what step? So that's a great
question, man. The best one, the best personalization that I found the most success with is scraping the
company's website page hands down 100%. Just because if you think about it, most people,
if you go on YouTube now or like you just go on Twitter or like anyone who's leveraging AI
and personalization, everyone is taking data from the summary, basically the job title, everything
that's under LinkedIn summary, like the person's LinkedIn. Well, in reality, when you come in
from another angle, you come in as you saw something in their website. It's a little bit
more objective to the recipients and they kind of like, it puts you in another frame. So you're not
just there to ask or like give them like to add some personalization about their favorite dog breed
or like whatever they had in their last post. So you're coming in as objectively as possible,
saying, Hey, I noticed this about your company and you're kind of like, you know, just coming in
and as adding value, you're not just adding like just surface level personalization that I hate.
And I don't think I don't recommend you guys do that. And if you notice also in my flows,
I don't just add the summary, the job title, like I include everything like the summary,
the job title, and also the text of the script webpage. Right. So that's pretty much it. Now,
let me show you guys. So if I go to my make.com, let me walk you guys through what like what the
personalization I used basically to scale. And if you guys noticed, like I updated a few things in
the blueprint library. So I've included the prompts that I recommend you guys, like everyone
use it. And it's one of the best prompts you could use just because.
Like I've put so much effort in that one back then, like, like six months ago, and it really
helps. It's specifically tailored for our business model. Okay. So let me look for it. It's called
validate email. So this is the, I think this is the one. Yeah. So this is the system that is,
I think it's this one. Let me go back.
Validate email. I think it's pretty much this one. Yeah.
Let me see. I believe this is the one. Yeah. So this is the one, guys. So this is the
personalization that I would recommend everyone use just because it's pretty, pretty general.
And it works with any industry you're going to pick. So it's a, it's like, it's extremely,
like I said, extremely like tailored for our business model. So the, the icebreaker that
we're going to be using is basically, like I said, like tailored for our business model.
And if you guys read this, it's, yeah. So perhaps personalized outreach,
shoeline icebreaker based on pathway inferences. The message should be directed to a potential
clients using publicly available information to connect their dots and demonstrate an understanding
of their unique situation. So this is everything. So if you guys read this,
relate these insights to the service being offered, which is building sales
systems specifically designed to connect them with their ideal clients. Now let me try to
actually test this like in front of you guys. Right. So let's say, for example, I'm going to
unlink this or let's see leads draft. Let's see if I can have like a retrieve like one limits from
here. Let's run this module only. All right. So we have this. So we have the personalization here.
Let me unlink this and unlink this too. And then there's just run this. I think I'm going to have
an error just because I don't have the API key right here. I've changed it. So let me
check this, take this off. And then I'm just going to connect this right here and then let's go back.
Okay. So that website didn't work. So let's just try another one to five
run once. And then let's see. So the first one, second one. All right. So let's see.
So if you read through this, hi, Bruce, I've been impressed by pro novus is using analytics to
transform construction efficiency. Are you exploring exploring innovative ways to leverage
that data-driven insights to connect with construction firms that are eager to enhance
their project outcomes? So this is what I mean by that great personalization. And if you guys see
this, if I go back to personalization, so this is what we're feeding in. We're feeding in every
single thing, right? Feeding in the website, scrape data, like we were like feeding in the summary.
So it's not just one summary or like job title. We feed in everything. So look at this.
Yes. Yeah. So we're feeding everything right here. As you guys can see, we have in full name,
lead website, data, the organization named headline title. So this is essentially what you
have to do to have the best personalization possible. So yeah, this is this system right
here is updated in the blueprint library, also along with the prompts. So just go ahead and use
that. Awesome. So I think this is the question that we have here. Which personalization would
you recommend at what step? So I would just use this honestly, I would just use this flow right
here. And it's going to get you like pretty much 80% of the results, right? Because you're getting
through the noise. Trust me, like the average email that most companies get is you'd be surprised,
man. You'd be surprised that the composition is pretty low. Awesome. So let's read through
the questions. If you guys have any questions here, just put them in a chat. For follow up emails,
I lack case studies for business model. Any other ideas on how to provide value
and get the customer reply? So essentially what I would do, so there are multiple ways,
but the best way that I found would be basically taking what you said in the first email. So let's
say you don't have any case studies, right? You say something like I built a system that generates
X amount of pre-qualified meetings with ideal clients and X amount of time, right?
And then what you would do is you would say something like hey,
TLDR, and you would just include the value, right? The only thing that you would include
in the second email would be what you did, right? So in the first email you would have
the personalization basically and what you do, your clear value proposition and the soft CTA.
Now what we do, we just re-say what you said about building a sales system that generates
X amount of leads, right? The only thing that you would say is that, right? Instead of just mentioning
a case study, right? So kind of like basically using the same formula. Instead of just using
a case study, you're using what the system does and what the client is going to get, right?
Because this is what they care about. They only care about the results they're going to get.
So yeah, this is what I would do.
Hey, Saad, when you scrape websites, do you only scrape the home page? Do you even scrape
the about by? No, I don't, actually. I just scrape the home page and I have never found
any issues with that. I don't scrape any blogs or any other pages. Typically, these B2B companies
that we scrape their websites, they know that their clients or their ideal clients, once they
enter their home page, they must include everything there, right? Just think about it. For example,
on my website, I'm putting a giant list of my case studies right here. I'm putting my entire
value proposition right here. Even though I have other pages, the only thing I would put here is
the case studies and basically the entire value proposition, right? Most of these companies,
they're not stupid. They know what they're doing. They know their landing page is basically their
face card. So I wouldn't worry too much about it. I would just scrape the home page and typically
you'll have more data than you can ever manipulate. What could I use in my free campaign not having
social proof to show for my clients and sales systems at least? Like I said, man, I would just
use the first thing that I've done. So I'll tell you exactly what I've done. So what I've done is
I've included any password experiences and I linked them to my sales agency. I found the best
way to do it. So you'd be surprised. A lot of work you've done, even if it's not related to
automation, you can just go ahead and link it to it, right? So let's say you've worked in some
other company. Just think about it, right? If you've built in that system, they could get
shit tunnels. So you should be confident about your system. You should really be confident about
it. This is a limited belief that I see on a lot of people in the community. You should have the
confidence to basically believe that your system is going to get them results because it will get
them results. Like there's really no other aspects of it, right? So the first thing I would do is I
would leverage any work experience that I had and I would link it to sales systems. I know it will,
just don't have figures to show from the past. Then the first thing that I would do is just
then I would just front load as much value as possible. I would say something like,
I built a system that generates X amount of pre-qualified meetings with decision-makers.
The only thing that should change is your language. So instead of just framing it as
leads or booked appointments, just like everyone says, you would frame it as, hey, I will
connect you with pre-qualified decision-makers. The words are everything you need. The best
marketers aren't the best marketers. They're the best scientists and the best people that can
communicate. You don't need to put a dollar sign in the copy. I've had so many campaigns that I
did not leverage any dollar sign, like how many revenue I've generated, and they did pretty well.
So I would say I built a system that generates, let's say,
10 booked meetings with pre-qualified decision-makers. The personalization is
going to do most of the job, trust me. When you have a personalization that speaks exactly
their industry tone, and basically if they had any case study or GPT is going to basically
retrieve their ideal customer, which is 90% is the real ideal customer, you're going to get a
response 100%, trust me. You're going to get 100%. And then your job is just nurture them to a call.
Once they say, hey, I would like to know more information, then this is the highest leverage
that you have. So they're going to be like, hey, Roger, thanks for reaching out.
Would love to know more. This is where you basically pitch your service. If you go to
the classroom, I've added multiple replies, basically every possible reply that every lead
can give you in your instantly campaign can ask you. Sometimes they would say, could you tell me
more? Then now you would basically say the following way, basically. Now that I know it
on top of my head, you say something like, a pre-qualified decision-maker just means someone
who is interested in your service that is willing to hop on a call. This is the most straightforward
and clarified way to say it. Company founders, they love when you speak to them this way.
Just because most legion agencies, they try to hide behind buzzwords. When in reality,
this is what you're giving them. They say booked meetings. Well, booked meetings of what?
What is a booked meeting? Meeting with who? You need to clarify your value prop in a way
that makes it so hard for them to say no. Now, if you tell them, hey, I'm going to connect you with
long-term partners that are interested in your services, there is no person on earth
that would say no for a 10-minute call. The only way to frame it is to help them grow.
If you guys notice that in my messaging and what I recommend you guys use is basically
always pitching your service as helping their company grow.
I want to help you grow. So this is why our business model is super strong.
It's because we are framing ourselves as growth partners. We're not just a random service provider.
Let's see if there's any other questions. Okay, so Sacré-Dialogues said,
I hope I said your name correct. What are the main reasons why Elite is not closed on a call?
Well, it really depends, man. It depends on your ability to close the deal, a bunch of aspects.
Maybe there's no pain. Maybe you didn't pitch that right. But I think it's just a numbers game
at the end of the day, so you don't sweat it. The most important thing is you have to get your
apps in and then you're going to get used to pitching your service. A more actionable
thing that I would recommend you do is coming into the sales call with the mindset that you
already closed it. So you're going to be more positive about it. You're not going to beat
yourself to that if you don't close it. So at the end of the day, it's just a numbers game.
So this is why I would recommend hopping on as many sales calls as possible.
Just going to get your reps in first. And then each sales call is going to get easier and easier
and easier. The main thing is your tone, how you pitch your service, and the step-by-step
sales call roadmap that I showed you guys in the last video, where the best way to close a sales
call is to come in, ask a bunch of questions at first, be enthusiastic at first, show gratitude,
and then ask them a bunch of questions, give a small tiny presentation of what you do,
and then try to close them right away. Just right away, try to close them.
And then it's going to act like a weird, different thing for each prospect. Because
each prospect that comes into the sales call, they essentially expect some sort of pitch in
like five minutes before the call ends. Now, if you change that and you pitch them right away,
now we have 40 minutes of actually handling the objection. You didn't put your entire load
for the entire conversation. You just give them a little bit of hints of what you do,
and then you pitch them the service. You pitch them, hey, this is how much it's going to cost.
And then now you've completely took that out of the way, and now we have 40 minutes to discuss it
and basically tell them exactly how you're going to help them.
Just because if you keep talking about how you're going to help them during the sales call without
actually trying to close them and trying to close them at first, they know that's five minutes
before the call is going to end. They're going to be like, hey, yeah, just send me a proposal.
The ideal recommendation would be trying to close them on the call.
This is like the best way I found to close. Remember, guys, it starts with no.
Awesome.
So if you guys have any questions, just put them in the chat right now.
I believe these are the questions that we have here.
So it looks like we don't have that many questions today. What are some questions,
objections from leads that you've struggled with the most in the beginning of your journey?
Maybe something that surprised you when you heard it.
Yeah, so the biggest...
There's a loud car outside. Sorry about that, guys. So basically, the most... I think once,
one time, I was very confident coming into sales call and then there was this founder.
I believe that it was like an enterprise level company that I was pitched in.
And then I was going through the entire presentation,
what I do, being enthusiastic, and what they said. Basically, they said,
well, this is great, Saad. This is amazing. But what makes you not be a better fit?
So that completely... Actually, I was not expecting that.
So, yeah. I think that was one of the hardest questions that I've encountered.
But yeah, the first thing, I wouldn't worry too much about it, guys.
Like... Let me fix my camera. So I wouldn't worry too much about it.
I wouldn't worry too much about it. Like I said, you're just gonna get your reps in at first.
And then you'll be all right. Hey, Saad, when you're...
Can you hear me out? So it looks like you couldn't hear my answer.
Hey, Saad, when you're scraping leads, how do you clean the list so that it stays within your niche?
For example, if I choose dentists, I see other niches, spas, etc. What should I do in this case,
especially if we're seeing a list of three plus K? Yeah, man, with scraping...
Obviously, it's just a part of the game. You're gonna have some companies that are outside of
your niche. It's just how it is. You don't need to worry too much about it.
Even if you filter out some keywords, even if you exclude some keywords,
let's say you're scraping Apollo, LinkedIn, and you exclude some keywords,
you're gonna have some companies that are within other niches. It's just normal. When you scrape,
the accuracy of data is not gonna be 100%. It's just how it is. You don't have to sweat about it.
I would just exclude the keywords, as much keywords as possible.
If you really want to exclude as many keywords, I would do something like take a company's
webpage that you do not want to work with and just put in a make.com flow that extracts all
the keywords that you don't want to deal with. And it's gonna be, let's say, 50 or 100 keywords.
And I would just add them to Apollo to exclude all of these. But in reality, you're still gonna
have some companies that are outside of niches in the list. It's just how it is.
As a thought experiment, I have a video production company. If I wanted to find for myself clients
as a test, which I can then learn to do so for others. How would I go about finding leads that
want video production services, wanting a video promo commercial explainer? Eventually, I can offer
for this to your services, but I can test this out on myself for now. Yeah, it's all about the
offer. That's a great idea. But the only thing that would make you stand out is the offer.
Just because you need to provide a little bit of value upfront for every lead. So you need to
find an offer that would resonate with them. And the same formula is gonna work. You would find
what would be your ideal customer, the one that you want to work with, and then just prepare a list
and then have an offer, basically.
Yeah, the most important thing in this campaign would be the offer.
And then it really depends at that point. It really depends. When you know your ideal customer,
then I would worry about where I'm gonna get leads. But first of all, you need to know
what would be your ideal customer as a video production company. What are the people that
you wanna reach out to? Is it going to be B2B? Is it gonna be B2C? You need to define what you
want from the campaign instead of just thinking vaguely. So on a call, who talks the most,
you or the lead? What is the ratio? Yeah, that's a great question, man. The person that needs to
talk most is the lead, 100%. Because humans, when they feel like they've talked a lot,
they kind of have this psychological aspect, they trust you more. So I would just keep asking
them so many questions and I'll tell them to give me so many information that I could leverage and
pitch to my service. So maybe ask them as many questions as possible. And like I said,
give a little presentation about yourself after you're asking the questions,
and then pitch to the service right away. Like pitch the pricing. Just you wanna get that out
of the way first, and then you can handle the objections for about 40 minutes. So let's say
you have an hour on the sales call. So in minute 15 or 18, you would be pitching the pricing.
And then you have like 40 minutes of actually really getting down to it.
I had issues getting normalized company names, like an own employee would prefer the company
himself using a prompt. Many of the progressive recruitment talents or CLO talents stayed the
same instead of progressive or CLO. Yeah, man. Sometimes GPT is gonna mess up. I would just
use a better model. Like I would just use GPT instead of the GPT 4.0. I think you're using
GPT 4.0. GPT 4.0 can be a little bit dumb sometimes. So the only way to solve this is to change the
model, right? So net 3.5, that's weird actually. It's the smartest one. But as you guys can see,
even AI will mess up sometimes. It will mess up like 5% of the time.
But you gotta remember, when you're doing this at scale, a few leads are not gonna change anything.
Yeah. Maybe I'm giving him more examples. Yeah. Even though in the flow that I've
included, there's like 10 examples. So it really depends on each company's name.
So you don't... So this anyway...
Yeah. Keep in mind, Brian, you're not gonna be 100% specific with them. There are going to be
maybe like 15 companies that are in your list that are not gonna be your ideal customer,
just how it is. This is why we're doing it at scale and we're scraping more than 5,000 leads.
It's impossible in any scraping system to get like 100% right ideal customer every single time.
It's just impossible. So you're gonna have like a few companies that are
there. Because no person on earth actually has a campaign that is specifically tailored
for those people. You're gonna have a few companies that are outside of your industry.
And this is how you get data. Because you don't have full control over it. You're gathering data
from a platform that actually also gathers data from another source, such as, let's say,
Apollo. They get leads from LinkedIn. So it's outside of your control.
It's not something you can really use or pick. You're just taking a big, large pool.
How do I find companies that actually want a video? Any ideas, data aspects?
So let's say after health services, how do I find the ones that want? Well, you can't really know
unless you try. I wish I could give you an answer right away. What you can do is just go ahead and
reach out to these people and ask them if they want a video production. You have to interfere with the
market. Or you could look for companies hiring. So there's another way. You could look for companies
hiring for basically a video company. You would look for keywords in their job postings. So you'd
look for companies that are hiring for a video production company, maybe like a video editor
or something. What I would do is I would ask Chad GPT for all the jobs about video production
that a company would be looking for. And I would just look for these titles in,
like, let's say LinkedIn job scraper or indie job scraper. And kind of like the same way that
you guys see me scrape companies hiring for an SDR. This is like the beauty of that system
is whatever service you're providing, you can try to find companies hiring for it. And you come in
and pitch your service as someone that will give them, like, as an outsourced provider instead of
them hiring someone in-house. So that could work. So what I would do is I would have to
I would have a campaign that's a little bit, you know, higher in volume. I would scrape leads
from Apollo or LinkedIn. And I would look for health services. And then what I would do is I
would just basically, I would have it like as a test campaign. Like I would just ask them, hey,
would you guys be up for a video? Or like, do you guys want a video? I operate as a video production
company. And then what I would do is basically have another campaign, yeah, another campaign
that, you know, scrapes those job postings. Okay, so what types of lead magnet would you recommend
proposing to recruitment agencies? And what lead magnets would you recommend offering on behalf of
recruitment agency clients? A lead magnet. So I've never sent lead magnets to, like, prospects. The
only thing I would do is the flow that I showed you guys where you offer them, like, a free batch
of leads. Like, I have the best success with it. Yeah, so, like, I would just send them, like,
a form. They would fill out the candidates that they want. And I would just find them, like,
lists of 200 or 250 companies hiring for these roles. And I would say, hey, like, would you mind
hopping on a call? We can discuss this. And I can basically build you a system that finds these
companies and reaches out to them automatically, because they already hire and for the candidates
that already have in your company. And this is how it gets sold immediately. But in terms of lead
magnets, the only thing you would do is you would have a lead magnet nurturing someone into a call.
I've implemented this in the past. So let's say a lead books a call. Let's say they book a call
in, like, three days. So you nurture them lead magnets about what you do, like, in your emails,
right? But, you know, keep in mind, you're not going to see some crazy results with it.
Brom having issues with PayPal and Stripe is not available in my country. Is there any way I can
make any payment process smoothly without integrating it to PandaDoc? So if you're not
going to be using PandaDoc, man, just use something like DocuSign, right? I would just use DocuSign,
and I would just invoice them using PayPal, right? Most people in the U.S. are using PayPal,
so I wouldn't worry too much about it. So I would just invoice them using PayPal. And the first
thing I would do is I would send them, like, a contract, right, using DocuSign. Once they sign,
I'll just send them the invoice. You don't have to integrate with PandaDoc as you just got started,
right? I'm not sure if you talked about this, but can you explain how you price your service?
Any strategies on how you retain them month over month at a high ticket? Yes, that's a great
question, man. So the first thing I've done or the best pricing model that I found, which basically,
the one that I'm still using currently, is I pitch an upfront fee, right, which is going to be
not a crazy amount, just basically like $2,000, $3,000. And there's going to be an upfront fee,
where I basically deliver them, like, an X amount of leads, right? And at first,
it really depends, right? There's multiple pricing models, right? So you can charge,
like, an upfront fee, and then you could charge a paper lead, right? So at first,
what I would recommend, you charge them, like, an upfront fee for, like, an X amount of booked
meetings, and don't charge them for a paper lead model, right? Try to just get your foot in the door
at first, and then once you get... So the best thing that I would recommend is you crush the
campaign. The first campaign that you're going to, like, help them with, that you're going to build
them, is going to be best... This is where you actually crush the campaign, because once you
actually get them, like, a few leads, you become irreplaceable. So I would charge, like,
an upfront fee for the system, right? And then it's going to be something like five to ten
booked meetings, right? I would do that. And then I would transition right away. Once I deliver them
the five to ten booked meetings, I would sell hard, and then I would enter the Trojan retainer
that I've talked about in the Masterclass, which is going to be a paper lead model along with a
retainer, which is going to be a $2,000 to $3,000 fee, which is going to be campaign management,
basically lead sourcing, nurturing all of these leads that are going to be interfering with their
offer, offer creation, right? So my recommendation would be frontloading as much value as possible.
So if you guys go to the classroom, you're going to see the offer deliverables, and there's, like,
a list of all the deliverables that I would recommend you guys mention whenever you pitch
in your service, right? Because it's not just going to be a booked meeting. There's a lot of
things in the back end, right? But the client doesn't know this. But you need to mention it,
just so they know that you're going to put a lot of effort into this. And then what I would do
is basically just now enter the retainer. But keep in mind, now you can, like, upsell hard
just because you've delivered the rules in the first campaign. How long will this business model
be relevant? According to you, as you know, AI is on the rise. Agents are taking over everything.
Man, I don't think agents are going to be all that just because agents are always going to
mess up, right? Like, I don't like these AI agents models. I think
the high level that we have is human connection when it comes to closing deals. And, like,
an AI agent is not going to be able to build out a campaign, handle the outreach, be an SDR.
It's never going to happen. Maybe in 100 years, maybe. But think about it. An AI agent is not
going to look in the market, try to find a customer, find that customer, talk to them,
pitch their service, and get a client for another company. An agent is not going to do that, right?
So our business model is going to be viable forever, basically. Because let me explain to
you why. A company's entire life, the entire freaking thing that a company makes, that it
makes a company stay alive is customers. If you don't have any customer, you don't have any
business. Just keep it 100, okay? If a company doesn't have a customer, it's a side hustle, right?
So as long as you keep getting them customers and getting them pre-qualified decision makers,
like meetings with those ideal clients,
like any business on planet, whether it's a B2B company or a restaurant, they need customers,
right? And this is how, when you basically go back to the basics and really dial it down,
you know that the main thing is customers. So I wouldn't worry too much about voice agents
and all of that. I think most companies that worry about the chat bots or the voice agents
are never going to scale, just because the main thing that actually drives revenue is
getting them more clients. It's just that simple. What was the most, and I wouldn't worry too much
about voice agents. I would just stop looking at this because it's going to really like,
it's another shiny idea. It's another shiny idea syndrome that most people look into.
And if you stick to one thing, you're going to crush it. If you keep looking at other,
you know, other things such as, because the grass is never going to be cleaner.
If you go to another grass, you're going to see another grass and you're going to be like,
that might be the one. And you're going to just keep hopping and hopping. It's called
niche hopping. And this is how you never become successful. So what was the most you ever charged
for a single system? What was unique about that kind of situation? Yeah. The most amount of charge
was a red light therapy system. So it was very hard to get them leads just because they were
charging, like the average deal size for them was like $15,000.
Like some niches that you wouldn't know, like you wouldn't actually know that they make so much
money, right? Just like red light therapy. Like there's a lot of millionaires that invest in those
devices, right? And then you would know like that a company would make like a shitload of money just
by selling one of these red light therapy beds. So what I've done is I was basically,
it was a pretty hard campaign. Like I couldn't continue on it just because a lot of people find
those leads and I would just get like 300 leads, but they've managed to like close like three or
four, which was awesome. So I was basically scraping companies and a healthcare organization
that they were basically, they had like a red light therapy and only the bed,
right? It's a specific model that they were selling. So it's very hard to find those companies
that they had that. And I believe this is the company is called Red Light Systems.
Yeah, this is the one.
Okay. They had a great offer. You know, they add like a red light therapy business with
high ticket ready paid patients. Oh yeah. Like they had like a great offer.
Okay. Let's see. Another question that we have. So we only have eight minutes left. So guys,
if you have any questions, I'm just going to put a thread and make sure you just copy,
just copy and paste your question in the chat and just add it in the thread. I'm just going
to reply to it and answer it next week. So do I have, I know before that we have,
when a client pushes back on why they are paying for leads that don't show on the call,
what is the best way to explain how to cause is already factor. Could you go back over this again?
Yeah, man. So you have to explain to them that every like the pricing model that you are
implementing is already factoring like the costs per lead that doesn't show up. Right. So what you
do is you, you can like, you can say the following, you can say something like, Hey, usually we would
charge like, let's say $250 per per per per lead. Right. And then you could frame it in a way where
you can say, Hey, instead of that slash in like, let's say 20% of that, just because we understand
that some leads are not going to show up. Right. So it's all about how you say it and the sales call.
Right. Because we know that some leads are not going to show up. It's just how it is. But yeah,
it's all about how you frame it. Like this, you would explain to the client on the sales call,
Hey, we understand that some leads are not going to show up. So instead of this, we're going to
like, uh, slash a percentage of the paper lead. Right. Which is, which is called anchoring your
price. It's a method, but the, uh, the main thing that I would recommend is you never wait for the
client to say this. Always make sure you front load it. You like, you should be the one to say
it, that says this. So when you, let's say you're in the sales call, you're asking the questions,
you're given a little bit of presentation about what you do. And now it's time to pitch your
service. Once you pitch the pricing, you completely front load the, uh, basically explaining how a
paper lead model and how your, your slash is not show up, uh, showing up leads. This completely
takes the entire conversation in another way. And the clients understand that you're professional
about what you do instead of them asking you first. Right. So you're kind of like defining
your availability in a way instead of a client assuming you're going to be available
all the time in a way. Does that make sense?
Do you send the lead magnets upfront to your recruitment companies? Do you offer this customer
further animating? No, I actually send them the lead magnets. Um, after I basically, uh, ask them,
like the first email will be like, Hey, would you be interested in a, like a list of companies
hiring for the candidates that you guys have and just provide me with a list of candidates?
And then I'll just go ahead and find those companies. Like, this is the first, the initial
offer. This is the entire offer that you guys would run in the recruitment, uh, industry.
So the last question that we have here is how did you come across that red light therapy client?
Do you, do you remember what I campaigned or responded to? So actually it was a referral net.
Like it was a referral just because I don't recommend you guys target these companies.
And I, I was not actively targeting companies that are red light therapy companies. It was just a
referral for my clients. And they basically just, uh, referred me to them and they're like,
Hey, I can actually get results. And I was like, sure. Like what is the average deal side? They're
like, Hey, $15,000. I was like, okay. I mean, even though I know I'm not gonna, I'm not gonna
get you so many clients just because there aren't many leads. If you, if I can just deliver like
one or two, maybe like three maximum or really work on it, I'm not gonna, I'm not gonna get you
one or two, maybe like three maximum or really work hard for it. I can essentially just make,
let's say like the average deal size would be $15,000. I can make like a, I don't know, like
for three deals, I can make like 7,000. It's pretty good for me. And I think it's pretty much
pretty good for everyone. Yeah. So, um, okay. We have another questions. Why a hundred percent
leads wouldn't buy the service if we offer full refund guarantee? Why a hundred percent leads
wouldn't buy, leads wouldn't buy the service if we offer full refund guarantee.
So I'm, I'm, I'm assuming you, you, you, you meant that why would a lead not buy the service
even if we offer a full refund guarantee? Some clients aren't, some leads, I can't give you like
an answer that I wish I can, but it depends on each lead, right? Do I have to register my company or
can I just start working without registering or what if I can, yes, you don't have to register
a company. Given my, you can always do business without actually registering a company. Um, I'm
just, you know, giving you my best recommendation, right? So you can always register a company later
after a few sales. It's totally cool. Awesome guys. So yeah, I think that's all of our questions for
today. If you guys have any additional questions, I'm going to make a thread just after this call.
And then once I do that, just put your questions there and I'm going to make sure I go over them
and then I will respond and answer these questions next week. So thank you lovely people
for coming in today's call and happy new year. I hope you guys, actually I know you guys will crush
it coming in 2025 and happy new year guys. And I'll see you guys in the next call. Peace.
