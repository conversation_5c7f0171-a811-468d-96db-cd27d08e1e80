Okay, so let's go over the first question that we have.
First we have <PERSON>, looks like the type, thanks man.
Okay, so the first question is from our friend <PERSON><PERSON><PERSON> and he's asking, how would you tackle
cold email outreach in a small market like France, where some niches might only have
500 potential leads.
Not everyone speaks fully French, English, I've been using AI tools to help with translation
and message postings.
All the courses I found on YouTube seems tailored to address a big market, the US and the USA
and UK.
Well, I'm going to answer your question my friend, right now.
So I was taking some notes and then I'm just going to explain to you why you should be
going for the US market.
Well, first of all, I want to explain to you by saying that never go for the crumbs, like
always go for the big buyer.
Typically the US and the UK have large customer base and money, like always the US is the
only target you should be going for is going to be the US.
Sometimes Australia and UK, but I would just recommend you just target the US.
Because think about it, 500 leads, how many companies can you actually scrape, how many
leads can you actually scrape for those clients?
If you think about it, how many?
And how many of them are actually going to convert, right?
So let's be real, even if it's like 10% conversion rate, even though it's not even scalable,
so you would eventually just run out of leads and pretty much you cannot have a healthy
LTV, which is basically the amount of time and months that a client would pay you to
continue you delivering them the book meetings or whatever server you're providing.
But I'm also going to give you another solution, so if you really want to continue targeting
small markets like France, I would basically just knock it out of park for a few clients
there and then I would leverage that to expand into similar markets in Europe.
Even though in Europe, most companies that I've worked with in Europe is very few, like
one or two, the cold email is not as known as in the US, basically.
They typically rely on other lead sources and mostly referrals and pay-per-clip ads,
but I would always go for the US mainly.
I'm here to explain to you what LTV means, basically just the total amount of money a
customer will pay you over the time they do business with you.
If you sell a service for $400 a month and the customer stays with you for 10 months,
that means their LTV is $5,000.
Like I said, you can work if you find a few clients in France, you can work with them
and then I'll just use those case studies and then I'll just leverage that to expand
in the entire Europe and eventually just go and basically expand to other markets.
There are multiple people coming in, so let's admit all of you guys.
Welcome.
Okay, so the next question is going to be from my friend, Aaron, sorry if I butchered
your name.
So once a client responds to an email after a cold outreach, how do you handle back and
forth communication with them, especially if they have several questions?
Can AI be leveraged for this or does it need to be done manually?
Now at first, you don't want to use AI just because you don't have that many clients
to actually work with.
So I would encourage you just respond to them manually and you could do something that I
used to do is I used to have pre-drafted messages that I put in the community.
So these are all pre-drafted replies that you could have basically in your notes app
or your Google Drive app on your phone.
So even if you're on a go and a lead replies to you, you just go ahead and basically just
tweak the reply a little bit and then you just reply to them.
So another thing is, especially if they have several questions, like a bunch of examples
of those questions could be like, you know, give me more information about your business
or something like what is the price or do you have any case studies?
So AI can definitely be leveraged at this.
I definitely did that scaling up to like $40,000 a month, but I only leveraged AI when it was
like the time to use AI.
But if you're just handling a couple of replies, basically just a couple of replies, like let's
say like three to four replies a day just because you just got started with your cold
email campaign, you're not like sending basically like thousands of emails per day.
So I wouldn't go that far and use AI at this point, just because you don't have that much
scale to work with.
Okay so the next question is from Roger.
So my client runs a print shop in New York.
They do bags, warm-ups, jerseys, t-shirts, pens, mugs, average order size 6K, okay, huge.
Right now their clients are all over the place through primary schools and yet their mail
is only for referrals.
Is this a viable candidate to run a cold email campaign for?
How would you get started?
Okay so I'm going to answer your question my friend.
So let's go back to our main one.
So with an average order size of 6K, like even with the successful conversions, this
is going to be very lucrative just because you can leverage a lot of things.
You can definitely build a cold email campaign for them and it would definitely do well just
because they have like, I say, you said something like, if I read this, if I go back, they're
mainly from referrals and they have like an order size of 6K, so that means they already
have clients, they already have case studies.
So what you can do is you basically just leverage those referrals and word of mouth, it just
shows that their existing customers are satisfied, so they have PMF, basically PMF is just product
market fit which means you can leverage that as hard as possible and basically front load
that in your email or in your email campaigns because it's going to help you with conversions.
Social proof is extremely important and when you work with those companies that have pretty
big case studies and they have like extensive case studies that they've worked with multiple
companies, it's so easy for them to get clients and basically just scale their lead acquisition.
I put up a target for you here, so I would target schools and educational institutions.
I'd also consider summer camps, sports teams, community centers and non-profit organizations
that might need custom print items.
You could easily scrape these off Apollo and you can also like scrape them off LinkedIn
and Sales Navigator, I believe.
Just look for decision makers of schools and educational institutions and then you just
go there.
Like you just go from there and I would leverage social proof hard because like an average
order size like 6K, you don't even need to like get them a lot of clients.
Like you will like basically, you will easily just, the cost proposition, let's say they're
spending like $10,000 to like acquire clients, right?
So if you just put like an initial investment of like $400 to build a campaign for them,
like you can easily just fulfill a project, right?
And then like they have like an average deal size of like 6K, so they already like paid
the initial like investment, the initial setup that they paid you.
So it's so easy.
Like you just get them three clients or like three book meetings and then that's it.
You justified your entire service with them.
So hope this makes sense for you.
So the next question, to run the campaign for our clients, we have to buy domains similar
to theirs or we can use any particular domain which has no resemblance with client's brand
name.
Does that work?
Well, so I already replied to you here but I'm going to give you like my full extensive
like basically response.
So at first, I would recommend you buy them the five domains with three email inboxes
and replicate the same.
So once you have like I said, like once you have a couple clients under your belt, just
go ahead and start like buying pre-warmed emails because you'll have the most leverage.
This is essentially what I've done.
So at first, I was delivering projects as in I was buying them the domains.
I was doing the initial setup.
I would also like buy them the instantly domains, the instantly basically subscription.
And then I would do everything on their own plat, like on their own, you know, using their
own emails and do everything and then I would just hand it off to them and they can just
go in and like log in.
I don't think it's the best business idea.
I don't think so because it really, the main value that you have there, it's not the initial
setup that you're doing, it's the actual deliverables which is actually delivering them clients
and book meetings that, you know, there are ideal clients of their whatever customer,
whatever service or product they're selling.
So once you have a couple clients under your belt, I would 100% just invest into the instantly
yearly plan and it's extremely, extremely rewarding just because you can literally lower
the cost exponentially.
So say you have like a couple clients, right, and then you buy the instantly subscription.
It's basically like $900 a year and you get to send like 150,000 emails per month.
Like how many emails are you going to be sending for each client?
Like it depends obviously, but you can scale up to like 2,000 emails per day and you still
have like the necessary, you know, and your plan will still allow basically all of the
emails to be sent on all of the emails to be linked.
So what I used to do and what I've done like previously is once I started scaling, I then
purchased instantly like yearly plan and then what I would do is just give them a dashboard.
So instantly just allows you to share like a dashboard login with them and then you can
just assign the campaign that they have there and then what you can do is basically just
once you share that with them, they can just go ahead and see the analytics.
So when you are like constantly like delivering the results, they will see like the analytics,
basically how many emails are sent in and then that will be it basically.
So you're collecting all the upfront fee, so you're collecting your, let's say you're
charging them like $3,500, like $3,500.
So you already have the instantly, so you don't need to like purchase additional bullshit
and you know, the real sauce in here is you can basically instead of, you know, buying
the domains, you can just go ahead and buy pre-warmed up domains, which is essentially
how I've managed to scale.
So if you go to my website, I have this thing right here.
So if I go to FAQs, you will see that I have something like how quickly do we see the results.
So we don't waste 14 days on warm up nonsense or infrastructure fluff like other lead gen
agencies while we're busy warming up domains or busy like delivering new results.
So what I mean by that is I will just buy them like pre-warmed up domains and then I'll
just take a few days like perfecting their offer, scraping the lease and I'll just get
up and going because I want the market to tell me like, give me like, I want to interfere
as like as quickly as possible with their market so I can just get the results and just
delivering them the book meeting like as early as possible so I don't worry about it later
on.
So it really helps you sleep at night and it really decreases the refund rate like because
if you get refunded a lot Stripe will destroy you.
So just keep that in mind.
Okay, looks like there was a lot of people coming in and that's everyone.
What's up everyone?
All right.
So let's go for the second question.
So what changes if any are you implementing during this month since this holiday season?
That's a good question.
What patterns are you seeing in your own agency outbound and your clients outbound i.e. paused
campaigns reduced the number of emails etc.
Is there a slowdown?
When do you project things will pick up?
So basically typically like when you are building at a campaign right and there's a holiday
I would always always pause the campaign.
One I'm going to give you like a few reasons why it really helps with deliverability because
you're pausing the campaigns therefore you're leaving the emails to be warmed up just a
little bit more.
So you're going to have like an extra layer of deliverability like later on for the clients
and also saves you a bunch of time when delivering those book meetings with a client.
So this is like my note of a device.
So whenever there's a holiday it's actually perfect for you as a sales team because you
can refine your approach.
So you would pause the campaign right and I would always say something like hey I would
just text the clients or like it would be added in my Slack channel and I would say
something like hey in terms of the holidays we're going to pause the campaign just because
we want to ensure our emails are being sent to the recipients and the recipients are actually
opening the emails not like you know playing with their kids at a park or like going out
with their partner.
So I would always pause the campaigns and then I would just once everything goes back
to normal I just go back to my high intensity sending which is basically sending 200 emails
at first.
So I would just send 200 emails at first and then I continue the basically I just ramp
up as I go and then continue from there.
Awesome so the next question is from Jason so I'm looking forward to this call I would
like for you to do an example in depth niche finding literally from finding up an idea
chicken validity and insertion leads.
So I actually did this like 15 minutes ago before I got started with the call.
So I drafted for you guys this presentation that I'm going to go over.
So we're going to basically I'm going to explain to you what systems thinking and how you would
approach basically systems thinking and how a business would operate and implement an
cold email.
So let's just go over.
So what is an actual business?
Business is basically just solving the problem and delivering value to the customer.
So delivering the value to the customer is the main thing like the more value you provide
then the more successful your business will be.
Now how do you provide value?
The market only rewards people who expand the market like always keep that in mind.
So how do you do that is by identifying an actual real life problem that exists among
participants within a given market which is the niche and then you understand it and then
you seek to solve it with a solution which is your service which is our lead gen services
our automated lead gen services.
So the bigger the problem the bigger the business the bigger the problem you solve the more
money you make.
Now how do you pick actually like how do you actually pick a niche?
So like in business when you want to pick a niche it's something you're interested in
and you look for what's related to that niche.
So let's say you're interested in golf for example even if you're not if you're interested
in like golf it doesn't need to be going and playing golf.
It could be like helping golf players acquire more clients or like helping coaches like
golf coaches like acquire more clients.
Now the way you do that is you go into that niche and you you ask yourself like what problems
are people facing in this niche right?
And then how do you find a problem is you talk to the people in the niche so you don't
actually go and sit down and shower and like try to think what is the problem if you're
not them.
So what is the problem that they have?
You have to go and ask a few people and talk to them like what is your actual problem?
And then what you do is you come up with a solution.
So now in our case is what like I am person like I am company I don't have clients.
What is the solution?
I am a client and I have clients right?
So I am a company I don't have clients solution I am company and I have clients now.
So how do you find a solution?
So is there anybody who is in X niche who has that solution?
The answer is probably yes.
So what you do is you identify the rare few individuals who have solved that problem for
themselves and then you grab that solution and you make it widely adopted.
So I like this quote it's called the future is already here it's just not evenly distributed
yet.
So you study them and analyze them and see what they're doing and identify the core differences
between people with the solution and the problem.
So how do we apply this in cold email?
So this is what system thinking is is you apply like a big concept and you apply it
into the exact service that you're selling.
So what does perfection looks like?
So the goal is to find niches okay I want you guys to like really really understand
this because you're really going to help you with finding a niche because niche selection
like is extremely important.
So what does he perfect like what this perfection actually looks like so the goal is to find
niches where there's a natural and ongoing demand for services between the two types
of businesses ensuring mutual reliability and a consistent example flow.
So what I've done is I went to the module of niche selection and I just went ahead and
I give that a little bit of thought and I just came up with this niche right now which
you guys can absolutely just go ahead and destroy it.
There are multiple niches you can go for but first of all let me admit our friend right
here awesome.
So welcome my friend.
So now let me walk you guys through how would actually live go about like the niche selection.
So now that we know so I just picked the cybersecurity services you know helping financial
institutions that is a huge niche because financial institutions you know they need
they need top notch cybersecurity to protect client data and comply with regulations.
Cybersecurity companies need financial institutions for stable high ticket contracts right.
So the problem is that there's the need right here is the need.
Financial institutions need top cybersecurity to protect client data.
Now how do you do that is when you start a campaign you talk to the people on a niche.
Now back then you just go ahead and talk to them and you meet them in person but this
is the power of cold email.
So what you do so here's an email like pre drafted email and I'm obviously going to leave
you guys this basically this entire whimsical at the end.
So here's how I would do it.
So I would I would personalize I would have like a personalization that I always tell
you guys to do.
So I would have like a non service level observation using AI with it would have like a recent
case study script from their website funding or my song right.
And then I would say something like not sure if that's something relevant for you but I've
been trying to learn I'm just startups right here.
You can just put anything really like in like companies in their industry.
You ask them not sure if that's relevant for you but I'm trying to learn how ex companies
in their industry are using their network to grow their business faster.
I've been talking to a few founder or a few founders already and before I start building
I want to make sure the pain is real.
If you have any insights or stories about this we'd love to hear about them.
See I'm not pushing for a call at all because I don't I don't encourage you guys you know
a push for call for a call.
If you look through the outbound master class you would see that I should never push for
a call first just because people are going to get weird when you do that.
And the best results I've gotten was basically when I just ask them they want my information
and we can follow up and nurture them later on.
So what is the problem.
We have the problem which is the need right here.
Now what is the solution we're going to come up with is we're going to connect them with
their idea clients.
Now the problem is I'm a financial institution and I don't have clients.
The solution is I am a financial institution and I have clients so both connect and that
means you make money.
This is a simple equation right.
So now let's actually preach now that we pre drafted the email like I always tell you guys
how to do it.
So you'd write the perfect email for that perfect customer and then you just use AI
to essentially scale it like crazy instead of just writing an generic one.
So you'd have like a non-service level observation, recent case study, funding milestone and growth
and you'd say something like hey not sure if that's relevant for you but I'm trying
to learn how financial institutions are securing long term partners with ideal decision makers
who have the budget and pain points they can solve to grow their business faster.
So as you guys can see this pretty smart is what I'm doing here is I'm connecting both
of those folks in a way so this is how you find an actual niche and you say something
like hey I've been talking to a few cyber security firms already and before I start
building I want to make sure the pain is real.
Now you will get like over 10 percent reply rates over this copy I can assure you that
just because it's not pitchy and it's kind of like breaking the matrix in a sense because
those companies get like thousands of emails like per month like of cold outreach and everyone
is trying to sell them something.
So you just ask them like a question and then you can eventually like keep going with
this and eventually nurture them until they book a call with you.
Once you book a call with them you can just present your pitch and basically just go ahead
and you can just sell them the cold email system and the Legion system as normal as
possible.
So I want to talk to you guys so does that make sense guys like I just put something
in chat so I can just see if everyone is following up.
They never did anything else then thanks do you guys understand what I'm going where I'm
going so I just did like a niche exercise in front of you so it's kind of like a way
where you connect to industries and you go in and you're like hmm who is this industry
that needs this industry and I'm just going to talk to you guys and see if this yeah the
basic import yeah exactly yeah.
I'm going to answer the questions in the chat in the next 15 minutes so let's continue our
questions because there's a big question right here that I want to tackle I would also like
for you to touch on limiting beliefs at the moment my limiting beliefs are really holding
me back and keeping my mind closed and possibilities out work yourself out it's something I'm trying
but I'm a bit directionless in the least department so there's something called out which epiphany
guys and I've been through it so many times especially when I forgot started when I was
just making like three or four a month and before I even got started to like make it
like 10k a month so I want you guys to understand that if you have limiting beliefs that same
you know that I can't get clients through cold emails or I need a perfect copy or you
would initially just start your cold email campaign and then you would like a few days
like pass by and you go ahead and change something within KPI like these are your limiting beliefs
this is our unconscious limiting beliefs limiting you so instead you need to shift your paradigm
and actually think about cold outreach as something like this like like getting clients
is easy sales is easy I love outreach like these are all like you need to be saying this
to yourself because you need to like reprogram your your mind to actually believe that cold
emails are gonna work because if you don't you're gonna you have all these unconscious
habit like going in into your insulin account or like going on to YouTube and things and
look at some video and some guys is like saying I did this and my reply rates doubled and
you would go ahead and basically just change everything and you would know like you wouldn't
like you wouldn't let the campaign to like basically just stabilize because this is it's
called outreach lag so when you start a cold email campaign right there's a little bit
of time until like it stabilizes and you start getting the results at some points it just
gets whoosh you start getting like so many replies so we have to like give the system
a little bit of time to stabilize and this is like the outreach epiphany so this is like
a lot of this is like a big problem with late-gen agencies so they they really really like have
this problem so what I would recommend you guys do is just go through the pain so to
shift a new identity you must go through pain and you must basically repeat these affirmations
like I'm not stabbing people they're you know like I'm helping them like I'm I'm you know
I'm my outreach will work so when you do that the if you implemented everything that I've
told you basically like the only thing you should be doing is just waiting for the replies
to come in that's it don't change anything like the best marketers guys are not the best
marketers they're the best scientists so when like a scientist is basically in their uh in
their kitchen performing like uh let's say like in like a like a solution they they uh they try
that solution and they give it time like they they they don't change variables do you think
they come in online and they see another scientist they're like I implemented this and they run
their petri dish and they changes they don't you have to like let the system stabilize so uh let
me fix my camera guys for you awesome so yeah um now let's tackle liam's question and then we're
going to go over to the chat so liam has some really good questions uh thank you liam so let's
just go ahead and then answer them basically oh hey sod thanks for the no vs youtube videos my
pleasure my dude so as someone just starting out I'd love to hear a thought on the following
choosing a niche how to identify the right niche to find to focus on any tips for niches uh for
beginners to get their reps in so I would always pick like a b2b niche like I said because basically
uh b2b niches are the most scalable predictable and you can get results because the main hurdle
like I said uh for scaling is actually getting results uh because I know you guys will get
clients just see if you run the cold email system that I've that I that I outlined which is 5000
leads and you just go within kpi right so I I put like an like I've built so many campaigns that
I know like this standardized uh kpi which is basically just two percent reply rates just two
percent like you don't need to be crazy you're within kpi like if it's two percent reply rates
and you're getting like 100 flies of 5000 leads once the campaign is like ends you will eventually
have 20 positive replies all of them are going to be nurtured to a call and you even if you can
close like three to four and you pitch them like two thousand dollars like let's say you're a
beginner you started like 1500 dollars so that's even like 3k which is huge like how many people
are like actually getting uh customers through cold emails like this or or like through not
cold email through like ppc or like uh you know referrals like just like this like not so many
and with an internal cost of like you know 400 dollars 400 a month it's pretty crazy for the
pricing what is a good starting point for pricing your services for new and don't have too much
convection in your services so you need to have both statements guys um I always say this so you'd
have uh obviously at first you're not going to price like something like 5000 like 6000
you're going to go over that like 1500 1600 just because you don't have that many clients
and uh eventually you would just basically uh have like a bunch of case studies so the
goal is to actually like really really really knock it out of the pocket the first few projects
and once you do that you're eventually good to go so you can just leverage social like you leverage
that like you would have like your first thing you got to like write in your emails your social
quote so I used to say something like hey i'm sad um like i'll have like a one-liner like a
personalization uh that asks a question and then i'll have i'm sad i directly added this amount
to this company so the front load the hell out of social um okay
okay
so let's uh admit awesome so let's go back so the next question before we go into the chat is
client acquisition without a testimonial how do you win clients trust and leave your first
deal when you don't have testimonials on a track record yet so uh answer this uh basically let me see
let's see uh yeah so even if you don't have direct experience in this industry like you can
showcase relevant projects you've worked on or you can take my blueprints and tweak them like
who gives a shit like you can just go ahead and take my blueprints you have my blessings
uh and like say something like i built a system you can just go ahead like maybe change like a
copy just a tiny bit and then you would say something like hey i built a system that generates
x appointments per month that's been in the sent on ads so you can just go ahead and say
ads because i understand guys like let's be real like i don't like this cookie cutter advice
that tells you like like i'll never be that guy that gives you like cookie like cookie cutter
advice i'll just tell you like no fluff no bs like just do that and then i would leverage that and
if you've worked in like any entry before i'd showcase that too i'd say something like hey uh
so for me like i like my brother i used to work with him uh and he had like a consultant firm
basically he consulted with sap and i helped them uh basically like six months and what i've said
is i worked with this firm consulting and then i built this sort of system that generate x
appointments and that's that should that should be like pretty much you should be pretty much
good to go just because i know like at first you don't have case studies like how the hell are you
gonna like put social proof so i understand where you're coming from so i would just do that and
then i would also like create a hypothetical case study on how you would approach their campaign
so i would say something like um i built a system that would basically help you
connect with these type of clients um and then i would deliver you this amount of book meetings
or your money back so that would also work and uh yeah this is exactly what i would do
so uh the last question before we go into the chat so delivering without a proven system what's
the best approach to fulfillment deliverables when you're still developing your process so
understand that your first projects are going to are going to suck it's okay i'm constantly refining
my myself process so uh you don't have to like go crazy about it at first so just have like a like
a link pipeline where you minimize the steps of any lead that comes in into your pipeline because
you don't have bottlenecks right now uh so basically bottleneck thinking is when you
have like an entire giant bottleneck where there's a lot of friction on a client end
so you don't want to have that you want to have like a basically just they book a call
they receive a thank you email um they get added to your crm you nurture them like for 24 hours
before the call five minutes before the call you get into the call you close them after the call
you send them a thank you email that is automated and then once you do that you just send them a
proposal and a proposal i always recommend you use pantadog because it integrates with stripe
now i want you guys to minimize the hell out of friction of the clients who just click one
button they pay you so a pantadog actually like uh integrates with stripe and once you have your
stripe you can just connect to pantadog you send them a proposal you can basically template the
shit out of it and then um you would essentially have everything there we client deliverables
everything there and then you send them the proposal if they like your call great uh then
they they could just sign and basically get redirected to your stripe page right and then
you can just pay and once they pay you get started to the project so as you can see this is like
extremely lean very easy you don't need to like go on a basically like three close call unless
you're working like with enterprise level companies so then they have like this demo call and then you
have introduction call but uh at first if you're working with like less than two million dollar
companies then i'll just like i'll just go ahead and have this this process i understand that at
first you're still going to be developing your process as you go right like even like bigger
like the biggest companies on earth like google this they're still refining their process so
managing software should clients create and share their own software accounts or is it better to
factor these costs into your pricing i would always factor these uh costs into my pricing
just because again it minimizes the hell out of the friction uh if you tell them well you're
going to pay the setup fee and you're also going to pay for the software it's the likelihood of
them you know working with you is going to be like five to ten percent lower i'll just be like hey i'm
going to deliver the booked meetings you just pay here pay the upfront fee and then make it
extremely extremely straightforward and easy for them to not say no and then you just go ahead and
sign up on your own it's going to take like a few minutes uh and then you're pretty much good to go
so let's go ahead and answer the questions in the chat awesome okay so i'm just gonna go on top
so um friends uh yeah yeah they have weird restricted laws
hey sad i know you've touched on this but can you go briefly uh go over the principles and processes
you take over and comes to custom we're creating a copywriting custom creating copywriting for new
campaigns for both your own campaigns and clients and how you iterate on them once changing the
variables of the campaign after receiving data yeah that's a really good question so like i said
uh i'm just gonna go back and i'm gonna explain to you so i i think i already have this
ready so uh let's just go yeah so basically
you do not want to change anything at all so let's say you send like let's say you have in
your campaign yeah so you send you have 5000 leads right so you have you you you scrape 5000 leads
uh this is the input right the leads and you uh basically expose them
through the right conversion tools
through the right conversion tools which is going to be your copy you're using
the leads where you're getting your leads the the offer huge and then basically um your guarantees
and your ai personalization messaging and subject line and all of that now
all of this right when you expose them you're going to receive data so this is going to be data aka
metrics like i said if you fall between kpi which is two percent reply rate you do not ever ever
change anything just what just add the volume if you're sending like 5000 like you have you've
sent your first cold email campaign yeah right and you got two percent reply rate off of that
great now the system is healthy right like two percent a doctor has like a like a bunch of uh
you know kpis that denote the health of like the the health of a body so they have like a healthy
bmr healthy uh i don't know like bloody lipid i don't know something like this but you need to
look at your cold email system just like a body you need to diagnose it if if it falls within kpi
which is two percent reply rates and basically you got 100 replies 20 of them were uh and i'm
just being you know conservative you could get more uh if you get like 20 percent that are positive
in the 100 replies then what you do now you know that the system is working so what you just what
you do you just add volume that's it like you're not we're not trying to optimize here
all the thing like everything that we're trying to do is to uh find a proof of concept that works
that we can add volume to it that's all what like scale is like you do not try to optimize
just add more volume and it's gonna work so yeah um
um all right let's go to
campaigns under your own one yes uh one instantly account so basically i had one instantly account
i was operating as a one-man agency making 50k a month as a one instantly account so you don't
really need to go crazy about it so uh what percentage should you reach out to for informational
interviews for selling your services um what percentage of your leads you should reach out
just for uh well you have to understand that you're going to get more replies obviously when you
just uh reach you now for informational interviews so i would i would definitely go for selling your
service i would up the volume right here just because i know like i just know like uh for
reaching when you reach your now for informational interviews you're going to get a shit tone of
replies like i know uh i used to work with this consultant and he he what we used to do is he
would basically pitch people into their his podcast and in his podcast especially like a lead funnel
for him he would bring in people and once you bring them he would pitch them on the on the
podcast he like he would like spend like 30 minutes asking them questions and as a podcast
host and then at the end what he's doing is this is just transitioning like into a soft cta we're
like hey um and here's the black bear built over there like he would know someone and you he was
like he would be like are you do you have any problems with lead garrison currently and you're
like oh i know a friend and that friend is like basically a partnership so they would connect
themselves and then they would just sell him on a cold email system which is pretty pretty rad
um should we get uh well personally it's not intuitive for me but i'm following along
uh should we get all the make automations set up before creating and sending emails um yes but um
i will go deeper into that in the 12 day agency just because
there's some technical aspects that i'm going to explain
uh thanks for the work no worries jason i'm here to provide as much value because i believe
uh you only get awarded when you provide value that's just how word works when you get on your
first call with your clients do you present something on your screen or just talk one-on-one
and try to make a sales like that now you don't have to do that unless you're working like with
big enterprise so i've worked with this company called uh it's called coincidiv and they're
affiliated with microsoft and they're basically like 20 million dollars and i had to go like into
this suit and tie bs that they have to do is like corporate thinking so um i wouldn't i wouldn't
worry too much about it about a presentation unless the deal is like you know 15 20k so at
first i would just go ahead and uh so in terms of sales call i'm like we're going to have like an
entire video on that like entire video we're going to retrieve the case like all of their case study
that we scraped and we're applied to them in our initial email and then we're going to get that
and then we're going to be we're going to be saying something like hey basically i have an
idea so it's your ideal customer and the way we do that is by xyz and your sales call like you
literally like uh have like a conversion rate of like 20 20 25 percent when you say that because
you're speaking zero language you're like hey instead of just coming and pitching you my five
book meetings i'm like hey i'm going to connect you with five book five clients uh and here are
your clients instead of just asking me what is your clients in a sense so if it is a challenge
in getting set up in u.s what in turn if you determine stripe uh curious would you suggest
a c corp over an lc when using a stripe atlas so i like a stripe atlas just because they allow you
to have like 20 000 of fees but uh i have an um i have an i think i have no i have a c corp
so c corp is better in terms of taxes but like i'm not a lawyer so i wouldn't know i wouldn't
give you like a advice just because it's different how to hire as sales when we don't speak fluently
english if you don't speak english fluently i would just so you have like you need to have
like a conversation with yourself so it's either really two choices if you have the budget you
would hire someone like a sales rep uh and then they would take care of the call but keep in mind
that requires budget and requires like a bunch of training because they don't know what your
offer is or you can just go ahead and learn english so this is like a no fluff advice you
mentioned that you purchased the annual version of the instantly 97 a month uh you can send
125 000 per email but you can only have 25 uploaded leads yeah you can say like you have uh 25 000
leads uh per account per month so assuming you have like let's say you have like four clients
it's pretty much like that's more than enough to get them like their five book meetings
yeah but when you have the the niche already dialed down uh you should be pretty much good
to go also how many leads should i have when i start my calling making a payment for my agency
uh like i have the entire document in the school classroom guys uh five thousand leads are enriched
and scraped and validated not just five thousand emails like leads are that have everything there
so regarding niching down how do you scale when the leads dry up for instance the it service
healthcare mentioned let's say they had 2.2 available what do you do to expand further other
than that for new leads to be posted and scrape linkedin um okay let me actually answer this
in detail now we have to understand that the it service for healthcare is a pretty pretty big
niche so uh like our entire community can essentially just eat because uh like i like i've
worked a lot with those companies and there's many leads if you just go to apollo um if you look for
companies that are it basically it like any it company basically they have uh solutions for
healthcare it could it could be software it could be crms it could be uh basically uh
automation tools it could be um like platforms that are tailored for healthcare just because
you have to understand that healthcare have some weird regulations about patient care patient uh
privacy and all of that so they can't automate stuff just we do like like using make.com like
using like just generic uh platforms like software there's companies that are actually
like actively every day um you know being built to essentially just uh expand the market and now
with ai the ai wave like there is a lot of you know um ai platforms that would be incorporated
into healthcare so the next question with from liam is how do you come up with different lead
lists for your clients after each campaign how frequently do you run campaigns for your clients
so it really depends so uh if you uh so i use a variety of you know basically sources the three
main ones are going to be linkedin sales navigator to phantom buster and from phantom buster you would
go and take that data and you enrich enriches using any mail finder or drop contact or you
could use apollo and you would use mail.so now the beauty about this is uh apollo is pretty free
you just sign up with the g suite accounts or like a professional account and you would have
apollo lead scraper on apify and you just if you sign up to join secret they give you 250 000 mail
credit so you can essentially just keep validated until the end of earth so you would you would take
that right uh you would take the uh the list from apollo and then you would basically just
add this to mails.co and then you validate emails once you do that you're pretty much good to go
another system would be scraping companies that are hiring for their services
for the just like the sdr or like maybe like they're hiring for like
i don't know like seo specialists or something you could just look for companies that are already
like trying to find that role and you just pitch them as a contractor is there a video about
building the email infrastructure um so it's going in guys it's just because the 12 day
agency it's like 12 days to build your sales agency so um we're recording like a video every
day just because i'm putting my in like in your guys shoes uh but we can uh speed that up so
today we're gonna put i'm gonna be posting two videos just because i want to like uh i i want
you to digest first before i keep front loading information to you um so in a sense we have to
become experts in helping our clients figure out with their niche audience if they do not know
and more importantly for those who know exactly where their niche is how to find them correct
and then we have to help them tailor an offering that was be there in each and under what we'll
have to figure out also be able to do on demand for each scanner if this all correct pretty pretty
smarts that's how you scale so can you get away with just using apollo and not needing sales
navigator you definitely can my friend uh if you uh but just keep in keep in mind that uh you will
get rate limited so i have no uh like i don't care if i'll just give you the gray hat methods
just because uh i want everyone here to make money the gray hat method is what you do is you have uh
is if you have basically like a bunch of ge like professional not professional emails right and
you sign up to a bunch of uh you know apollo accounts and you can essentially just scrape like
maybe like six thousand a day you know you can just bypass that
yeah i mean yeah you gotta do what you gotta do um i mean they obviously they do not do not want
you to scrape their data but obviously the their plan is 99 bucks and now we're trying to lower
our margins to like increase our profits so makes sense but how do we differentiate differentiators
ourselves okay first if i haven't had six replies after reaching out to 1414 really yeah uh you
should continue with your campaign so if the campaign was just 1400 leads you cannot denote
the health of the system unless you find 5000 leads 5000 lead is that like the normal like
typical just standardized uh campaign how many uh like if if you get like no responses no replies
from 5000 lead then what i would do is i would keep uh i would i would then like have to like
basically just diagnose the system and then i would basically just keep all the variables the
same but change one thing so it could be a copy um guys uh please if you are running cold email
campaigns just put like an off audit and decline in the community and like i will come in and
someone will come in and just will roast and audit your offer and also your messaging so just feel
free to do that we have that like in our community how do we differentiate ourselves from other
companies um that scrape and present the same lead list i understand that we personalize the
hell out of it but if they um the hell out of under the personal however if they receive those
leads before um how are we better well this is how business works basically it's kind of
like burger king and uh and mcdonald's so i would say um well you're gonna basically
it's not 100 going to be like the same lead list like you might across like came across like us
like like some companies that would be like hey we heard uh we we got pitched with a company
before like this and this is how we offer will stand out so the way you differentiate yourself
is basically by the offer you have a great offer
okay so uh a multiple email creation tip that i use and you all need is one professional email
if your email is john agency you can use yeah and then communication to john yeah you can do that
but i wouldn't recommend do that when you send you cold emails just because you need multiple
domains because you're gonna burn this domain right so you'd have like tryagency.com it's
agency.com so uh you can do this if you want to sign up to apollo and use their platforms
oh yeah yeah yeah perfect um in the 12 day agency you're actively putting it out uh will you cover
the full detail of scale climate to handle yes yes this is the entire thing such as make automation
cr management for yourself yes i will cover basically the end to end for blueprint to get
51 000 yeah 51k like everything because i want to go like beginner stage like to like more
it's like a little bit medium and then the end will be like pretty cracked so you guys will have
like a the biggest sauce uh awesome i think that's it these are all the questions that we have
the last questions that we have in the thread i will we will answer them like the next week
so uh we're just gonna please guys if you have the the last questions and your questions have not
been answered just copy them i'm gonna make a thread just after this call right here and then
just copy them and paste them at first and then i will go over them the the first thing and the
next weekly call so if you guys have any more questions right now in the chat we'll go over them
what if we can't find 5k verified leads on apollo now if you don't find them in apollo you can find
them uh in linkedin like linkedin sales navigator could use linkedin uh you could use uh basically
uh there's a couple hacks um you would look for companies so i'm gonna give you like a hack
it's a pretty advanced one so what you do is you find one ideal client that we do that you would
that you would want to work with and you could look for their following right you would look
for their following and you would see basically companies that are associated with you go if you
go to phantom buster there are a bunch of phantoms right here so here's the platform the phantom
buster is great man uh if you go to linkedin leads and you look for linkedin solution there's
many like you can scrape leads from linkedin posts you can uh linkedin search exports just
did linkedin sales nav uh linkedin profile scraper uh linkedin basically a lot of stuff here company
scraper all of these scrapers you could use and there's linkedin sales nav which is the one that
we use right linkedin uh sales navigator search exports oh we're gonna cover all of that how to
scrape leads uh in the course so yeah that's uh that's pretty much how i would get them
eventually like you would never get run out of leads if you were like on uh linkedin just
because they only allow you to scrape like 20-25 hundred leads per day and i wouldn't recommend
doing that doing more than 25 because your account your account getting banned
and awesome
how do you prevent difficult leads for concurrent campaigns for a client so well
instantly handles this automatically so when you upload the leads they will check for all the
leads that are duplicated in any campaign so it will be perfect so if i wanted to
uh if i wanted to scrape leads for a b2c vc business is instagram the best bet so if apollo
or phantom buster better for this yes yeah b2c i would just scrape i would use phantom buster
i would scrape instagram and then i would also scrape instagram using apify let me know if you
guys want like an uh like a system built live and i'll do that and just record that and just post
in maybe like it could be like just a community exclusive uh just let me know guys um yeah and then
and then
specifically appeal that our audience yeah
so once we select our niche should we focus on website copy and branding exclusively on that
yes so once you uh so once you pick like a b2b niche right you would just tailor that into that
like a website content to them and obviously the branding should be tailored to them you're
speaking their language uh just to confirm that's that is 5k per niche that we should be saying yes
5k okay awesome how would all these lead generous in software cost like i would say
about 400 dollars per month um but think about it like uh the cost rack was just like 10 to 15
times lower so yeah thanks so much guys for coming into this call and uh hope you had uh
fun just like i did uh talking to you guys i love the interactions on chats just because
live and it makes me like thank you guys and then next week
