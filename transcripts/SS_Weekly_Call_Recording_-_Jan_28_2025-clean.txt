What's up man, hey, what's up everyone, this feels like a family because we've been having
a lot of calls lately.
Yesterday we had like a great call with <PERSON><PERSON><PERSON><PERSON> and then I'm just gonna wait for him to send
me the documents just because he wants to fine tune them for you guys and then I make
a post and I'm gonna pin it in the community so you can just go ahead and re-watch the
video if you guys want to and also the resources just because the resources were amazing.
Alright guys, so let's just wait for people to come in, hey <PERSON>, okay, so I'm gonna
start sharing my screen and then we're gonna go ahead and tackle all of the questions.
Like always guys, the first 30 to 45 minutes is going to be for the questions that we have
in the thread.
So we don't have that many questions in the thread so it looks like we're gonna go ahead
and just answer your questions in the chat, which I like it, it's more of a personal instead
of just reading a question.
So I'm gonna go ahead and share my screen and then let's get into this.
Alright, so the first question, so this is a pretty long question and I have a presentation
for you my dude.
So first question by our friend <PERSON><PERSON><PERSON>, so what do you think about asking our clients
to make an account on Instanly and Google Workspace and adds us to it, especially meaning
that they own the infrastructure versus setting up for them their own Instanly account, meaning
we own the infrastructure.
That's a good question man.
Last question, what do you think about sharing for free with documentation how to use it
should they decide to part ways which will only be possible if they own the infra in
the first place.
Do you think that's a good idea?
How do we pay for the infra?
Are the setup costs included in the total invoice paid or are the setup costs separate?
So let me walk you through the difference between setting up everything on your end
and setting up on your client's end.
So in terms of setting up everything on your end, the first thing is it gives you leverage.
So when you own an Instanly, it gives you leverage to keep the client working with you.
If they rely on your tools and setup, it's easier to maintain a long term relationship
because they see you as an essential to their business operations.
Just think about it when you build everything on your end, it's only going to be cheaper
for you but it also gives you leverage.
You basically have the keys to the castle.
So if you stop working with them, the business will stop working overnight.
So it gives you a huge leverage which I think is very important, especially with how many
shitty clients you encounter because I've encountered so many shitty clients in my career.
So since these systems run in essence, their entire business, it gives you a huge leverage
so you can go ahead and you basically have more authority and it gives you this perceived
professionalism that you own everything there and you basically handle their entire outreach.
Another thing is it's faster, right?
So the reason why setting up everything on your end is faster is because if I'm doing
everything, the setup, I don't have to wait for the clients, I can just go ahead and basically
have the discovery call, close them, onboard them right away and basically have everything
on my end.
I don't have to wait for the client to create accounts to share logins, therefore I can
go ahead and deliver results faster.
So you can deliver the results faster in like a few days, especially if you have pre-warmed
up emails.
So some clients you might be working on are not that tax-savvy, so you're going to have
to walk them in the onboarding call by signing in make.com, signing in instantly, buying
the domain.
Sometimes you tell them, hey listen, you're going to buy this and this and that and you're
going to give them like even like an SOP documentation on how to do that to wait a couple of days
until they finish that up.
So yeah, there's definitely like a lot of issues with that.
So I would also, there's another thing, which is figure out how things work.
I can just get started immediately.
The next thing is cheaper.
So I already have accounts for these tools such as Apollo, right?
Say you're scraping leads using Apollo and then, for example, like you have LinkedIn
sales navigator, you have Phantom Buster, you have, let's say you have the paid up.
So instead, you can just go ahead and get up and running easily.
It's going to be more cost effective, especially if you use it instantly.
So what I would recommend, once you start getting a few clients under your belt, you
would get the instantly yearly plan, right?
And then that yearly plan is going to basically help you with that.
You don't have to pay $97 each month, right?
For each client.
So you can just go ahead and sign them up and just give them the dashboard.
Like I usually do it now.
So it's more cost effective to use the existing subscription rather than asking the
clients to get new ones.
The second thing is it's easier for the clients.
So some clients don't want to deal with creating accounts, right?
So especially if you're dealing with founders, they don't want to deal with creating
accounts, they want to leave all that for calendar time.
So they don't want to know how these tools ended.
They also hired you to handle that.
So you make it easier for them by doing that all in your end.
And at the end of the day, they want booked meetings in their calendar.
Another issue you might encounter is that if you tell them, hey, here's the price,
here's the setup fee, let's say it's $1499 or $1500, you tell them, hey, here's the
setup fee.
And then you're also going to pay for costs, software costs.
It might be a little bit harder for you to close that way unless you say, hey, we have
a setup fee and that setup fee will include all the infrastructure that you will be paying
for. So you're going to say, OK, in my contract, just like in this question right here that
you have.
So, yeah, are these setup costs included in the total invoice base?
So you'd include them in your contract.
You'd say, hey, here's the entire setup fee plus the software.
And it's like depending on the price, you would frame everything as $1500 or what pricing
you have instead of having it separately.
Now, the second thing is setting up everything on the client's end.
Now, also, there is advantages and inconveniences.
So if the client sets up accounts, they fully own them, even if they want to take control
of the system or work with someone else in the future.
So this also will help you close them just because they don't want to like it moves
that perceived.
It helps you not like it makes you like in a way they don't like like you have to think
about it, guys, like these some of the clients that you're going to have in your career
will be like, well, I don't want to be working with you all the time, right?
So like there's some companies or clients, they'll be like, hey, I don't want to have
like long term commitments.
So there are some clients like this.
So this helps, right?
So in your discovery call, it really depends on the client.
So when you're in your discovery call, you're going to be asking them, well, would you want
to have like you're setting up everything on your end?
Here's the price for that.
Or here's how we handle everything like on our on our end.
And then here's for that, too.
So this is what I usually do it's what I used to do at first.
So depending on your offer again.
So another thing, sometimes you have difficult clients.
So let's be real.
Like if a client seems like it's going to be a pain in your ass or doesn't value your
time, it's better to have them set up that way.
You don't have you don't end up chasing them for payments or access later.
Another thing is long term flexibility.
So it's huge.
So once you set up everything on the client's end, it makes it very easy to offer.
So let's say you don't like the clients, right?
Maybe the project is over.
Maybe perhaps they let you go, right?
You know, like previously, you would have to manually transfer each scenario workflow
into another account.
Probably it's going to take like three or four hours.
So what I would recommend.
So the address, the elephant in room, what I would recommend is basically setting up.
Setting everything on your end.
And once you do that, right, once you set up everything on your end, it's going to help
you reduce costs and it's easier for the client.
So this is what I would recommend you do.
Like always set up everything on your end and make sure you because it's going to help
you. It's going to help you like make it more cost effective for you.
How do we handle the infra?
Are you set up included in total invoice paid?
Yes. So if you're going for the the latter one, which is going to be setting up everything
on your end, you include everything there just because I want you to close it.
I want you to close the clients.
I don't want.
Want to sort of friction for them and it's just not worth it.
So I hope this helps, man.
If you have any more questions, just drop them in the chat.
Axel, so how do I know if I'm rate limited in AP, if I am currently trying to scrape
leads with fast indeed job scraper, but all of a sudden I'm not getting more than a hundred
results per search no matter what.
What could I be doing wrong?
Yeah, man, it looks like you you actually been rate limited.
Are you making sure that you're using this filter here?
If I go to AP, if I there's this.
If I go to console.
Fast indeed job scraper, make sure you toggle off this.
Indie job scraper.
So I believe this is the one you're using.
In the wrong option, it has to be no timeout.
So it's not going to stop until it's until it like it finished scraping the entire pages.
Let me know in the chat.
Yeah, no worries, Wayne.
Yeah, you have that on.
Yeah, it looks like.
OK, in terms of Max jobs, are you making sure it's there's more than that, for example,
let's just put 500 here and let's just test this.
And then we're just going to put in, we're going to leave everything here and then data
center. OK, this is awesome.
OK, so let's just start and then see if it's going to give us 100.
If it's going to give us 100, that means an issue with the scraper, 100 percent.
So click save and start.
Yeah, yeah, so it looks like you only can scrape 100.
So what I would do, man, is I.
Usually when you scrape this, it's going to give you more than 100.
So it looks like there's an issue with that.
Might have to email the the developer.
So I would do in your case, man, here's like a little hack that I just thought about it
now. So basically you would have a bunch of filters.
Maybe you tweak the filters, you would have teacher.
And then what you would do, let's check this.
So let me actually see if we can do this.
Let me see if we can troubleshoot this.
So what you would do is you would you would filter by 24 hours.
Let's say, for example, Martin manager, market executive, and then I believe Manchester,
I think you told me.
UK, oh, because we're on in USA.
So let's just put New York, for example.
So what you would do is you would put and you would filter by 24 hours and then you
would scrape this page. And then what you would do again is you would remove that
filter and then you would scrape again with the last three days.
So when you filter by 24 hours, it's not going to give you the last three days.
So you just like removing all the 24 hours jobs and you just scraping.
So you would like you would essentially have like three, like 400 leads here.
And then I would also maybe I would say, you know, I'm not going to do that.
And then I would also maybe I would also filter by remotes or hybrid, just because
what I see here is it's only like this scrape of only scrape one hundred results from
the page. So the crawler is just going to go through the the jobs and it's going to
stop at one hundred.
Yeah, so this is the issue with that.
So again, what I would do, I would use this.
I will still use the fast job scraper, but I would also use a combination of a bunch
of other scrapers, such as this one right here.
So there's a like a million.
There's this one from this guy right here.
There's the this guy from Curious Coder.
I would also use this one right here.
I think.
I've made it under maintenance.
So, yeah, I would I would use a bunch of these.
I would also use this one to indeed job scraper, new PPR paper results.
So this one is new.
There's only 40 monthly users.
So I would use like a combination of that.
So this is what I actually do.
Like I use a bunch of bonds and also use LinkedIn job scraper.
So LinkedIn job scraper, I use this first one and these two.
So these are the ones that I use, especially this this guy right here.
Be be.
You get I keep in mind this thirty dollars per month.
So, yeah, so I would I would just use all of these scrapers and then we're going to
have to come up with our own one.
So now that I think about it, maybe we can just come up with our own internal
scraper in the community.
Yeah.
So, yeah, I mean, I hope this helps.
Make sure you follow up with me in the ends and maybe I'll record a video for the
community on how to basically tackle that.
We have our friend Jason.
First of all, congrats on the last win, man.
Really proud of you.
Second of all, let's read through the question.
I have a potential.
Why, I want to know if we give them the leads leadless after the contract has
ended, I wasn't sure about this.
Do you give them the least if you are giving them a dashboard and instantly
premium, then they can see and download the leads anyway.
Yeah, man, if they ask you if they want the leads, just give them the leadless.
But what I would recommend just make extremely aesthetically pleasing, right?
Make sure the Google Sheets is extremely aesthetically pleasing just because what
the clients see is basically like the end deliverable.
So make sure you also include all of the information that you scrape just because
more data is better than less data.
Make sure you include all the fields of the scrape and make sure aesthetically
pleasing and you can just include that in your deliverables.
Actually, you can, you can essentially just give them the documentation of the
system, the make.com system, the blueprints, you can just go ahead and take whatever
system you're building them from the classroom.
So we have everything here.
If you guys been checking the classroom, I've made sure I updated a lot of things.
There's new modules here, too.
So there's the first one.
Send your clients notifications.
I'm going to walk you guys through it shortly.
So you just basically take in so like let's say you're scraping Apollo, right?
You take in the system, you would record a video documentation along with an SOP
and you say, hey, along with a a dashboard, you're also going to get a delete list
and you would frame that in your offer to and you just add that in Google Drive and
then send that to them and you can use an email, whatever method you're using.
So that's that's going to give you like a perceived authority to give you like it's
like you're delivering, you're like you're over delivering basically in the project.
So these are the questions that we have in the thread so far.
We're just going to go ahead and basically now answer all of the questions that you
guys have in the in the chat.
All right.
Jason said, OK, great, I'm giving them a proposal.
Thanks, of course.
Yes, always over the level, man, like what I do is like whenever I have the chance over
the level, especially at this moment now that you can give them the lead list to make
sure it's extremely aesthetically pleasing, it's really going to help you with the
option and the retainers, right?
As you guys saw yesterday, the call would be a beautiful presentation and like when
you walk with clients, they really like that, you know, aesthetically pleasing
presentation, you're walking them through that just because like 80 percent of the
actual deliverable is how it looks.
Right.
OK, guys, so let me know if you guys have any questions in the chat.
I'm just going to go ahead and talk to them now.
Yeah, yeah, I'm glad you asked me that.
So, yeah, DeepSeek is going crazy, guys, like everyone is going crazy about it and I
tried it and let's actually walk through what you guys do it.
So if you guys don't know what is three shot F one just means when you feed in three
examples, the results are ninety one percent.
So the results are ninety one percent, which means it exceeds Claude and Claude's net
is the highest, like most intelligent one that we have currently.
And now it's like it exceeds Claude, which is ninety one percent, like ninety one point
six percent. And also it's eighty nine percent more than Claude, it's eighty eight
percent here. And it's also like substantially higher than GPT four.
Oh, and GPT four.
So I've tried it, but looks like now there's like an issues.
So it looks like they have like a like a problem with they got like, like they said,
due to large scale malicious attacks.
So it looks like there's some malicious attacks into DeepSeek and you guys know it's
Chinese. So it's probably why it's getting now some malicious attacks just because it
looks like there's a lot of.
Politics issues right now, so maybe why they're having like a lot of attacks right
now. So, yeah, I've tried it earlier and it's crazy.
So maybe we can like build out a campaign with their API, it would be fun to build
that. So so far, it's way, way better than than GPT four and way better than CNET,
right. And it's also substantially cheaper.
So I read the news and they said that.
It's 18 billion of OpenAI stocks, so that's.
That's mind blowing, man.
Do you have any? I used it via open router and I got access to make could use it in
their web. Couldn't you? Yes.
Yeah, there's some issues with their interface.
OK, let's read through this shitty have an issue with dice from your video yesterday.
What do you do when most of their career as Zipper crew?
Can you get an email from 600 leads?
So basically.
Clearly, we have an issue with that and we have a fix for that.
All you do is you use the method of basically querying and making HTTP requests to
basically the company name, which is going to be a company name plus a hiring manager.
And let me know if that helps.
If not, just send me like just send me a message on on school and I'll help you out.
Just because. Yeah, just because Axel was having the same issue.
When does Vishal sales call come in classroom?
So it's going to be at the end of the day, guys, just because I'm waiting for Vishal
to give me the resources because he's fine tuning in and then upload everything for
you guys to watch to rewatch that.
So don't worry about it.
Sam says, do you have any tips and tricks about writing prompts for the personalization
of email? I know the prompts are really good in the community, but if I want to adjust
or make them myself will be a good.
So you want to write prompts to them?
OK, so if you've been using Claude, let me walk you through this.
So if you have Claude, you would go and Claude, I and here's what you do.
You would go. And go to the console.
So there is this thing right here.
This is what I use. It's called write prompts from scratch or generate a prompt.
So now you can even improve an existing prompt or generate prompts.
And another thing you can essentially write your entire knowledge base about cold
email. So the way I used to get my prompt that we use as self agency and why I've
managed to make it extremely optimized is using this exact same.
That we have here in Claude.
And if you go to a optimization, a prompts to help you write better copies, all of
these are for you guys.
So these are few prompts for market research and scripting that I use daily.
So all of these for market research to build in.
So such as write this in the fifth grade reading level, but includes specific lingo.
What is this something that keeps the CEO of a niche company late at night?
So just to retrieve the pain points of each client that you're going to onboard,
write an imaginary conversation between the CEO, CEO of a niche, trying to achieve
end results. What is the end?
The dream end results companies want in regard to generating new revenue.
All basically come in to Claude and you would just have generate a prompt and you
would just describe your task normally.
Let's say like I want, I don't know, like depending on the problem you want to be
using, you would just feed in a draft and like
basically a Claude is going to generate an optimized prompt.
For example, like generates.
I want to be able to
digest each website's information and output.
The latest case studies, for example, if you click on generate.
So this is like the, like the most laziest way to write it.
And Claude is going to generate, generates basically like an entire prompt for you.
And you can just go ahead and say, Hey, you're our task with extracting and
summarizing the latest case studies from a given website's contents.
And then you would put in basically the website content here.
And then this website is just going to be texts and make, and then you would have
basically other things here, such as case studies.
And if I click on continue.
Cool about Claude is you can click on continue.
You would have the prompt right here.
So what you can do is just click on this button right here.
And the website content would be here and the number of case studies you put too.
And you put in the website script page and you just click on run and
everything is going to work.
So let me know if you guys are using the Claude's playground.
It's really good.
Um, with mail that I saw, I'm not really sure what you mean, uh,
with the email that I saw shitty.
Is it, uh, like the dice scraper, right?
So yeah, the dice scraper, you would have a filter that checks in if I, or all.
Yeah.
I mean with the dice scraper.
Yeah.
Yeah.
You would have, uh, yeah, contains, uh, apps, right?
Yeah.
When I put that, when I put that, nothing goes through.
It just stops us.
So also I had 600 leads and then I couldn't go any further.
So when I looked at everything was zip zip recruiter.
Okay.
So looks like, okay.
Um, so I cannot really tell you exactly what to do.
So what you should be doing is just send me the blueprint and
I'll record the loom for you.
I'll troubleshoot and send it back to you.
Okay.
So for today's recorded video in school, this is what I was missing.
Yeah.
No worries.
They're pretty sure you're talking about the fulfillment video.
Yes.
Yeah.
Hopefully that helps.
Um, yeah, no worries, Sam.
Okay.
Um,
so let's see if we have any more questions.
If I'm missing any questions here.
Uh, when, okay.
When using your prompt to find B2B niches, how do we know which
company to reach how between the two?
Um, so typically you would ask the model right away, which would be,
which would make sense to, to, uh, target.
And another thing, what I would do is what I say, like just when I read
through the niches, I say, how well, let's see, like what would be most
straightforward way, right.
And the easiest way for me to basically, uh, campaign, right.
Like what is the easiest niche I can go for?
For example, let's say I want to, uh, uh, target marketing
agencies with e-commerce, right.
So both of them each other, right.
Just because e-commerce always need awareness and brand awareness and SEO,
like ads management for, for e-commerce.
So just me thinking about it, it's going to be very hard for me to find leads.
Like commerce leads.
So I'm just going to go ahead and reach out to marketing agency just because
a million one marketing agency.
Just think about it.
Like what is the easiest way for me to get leads from myself?
So I can target none of them.
And then once you know that that's it, just go ahead and target them.
You can also like use AI to help you with that.
But, uh, like, uh, it really depends.
Right.
So for example, I, I'm going to be building a seven day automated, uh,
campaign series where every day I'm going to build a campaign and I'm
going to basically do it live.
It's just to show you guys exactly like how it works.
So there, there's this digital marketing agency and e-commerce businesses.
So there's a national demand between the two.
There's an ongoing flow.
So what I, just by thinking about this, just because it's easier for me to get leads.
Another thing is cleaning services and home building companies.
So just thinking about it, I know I'm going to go for home building
companies just because it's easier.
Like think about it, like cleaning services, it's kind of like B2C.
So it's not really easy to get client, uh, get leads for B2C.
We'll just go for home building companies, healthcare
providers and insurance brokers.
What I would do is I would just go for healthcare providers, right?
It's easier to get clients, easier to get leads as a health for healthcare providers.
So yeah, so this is what I would do.
And I felt was on your, um, wonder if there's a way to scrape Google maps
locations to say, uh, that they permanently closed wonder if there's
an API scraper that shows this.
Is there any better way?
Yeah, let's just, let's just go ahead and look for this, man.
Let's just go to API, go to store and Google maps.
So Google maps extractor.
So, um, run filters, skip closed places.
So what you, uh, let's see.
Yeah.
So Google maps locations that say permanently closed.
Hmm.
That's interesting.
She probably make a video about that too.
Um, let's see, let's, let's see if there's another scraper and go for, so there's one
in the people isn't this, do you want to extract questions, search filters and
categories.
So there might be a way just worth looking through all of these API scrapers.
I don't have one on top of my head, so maybe we can just look for this.
And if you guys have any idea about this, just post it in a community and, uh,
we're going to have to do a little bit of research when it comes to this.
Yes, skip closed places.
Yeah, that might work.
Um, she, he says, uh, what's involved in building a scraper?
Yeah, typically needs a little, no, a little bit of a coding and like to
understand that I'm not, I consider myself the way, like the best coder.
Um, so yeah, we would build this using puppeteer for the community.
And then, um, it would typically have like, you, we would go and build
this here in API, right?
You would go to Actors and you just create your own, right?
But, uh, I think it's, uh, you have to go to API and then I believe you have to
go to, um, let's look for this API build your
own, uh, I think you can build your own, uh, yeah, we'd go to docs.apify
and you would basically just, uh, build your own.
Yeah.
It's going to take a little bit of time, a little bit of coding experience,
obviously, but, um, it's either going to be using it using Crowley, right?
And then you can, you can use AI actually to help you doing that.
Um, I believe, uh, now we have like, you can use GPT four o'clock to help you
with the boat, but it's going to take a lot of, a lot of time to win that.
Okay, guys.
So, uh, Hey Saad, do you use pre warmed up emails for your client's
campaign or do you typically warm them up?
A few weeks after, after you take up this stuff.
So, so, uh, that's funny.
Shady, uh, do you use pre warmed up emails?
Yes.
Currently I use pre warmed up emails just because it's easier for me to
lever results quickly for the clients.
Yes.
So, um, typically dependent on a setup fee.
Let's say if the setup fee is more than 1500, I would just go
ahead and buy the pre warmed up emails.
Right.
And then the only thing that you have to do now, build the offer,
help them refine the offer, give them the leads, uh, personalize the emails.
And then you just go ahead and start the campaign easily.
Right.
It's easier, uh, because now you're interfering with their clients, with
their ideal clients and their market quickly, so you know what works and
what doesn't instead of waiting 14 days.
Right.
And then check in if the offer is going to work.
Right.
So what I would do, like if, typically if this setup fee is more than 1500,
just worth it.
Yeah.
I'll just worth it.
Yes.
Uh, the other one said, should we stop a campaign when instantly
stops it because of hype?
Yes.
I would stop the campaign immediately.
I would stop it.
And then, uh, yeah, the best thing is like, if you have a high bounce rate,
that means you didn't validate the emails.
So make sure you're using mails.so or similar or such.
Um, question, when you lower your price for a first time clients and you want to
get them on a retainer at a higher price, what's your best way to share value in
that? Yeah.
So do you think is just offering more appointments?
For example, if my first client's 1850 and I want to get them on a retainer
and around 2250 or 2500.
Yeah.
And then, so it depends on the way you frame it.
So I'm going to tell you exactly what I say.
Uh, so once you deliver the, the book meetings that you've, the, you promised
them, you would say something along the lines, you would say, Hey,
clients, um, I see that we can essentially just send in more volumes, right?
And then, uh, like we can, we can deliver more booked meetings, right?
I rates for a retainer of this price.
Right?
So now you're, you're, what you're saying is you're framing it as value-based,
you know, so you're, you're saying, Hey, we can deliver more value.
Here's the price for more value.
Make sure always you say, you go in from the, uh, perspective, deliver more value.
Never say, here's 10 booked meetings.
We can, uh, like here's 10 booked meetings.
Here's a price for a retainer.
Always start as unlimited an appointment or minimum sale.
So it depends on how, like, uh, that's not going to fail.
So for example, let's just, for example, tell me your offer and then I'll
tell you like exactly what to do.
So how many booked appointments you actually promised them?
Five appointments.
Yeah.
Okay.
So five appointments is great.
You, you, you're going to be able to deliver them easily.
So five appointments, the way I would pitch this.
So after I delivered the five booked meetings, I would say there's.
So we shouldn't be scared in this.
Um, we can, I can deliver 10 booked meetings and I would go for more than
200, 2500, I would go for like 2750.
Right.
I would say there's absolutely no reason why we shouldn't be sending more.
I can essentially deliver more value.
I can get you double the booked meeting for a retainer of three months.
So the, uh, the LTV will be three months for you.
Right.
What you would do and what I would do in your shoes is I would book meetings
first and then the average LTV rights of your clients would be three months.
Right.
So you'd say, Hey, pay me each month, 2750 for 10 booked meetings every month,
but you're going to commit for three months.
Um, yeah, so let me know if that makes sense.
Jason, yes, yeah.
Pause the campaigns and say something along the line.
I'll send you the template that I use.
I say there's absolutely no reason why we shouldn't be scaling more and
deliver more booked meetings.
It makes sense.
Um, I can deliver 10 booked meetings every month, but you're going to
meetings.
It makes sense.
Um, I can deliver 30, let's say like 30 booked meetings in 30 months and 30
months and three months, and it's going to be 2750 each month.
So it's even less for you because you paid 1850 for the first month.
Now we're just paying 2750 for 10 booked meetings.
Right.
And then you'd have to commit for three months.
And now we have a client locked in for three months.
And now it's, uh, which it's like for you.
Um, let's read through this.
I just got this reply from my campaign right now.
How should I respond to this?
What do you think?
Reply here.
Actually, if you work on a commission basis, I can, I can pay on a $20,000
fee up to 20% or 4,000 non-interest in paying for leads as a result.
We get, we get a 1% hired for every 2.75 candidates, a client
interviews through this due to our process being so hands on.
Get me two solid clients a month.
You could make $8,000 first.
Get me clients a month.
You could make $12.
Get me two solid clients a month.
You could make $8,000 and then get me three solid clients.
I can't, I believe that was just a typo 12 K.
Yeah, this guy is absolutely okay.
Like I told you guys, like $20,000 a fee.
So what I would say is I would, um, yeah, I would front the, yeah.
What I would say is I would say something like, um, thanks for
getting back to me first name.
Um, so they said, if you work on a commission basis, I can pay you $20,000.
Okay.
So yeah, I would, I would definitely, uh, I would say I can guarantee you
two or three clients per month.
Um, two to three clients per month.
Um,
I'd love to set up a time to talk.
Does X or Y works for you?
I'd love to set up a time to talk.
That's X or Y works.
Um,
and in this week, just to not make sure pushy.
So thanks for getting back to me first name.
I can guarantee you two, three clients per month.
I'd love to set up a time to talk.
Does that does X or Y sounds good to you?
Let me know.
And then make sure you say thanks for the time.
Yeah.
So just get them on a call, get them on a call.
Yeah.
So it's, it's obviously going to be, to have an upfront fee.
Yes.
Yeah.
Yeah.
So I would reply to them and get them on a call just because I would not, uh,
waste such a positive reply.
20,000, like 8,000, uh, you know what you should be doing.
Axel is you're going to show them the ROI calculation live in your call.
Yeah.
This is what I would do.
The, the one dash Vishal showed us yesterday.
If you could show them that two clients, if I can get you three clients,
and that's even more, right.
And if you, even if you, um, yeah, man, this is an easy close, right?
So if you show them the actual ROI calculation, that's an easy close for you.
Just because if you ask them for upfront fees, such as 2,500, like, think about it.
Like one client here is $4,000.
You would say, Hey, you would pay 2,500 for this set up fee.
And then it's fully refundable, right?
It's fully refundable if we don't deliver the two clients.
And then it's time for you to just go ahead and knock this out of the park.
And in terms of fulfillment, just get that clients.
And then we're going to help you with the fulfillment.
Don't worry about it.
Just get the clients like worry about fulfillment later.
Yes, exactly.
Yeah.
I just know you're going to close this now.
You already closed it.
All right, guys.
So we have, uh, we have a little over 15 to 20 minutes left.
So make sure you post in your questions.
And then, uh, if there are existing bills, such as the ones, uh, about shitty, I'm
going to just send me the blueprints in the EMS and I'm going to help you out.
Uh, one-on-one my man, which is why the reason why I don't want to grow this
community past 200 guys, it's just so hard to give everyone the time and energy
just because I want to make it, it's my own, make it more personal instead of
like letting so many people come in.
Uh, how do you track the sales from a client like that?
Yeah.
So the way he's going to be, it's going to make, uh, they're going to have to
make him, um, a, and, and their Stripe dashboard, right?
They're, they're going to have to add him in their Stripe dashboard.
And whenever basically there's a closed or like there's money coming in,
their client's account, it's going to come in automatically.
It's going to be like automatically built.
Yeah.
So let's see if there's any other questions here.
Uh, it's so funny.
Like Axel always gets responses in the call, always in the weekly call here.
Uh, do you have an automation to have filters for your client's name?
Um, honestly, I don't have this.
Uh, I just, uh, like automatically, automatically just basically, uh, it
will be like automated build in your Stripe account, right?
You don't have to worry about it.
It's going to, the money is going to come in into Stripe account automatically.
Cause you don't need to put any automations here.
Uh, what I meant was that for the clients and when the Stripe notification comes in
and be closed, call for that client.
Yeah.
Ideally you'd have like a subscription, you'd have like a payment, like a
payment integration, right?
And it's going to be like another, like, uh, like it's going to be another
subscription, so it's not going to have like a native automation for your
initial Stripe notification.
Is that makes sense, Jason?
Unless you want to set up an automation that sends them an email, for example,
saying them, thank you.
I've received the money or whatever.
And I think that's pushing it.
I don't think you need to do that.
I think it's just more friction for you, right?
Uh, what I meant was tracking closes for the, uh, Axel's client notification.
When the payment comes through.
Yeah.
Um, so ideally you would have, for example, in your CRM, uh, what I meant
was tracking close for the Axel's clients.
And then you get notified when the payment comes through rather than all his clients.
Hey, so what I was just, uh, just to clarify, I can't type quick enough is
that, uh, instead of having, um, access to his Stripe dashboard and then you
being notified of all his payments from all his clients closing, maybe I was
just thinking out loud here.
Maybe you have like a filter.
So if it matches your client name, you could get a notification that that was
closed or something like that.
And that's one way of tracking your closes.
I don't know.
That was just something I was thinking out loud.
Yeah.
Yeah.
That's definitely doable.
Um, what do you do is you would wait for the client, like a notification
to come from Stripe, and then you would have a filter that lists all tasks of
your client's names on your ClickUp and then adds that to that, to the, to
the right clients, right?
Yeah.
That was, that was just something that I was thinking so that you can, I mean,
you might miss a notification or something, but I mean, $4,000 is a lot of
money.
You don't really want to be missing.
You got to, you're going to have to follow up at the end of the month and
give them a, you know, you click 10 seconds, give me 40K.
That would be great.
Yeah.
Yeah.
Yeah.
What you would do is you would watch events coming from the Stripe
notification, and then you would have a filter and the filter would be actually,
no, uh, it would come in and then the next flow would be list all tasks from
ClickUp, whatever CRM menu.
And then it's going to be, um, if the client's name equals the payments that
comes in from that notification, and then it's going to compare it to, and
then it's just going to add it to a, for example, like a field in your CRM with
the exact code of whatever the client paid you, right?
It would be, for example, uh, let's say, for example, your ClickUp.
Let me go to my ClickUp here.
You would have a field here.
Well, it depends on your processes.
How you depend on your process.
You would have, for example, uh, a couple of fields here.
So let's go to a build client example.
So we'd have.
So this is, for example, like a meeting, right?
An example of a lead that you booked for your clients.
You would have another stage here.
For example, um, meeting, or let's say, uh, sale closed, right?
And you would have like a, for example, like a filter and you would have a
coats, for example, like money is going to be a commission, right?
And you would have, for example, like the stage here and then.
And you would have a commission here, which would be in, for example, $4,000.
You would have a filter that checks in if the task name, for example.
Equals the notification from the stripe.
And it's going to automatically update this sales close.
And you're going to have commission here.
So I can definitely see this, uh, like in real time working.
Yeah.
Okay, guys, we have 10 minutes left.
Can you, can you post?
Um, the blueprints from your fire public YouTube videos in the community too.
Yes.
Um, yes.
Um, so there are a couple ones, but I'm going to include, I like update
everything for you guys.
And obviously the seven day campaign building is going to start today.
The first campaign is probably going to be something like maybe telemedicine
or I don't know, like, I'm just going to have to check the doc and I'm going to
start building these from scratch.
So I want to make sure we include all of the damn industries.
So you guys have the.
So basically any campaign can just go ahead and build that Edward.
Welcome then, uh, late to the party.
Just wondering if there's a per appointment book type of offer.
How would you track that as in charge?
150 dollars per appointment book.
Okay.
Yeah.
So, um, the way you would do that again is, um, so just thinking about it, it
would be an automation that's essentially you would, uh, yeah.
So, uh, it will be the same flow.
We're just going to add a couple of modules, right?
So if you go to the classroom and you go to client fulfillment, service delivery,
uh, let me see if there was delivery or fulfilling your clients, uh, I believe
it's, uh, not sure which one.
Uh, I believe it's, uh, not sure which one.
So anyways, I'm going to update this later on.
What you have to do is you would have basically like a web hook that catches
all the booked meetings that comes in from the client's calendar.
Right.
Once you do like a web hook, custom web hook, and then it's going to watch
from all the, like, for example, you'd have, um, just web hook save.
And then that web hook is going to watch all the booked meetings.
And what you do basically is you populate essentially, uh, the ClickUp CRM
in your ClickUp basically is going to be meeting booked of that prospect that
just booked in your client's calendar.
Right.
And then it would typically be, um, a paper lead model.
It would be like, uh, they would add you in again.
So whenever there's a, uh, there's a paper meet, uh, like there's a meeting
booked, right.
You would send them a notification through email.
You would say, Hey, a new meeting has been booked.
And then they have to basically like pay you for appointment.
Right.
Whenever there's there, they're going to see the, the meeting book there.
Right.
In their calendar, they're going to have to pay you.
And it's going to be a dashboard through Stripe, whatever payment
profits to start using.
Right.
Um, yeah.
Saying you use a client's calendar link to send over the interest to lead over
your email.
How do you track that?
Yeah.
Like I, like, make sure you watch the last video that I posted yesterday on
how to track that.
Yeah.
You would have like a web hook, right?
The web hook that watches for each booked meeting and you would add that to
your CRM to track all of that.
Uh, do you have any advice?
Do you have any advice on getting leads for a specific niche?
Do you want to operate in with a poll?
You can filter, for example, software companies, but how can you filter again
for all the healthcare, um, what's the best way to do that?
Um, legal finance for companies.
Yes.
What you do is you basically use keywords, right?
So for example, if you have the keywords, um, like, for example, you want to look
for healthcare, you would look for, you would choose the software development
niche or, and, or like the information, information technology in the niche.
And then you just include healthcare or do you include legal or finance?
It's going to help you like narrow down the niche.
Yeah.
I think I've done in this last full-fledged video that I just recorded for you guys
today.
So the campaign was B2B SaaS and they were just targeting basically, uh,
startups, right?
Only startup that were recently funded.
Yeah, man.
Thanks, Jason.
Axel's, uh, when will be the recording engineer?
What a cool name will be the recording for this available.
It will be available.
So like today, guys, by the end of the day, along with the Wimson core here,
right?
And then it's going to be uploaded to the classroom, obviously, and I'll make
sure to update the, uh, blueprints.
And if you guys have any basically prompt ideas or anything, just posting
the community, I'll help you guys out.
All right.
So how many times we have five minutes left?
Make sure, let me make sure like, I'll skip through this.
Thank you, Saad.
Always worth my time.
Always worth my time to, uh, talk to you guys.
Hey, I just want to say love the fulfillment video.
Extremely useful.
Thanks, Edward.
Oh, yeah.
But yeah.
Hey, man.
How you doing?
Yes.
All right, guys.
So, uh, I think that's pretty much it.
Oh, yeah.
Axel, how do I track a client's booked meeting if they don't have a webhook
available on?
Yes.
If you watch the video, I talked like, uh, you're going to have to get access
to that, right?
Either you get access to their calendar or cal.com, or you create a new
uh, like a calendar, basically a new calendar link, right?
Just because you need to track that.
Tell them, hey, uh, we need to track the, basically the booked meetings
just to make sure we have, we deliver all the results for you, right?
And if you don't, you're not going to be perceived as professional, right?
Trust me, because there are many times I forgot this and it just sometimes like
I piss away, like deals just like that.
Yeah.
Any sort of responses?
Yeah, no worries, man.
Main reason why we're keeping this community of 200.
Yeah.
That's it.
That's all we want.
It's going to be like a little, a little group, you know, more than 200 people.
I'm waiting for respond the other thing.
Yes.
Yeah, no worries, man.
I'll, uh, respond to you shortly after the call.
And I'll be out with, uh, what other, the plan that you're trying to do.
All right, guys.
So we are three minutes.
That's, uh, hey, harsh, a little late to the party.
Fashionably late.
Good to have you.
All right.
Last question from James.
I have a prospect who's working in an interesting niche.
His LTV for clients, 130,000.
Wow.
Dollars, uh, but he only has one client so far the market.
College football teams and he sells them data analysis programs.
He has no experience with sales and is looking for help in that area.
The market itself is only about 300 potential directors.
So a cold email and paying, not be the best.
How would you structure the pricing for a deal like that?
Wow.
Might, might have to add cold cold soup.
Yeah, man would get at least 10% of this.
Like 130, uh, $30,000, at least 10%.
Yeah, I would go for a, for a, for a partnership such as 10%.
The least you can get is I would go even more than that.
I would go for 15 to 20%.
It's like $130,000.
Think about it.
You just made, if you get him one client, you just made the guy $130,000.
And I wouldn't worry about this.
Like I would say, I would like those leads just because you have more capital.
Right.
Like you could, yeah.
I would go for, uh, no, ref share, obviously, just because appointment doesn't, like, it's not 100% sure, uh, if he's going to close it.
So I would go for a 10, uh, I would go for basically 10,000 ref share and per appointment, um, I would go for, um, essentially, um, per appointment, if you're going for, uh, per appointment, I would go for like at least a thousand.
At least I may be able to help you with the NLV and the Oklahoma Sooners.
Okay, guys.
So yes, he may be coming in strong.
Yeah.
Jeff has a lot of data.
So make sure you guys, uh, if he has, uh, like something I want to talk about, guys, if there are partnerships in the community, make sure you guys partner with each other.
Right.
It's really going to help everyone.
So let's make sure it's not just like one work on a project.
If there is like, uh, opportunities for partnerships, that would really help.
Right.
And everyone is going to be able to make money and basically crush it.
All right.
So, um, thanks so much for coming in today.
Um, so today we're going to basically update all the classroom, uh, with the new, uh, blueprints.
And then I'm going to also make a post about the, uh, the last sales call with, uh, with Vishal.
And then obviously the recording of today's sales call, uh, the sales, uh, mastery call is going to be in the classroom.
So thanks so much for coming, guys.
Uh, thanks, Axel.
Thanks, James.
Thanks, Jeff.
Um, let me through, let me through, let me through all you guys' names.
Thanks, Axel.
Well, thanks, sorry.
Thanks, Edward.
All of you guys are awesome.
Love you guys.
And I'll see you guys in the next, uh, call.
Cheers.
