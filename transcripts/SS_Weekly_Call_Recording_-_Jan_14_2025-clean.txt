Hello everyone, hello, good to see you guys here finally.
Welcome to our weekly meeting, we have a bunch of new faces here, hey <PERSON>, hey <PERSON>, hey the AI agent, hey <PERSON>, what are we doing now, hey <PERSON>, hey <PERSON><PERSON><PERSON>, hope you guys had a great week, you guys had a great weekend.
We have a value-packed session just because you guys have been asking one of the best questions I ever heard, and yeah, there are a lot of actionable steps in today's meeting, so yeah, the first 30 to 45 minutes are going to be answering all the questions in the thread, and the last 15 maybe 20 minutes are going to be answering all the questions in the chat.
Let's wait for everyone to come in so we can just get started because I'm cognitive of you guys' time.
So we'll just wait another one to two minutes and then I'm going to share my screen, and then we're going to start answering all of you guys' questions.
We have a bunch of new faces here, happy to have you guys here, <PERSON>, <PERSON>.
Let me know if you guys are hearing me okay, so if there's anything I can troubleshoot, just let me know in the chat right now.
Thank you, <PERSON>. Awesome, so first call, hey <PERSON>, happy to have you here, my friend.
Okay guys, so I'm going to get started with the first question that we have in the thread, which is from our lovely friend, <PERSON>, so let me just go ahead and share my screen right now.
There you go, entire screen, and then let's go ahead and get started.
Okay guys, so first question that we have from our lovely friend, <PERSON>, so hey <PERSON><PERSON>, I love your course Insane <PERSON>, thanks so much man, I appreciate it.
I have a question, how do you keep scraping new leads over time for your clients? The amount of leads available isn't infinite, well that's true, especially in smaller B2B niches.
How can you guarantee that clients will consistently receive steady flow of leads? This seems like a potential objection prospects might have, what's the best way to address it? Thanks in advance for your advice.
Well, that's a really good question, my friend, and I've been taking some notes earlier, like minutes earlier, so just go ahead and answer it.
So I want to essentially help you break this limit in belief, just because I get this question a lot, and the real answer for this is that I don't think you should worry about this at all.
There are always more leads than you think, just because companies, like I said, are hiring, companies are being founded every day, companies are constantly hiring people, businesses are expanding to new markets every single day, companies getting funded, there's a bunch of events that companies are going through every single day.
And organizations are changing their stack or tech stack or switching vendors every single day, so I wouldn't worry too much about it.
The only time you would actually run out of leads, only if you are targeting like very, very niche companies. I mean, think about it, I was targeting telemedicine, and telemedicine constantly are looking for MSPs,
which are basically companies that build software specifically for telemedicine. Now, let me explain to you guys what the telemedicine is, or what the telemedicine niche is.
Basically, these are companies that provide basically TRT, which is testosterone therapy. Now, this is a very niche that I was going for back then, and there are only a few telemedicine companies, and I was still able to get like thousands and thousands of leads for them, right?
So, I wouldn't worry too much about it, and like once you have them as a retainer, like what I would recommend is you can discuss expanding beyond a single niche.
So, what I used to do, or like let's say I onboard a client, and let's say I start working on their campaign, and let's say I promised them five to ten book meetings, which I would recommend you guys at the start.
So, let's say you promised them like five to ten book meetings, right? So, what you would do is once you deliver them those book meetings, what you have to do now is upsell hard, right?
So, you would tell them, hey, since we basically are targeting this niche or this market, I would recommend we essentially just expand to other markets, and let's see if we can get you better clients, or you can see that maybe your service is going to help this market solve the problem of this market.
And typically, they are more open to do so, just because at the end of the day, they also want to make money, and they understand that if you make them money, right, you're going to make money too.
So, like even if a niche ever becomes saturated, which is very rare, like there's always room to basically tap into other markets.
Another thing is the client's lifetime value, so LTV, basically how many months you're going to be working with a client. So, on average, a client will work with you for like three to four months.
Like for me, like the longest I've worked with a client was like six to seven months, right, and it was a recruitment client. So, within that time frame, like they typically make like a ton of money.
They make like 50 to 80K new revenue from the like the pre-qualified decision-makers meetings that you provide them, right.
So, this means like even if the niche seems small, the ROI that they get is massive and extremely massive, guys, and it really happens fast.
And let's say you get them, for example, like 10 booked meetings and let's say 50 days, right, just to be rational, let's say you get them like 10 booked meetings or like 12 booked meetings in like 50 days.
Let's say they close, be like three, three to four, depending on how great their sales team is, that's like 40K or like 50K, depending on, obviously it's going to be dependent on the size and how much they are charging for their service.
But that's huge, guys. Like imagine, like what is a lead generation method that you know that you can close three or four deals just by an internal cost of $400 to $500.
Or in their example, well for you it's $400 or $500, but for them they're paying like let's say they're paying like $4,000, $3,000 for a system, which is what I would recommend you guys pitch on your sales goal.
Like that's insanely low, right? So I hope this answers your question.
So okay, the next question that we have is from our lovely friend Jeff.
Hey Saad, this is the best online group. Love your content.
Thanks, man. Of course it is the best group, just because we focus on the main thing, which is client acquisition.
Basic question, when I'm trying to scrape leads, say from Apollo on ApiPy, it limited my account and it paused the campaign with a warning from Apollo scraping too many leads at once.
As this happens, if I just scrape a smaller amount at a time, say 500 at a time, how do I set up a schedule to scrape say 20,000 leads from a list?
Same applies for LinkedIn and others.
So I'm going to tell you exactly what I do currently.
So what I do basically, first of all, I would recommend you scrape in batches.
So Apollo and LinkedIn have a daily scraping limit, guys.
So a good rule of thumb is to scrape 25 in release per day per account.
So let's say you sign up to your accounts using ApiPy, like I say, like a professional email and another account using Apollo.
And once you sign up, they only allow you to scrape 2500 per account per day.
Now, you can use multiple accounts.
You can have multiple accounts, such as four or five accounts, and you can easily scrape like 10,000 to 12,500 leads.
There are Apollo accounts.
Let's say you have an account with the I believe the pro plan.
You can scrape up to 4000 lead, guys, and I've done it before, right, per day.
So depending on the plan you go for, if you don't want to pay for Apollo, really, you can just use the free trial on pro plan.
And you can just go ahead and have multiple accounts, right.
Multiple accounts, you can scrape more than that.
What I do now is I hire VAs.
So since I cannot, like I'm really bottlenecked with multiple things, like multiple clients, multiple retainers,
creating content, like being in the community, answering everyone's comments and basically replying to DMs,
just because it's very hard for me to grow the community past 200 just because it's really, really, really hard to give everyone the time and energy.
And I don't want to grow past 200 just because it's not worth the money at this point.
So like I would hire VAs and they can manage these accounts and scrape leads for you.
So this is what I do. And I've done this before all the way when I used to scale past 25K.
So what I would recommend you guys do.
So just if you have the money for that, you would hire VAs and then would essentially make multiple accounts for you and they would scrape multiple accounts.
They clean the data and then you would have another VA that just drag and drop the lead list,
enriches everything and just basically have a campaign running in the background every single day.
But I wouldn't recommend you guys do this if you're just getting started because you want to do everything on your own
because you don't have so many clients to work with.
So I would only do that if you're already making a big chunk of money as a salesman agency owner
and you want to scale past that 10-15K, right?
Because you want to allocate that time into sales calls and actually closing the deals.
So another thing is you would break it into daily chunks of 2,500 leads per account.
Because just think about it. If you get like 2,500 today, 2,500 tomorrow, that's already 5K.
In like four days, you're like at 10,000 leads, which is pretty good.
If you have multiple accounts, you can basically now achieve 20,000 leads, which is huge, guys.
Like if you use the Scraper and Apophile, this one right here, like $1.25 per 1,000 leads.
Like that is really, really awesome. Like this one.
I think it's...
I believe it's this one. Yeah, this is the one.
So $1.20 per 1,000 leads. That is really like extremely cheap.
I don't think there is any platform that allows this.
And the cool part about Apophile, guys, is that most of these creators are competing for our usage.
So every day, there's a new coder that builds a Scraper and lowers the price.
Because there's a marketplace for everyone, which is pretty good for us, right?
Okay, now the third question. Hey, Justin. Hey, Sad.
Hey, Sad. Absolutely live in the community and continue putting out for us.
Thanks, and I'm really happy that you are basically finding knowledge and people that you can network with here.
And the answer for your question.
So my question is, when you're scraping leads from Apollo using the Apophile's API,
how do you ensure you don't scrape the same leads and have duplicates in these spreadsheets
so you're not wasting the limits of 2,500 leads per day?
That's a really good question, man. And I will show you my Apollo account right here.
So let's say you are scraping leads here.
Okay, so for example, I have like a search here, for example.
So make sure, by the way, you always toggle verified and you have everything set up here.
So as you guys can see now, when I'm clicking on this net new, not total net new,
and you look at this URL, app.apollo.io, this URL, right?
This organization industry tag ID.
So the cool part about Apollo is that essentially is that it has all the parameters.
When you click on, let's say, founder, the URL is going to change.
Now, if I click on again, I would add founder.
It's going to be added in the URL, right?
So founder, as you guys can see, there's founder here.
This is how the API works, which is pretty cool.
Now, what you can do is you go to sheets.new and then instead of trying to manually
and erotically add in this, right, as parameters, here's what you do.
Go to your Google Sheets and then you would put in, for example, search URL.
And then you would put a number of leads, scrape.
And then what you would do, go back, copy this URL.
And then you would put your right here, right?
And then you would just put in the number of leads scraped.
Let's say you scrape 2,700.
And now what you would do, basically, is you go in and you put in a new search URL.
And then once you do that, you add it right here.
Now, if you go to this URL, you'll find the exact parameters that you put in the initial search, right?
So now you know that this search gives me this amount of leads, right?
So this is how you can bypass getting duplicates.
And obviously, when you add the leads in instantly, the filters are going to remove the duplicates.
They're going to tell you, hey, there are duplicates in this campaign or you already contacted this person,
which is pretty cool and instantly.
So I hope this answers your questions, man.
Yeah.
Okay, so let's go ahead and answer the next question.
So our friend right here, I currently have a full-time job,
and I'm looking for ways to make an agency work despite having spot availability during the weekdays.
I do have Fridays completely open, so I'm trying to figure out how to make the most of my current situation.
My ultimate goal is to transition to running my own business full-time.
Are there specific strategies or models you recommend that are more manageable for someone
who with limited weekday availability?
I'd really appreciate your insight. Thanks so much.
Yeah, man, I've been there, done that.
I used to work as a bouncer before when I had this fitness era that I had.
So I used to work as a bouncer at nights, and I used to try to learn how to code during the day,
which was pretty brutal, and this is way before I used to try to learn automation.
So yeah, I understand the field.
So what I would do in your case is basically go in and take in the 80-20 of the 80-20s.
I wouldn't worry too much about the business registration, building a crazy,
great website, or have this beautiful brand name.
The only thing I would care about is sending emails and picking one system from the classroom,
watching a few videos, understanding how it works,
basically interfering with the market as quickly as possible and start building an offer
and start sending emails.
You do not have to go crazy about it.
I would recommend you do things that don't scale,
and what that means is basically Paul Graham is a writer and someone that I really follow
and someone that I look up to said something such as doing things that don't scale.
What this means basically is most companies like Amazon,
most companies are big companies like Google.
At first, they didn't really care about optimizing everything.
They cared about getting up and running, and then basically just milking the juice
out of the things that actually move the needle the fastest,
and then we can worry about optimizing.
What I would do is the first thing I would do is try to get your first customer.
Do not worry about other things such as logistics.
Get that first customer. Do not worry about delivering the results even.
Worry about getting that first customer first because trust me, guys,
when you get that first customer, you will fulfill it just because you don't want to pay them back.
You will focus on getting the customer first, then worry about fulfillment later.
Get your first customer and then use that money to essentially acquire more customers
by having more volume in your campaign.
Basically, you would send within KPI 5,000 emails or you would reach out to 5,000 leads,
then you would get two to three clients, for example, or even you get one client,
which is amazing because let's say you're going to invest $200,
the cheapest way to acquire clients is $200,
and then you would actually get a client that's paying you, for example,
once you get started, $1,500, for example.
I'm just giving you an example.
Depending on how much you believe you can charge.
Let's say you are in a sales goal and you believe the system is going to get them a $20,000 or $30,000 deal,
and you really understand that and you understand your system, you will be able to close it.
Let's say you do that and that's like 10 times the initial cost of $200,
so you'd invest that back in and you would send more emails or you're getting more clients and it just snowballs.
This is what I like to call the Lollapalooza Effect, which is a mental model.
A mental model that I would encourage you guys to always implement is that when everything is working in the right direction for you,
so you close the one, you're using that money to fulfill the clients,
so you're sending them to book meetings, now you are developing a great relationship with that client,
now you're pitching a retainer, but at the same time you're doing more outreach,
now that outreach is going to get you more clients, and more clients is going to get you more retainers,
more retainers are going to get you more referrals, and it just goes in as this Lollapalooza Effect,
which is everything is working in your direction, you're making more money, and you're re-investing it.
So this is how we actually transition from having a full-time job to actually basically running your own business full-time.
So I hope this helps, man.
Okay, next question from our lovely friend Jesse, my best friend.
Hey Saad, I missed you about this one. I thought it may be better to discuss on the calls, others can learn.
So I think I answered this question in DM.
So when sending your campaign, instantly you mentioned that in the video to check, prioritize reaching out to new leads,
filter, and you also mentioned to set up max new leads, yes.
I did this with my campaign, did not send any follow-up as all the emails were being used on the new leads,
the same amount for each, oh yeah.
What is the solution for this to ensure all relevant follow-ups are sent while staying within the daily limits you sent?
So yeah, I would, if you really, really like want to prioritize that, in your case, specific case, I would untoggle that, honestly.
Okay, you can just untoggle the prioritized new leads, and then you have a follow-up of one or two days.
So it doesn't have to be one or two, it could be from one to three days, right?
One to three days is like the sweet spot.
And then what you would do, so what I would recommend you guys is having data high instances to send in,
just because you can get up and running easily, and you can send out, you can finish off 5,000 leads in like two weeks.
So you would start with, let's say example, you go in, I don't know, like 20 email, and you're sending, let's say for example,
you're sending 20 emails per email inbox per day.
So it's like, we have 15 emails, so it's like, let's say 200 emails per day.
The first day it's going to be 200 emails, the next day is going to be 500 or 600 emails, and then you pause for like 24 or 48 hours.
Now you'll have time to send the follow-ups, which is going to be the fourth day, right?
So you're going to wait for one to three days, depending on how many days you're waiting, let's say one or two days,
it's going to be highly dependent on your case, right?
So yeah, this is what we do.
So again, if they're straight up say, take me off your list or something along these lines, yeah, I definitely got this.
Do you still try to get a lengthened connection for possible future outreach?
I would lean to no, but I know you stated that it is key to squeeze the juice out of everything.
So I used to do this back then.
I used to say something like, they say, okay, I'm not interested.
I would be like, hey, that's totally cool.
Do you mind if I send you a LinkedIn request?
I would recommend you guys do this.
Typically, they will say yes, and then you can essentially do this thing right here.
You can say something just on top of my head.
You could say something like, that's totally cool.
Do you mind if I can send you a LinkedIn request?
Maybe in six months, it makes sense.
I can just follow up if you are interested.
And then what I would do is I would mark them in my CRM.
And I would basically compile all that list so I can reach them out in like three months, right?
And that would really squeeze the juice out of everything, right?
Like, typically, when I do this, I would get like maybe two deals out of like maybe 200, which is pretty good, right?
0.2%. I mean, still two deals, right?
Yeah, so with Instantly's health score metric for the email,
it can be assumed if we have a health score of 100% that our emails will land directly in the inbox.
What's your experience with this and how accurate would you say it is?
Any other Instantly metrics should we be considering?
No, I don't have full 100% data just because, unfortunately, I don't work with Instantly.
I wish I could. I wish I could give you like 100% accurate response.
But in my humble opinion and in my experience, when it says 100%, you are 100% sure that you are landing in the inbox.
They recently added this inbox placement deliverability thing, and it's really good.
I would encourage you to use it.
But in terms of 100%, I don't think there is any difference between 100% or 90% or like 99%
just because they have basically an internal software that checks every day for the warm-up emails that you send in
and checks if you receive an email from that warm-up that you send in back to the service in their spam folder.
So this is how, essentially, they manage this.
So I would say it's accurate, right? But I wouldn't worry too much about it.
I would just keep it below 95, right? Just keep it below 95, and you'll be good.
Okay, so Jason, hey, man, can you show the lead track in Google Sheets you have?
So I'm not really sure what you mean by this.
I'm building one, but I'd like to see how it went.
Just send me, like, just put it in the chat what you mean by the response track.
Because I use, like, at least what I used to use, I used to use, I'll just say, click-up lists, right?
I'll just add them to a click-up, or you can add them to your preferred CRM.
It could be Airtable, I don't know, like, Monday, whatever you want.
I just like click-up.
Okay, guys, so I have another question from our lovely friend Axel,
because he just messaged me before the call.
And he basically has a recruitment campaign for a client.
And, yeah, first of all, I'm going to answer his question,
because I think a lot of people are going to benefit from this answer, right?
Because, basically, a lot of people that I know are targeting recruitment,
and, basically, recruitment, guys, is a very, very great niche,
and it's very broad, so everyone can actually target this niche.
So I believe his question was, he has a recruitment,
and what I told him, basically, is to ask them for the candidates that they have.
So, typically, guys, when you are working with a recruitment niche,
they have candidates, right?
And what is the actual deliverable that you're going to get them, right?
Because I want to explain this, because so many people are asking this in the community.
Now, let me explain, okay?
The recruitment industry, what is their offer, what they are trying to do?
Basically, what is their ideal client?
Their ideal client is a company hiring for the exact job roles that they have, okay?
It's pretty simple, guys.
Don't confuse yourself.
Like, don't confuse yourself.
Now, when you work with them, they will give you candidates that they have,
and your job is to help them place those candidates, right?
This is their offer, which is a very clarified offer, right?
Super simple, guys.
So once you work with them, let's say in the kickoff call,
you'll have an onboarding call, and then they give you basically all the candidates that they have.
Let's say they have software engineers, and they want to place them, right?
Now, typically, recruitment companies, once they place a candidate,
they get like $30,000 minimum for a deal, for a placement.
Specifically, let's say you work with a recruitment agency that they have IT candidates.
Like, a typical software engineer is going to get paid like $100,000 per year.
So they get like $20,000 to $30,000 per placement for a company.
Now, the easiest way to basically crush it,
it's basically infinite glitch money in the recruitment, right?
And I'm saying this because I know it works.
You're going to have to use this following method.
So if you guys watched the last video, I posted this in the community.
There's this Indeed JavaScript that is absolutely crazy.
It's called Fast Indeed JavaScript.
And it's $15 a month, and I've used it.
And I'm really surprised that not many people actually use it.
So there's 33 monthly users in this scraper.
And this guy is absolutely genius.
You can scrape thousands of leads.
I just put in early on.
I just scraped 100 results, right?
And the way it works is basically, for example, Axel.
Let's say they give you something like, I don't know,
like software engineers that they have.
You can go to Indeed.
So I believe you told me it was the UK.
So you would go in.
You put in software engineer or software developer in the UK,
and you click on find jobs, right?
And all of these are companies actively looking for software developers
or software engineers.
Now, think about it.
The recruitment company, recruitment agency, already has those candidates.
And these companies are looking for these candidates.
So what does this mean?
You can connect the two, right?
So this is the beauty about recruitment and why in this community,
I always push for it, right?
It's not the only industry you can target, but one of the best, right?
And no one actually talks about it.
No lead gen agency community talks about it,
which is why it's better to stay within our community
because it's community exclusive, OK?
Now, OK, what you can do is we've copied this URL
and go back to Fast Indeed and just paste that in here.
And once you do that, you can just click on proxy configuration mixer.
It's always residential.
Run options is going to be no timeout, and click on rent actor, basically.
If you have the actor, he's going to have to pay for it,
and then you will have basically the run, right?
Right here.
So you'll have the results.
Now, what you will do is you go back to the high-tech lean growth system,
and this is one of the best systems that I've built.
It's going to help you a lot.
So what you do, for example, I'm going to go to my run,
go to storage, copy this data set ID,
and then I'm just going to paste that in here
and click on run this module only.
So go in right here, and then remove this,
and just hard code it, click OK, unlink this bad boy,
and paste that in here.
And then click on, let's see, for the limits,
I'm just going to put five and click OK.
And I believe, OK, so awesome.
So I just want to show you guys the type of data that you're going to get
that you can really leverage this to the max.
So for example, I'm just scraping like a manufacturing, maintenance,
or like this is a product manager title, for example.
What you can do is you go to recruits,
I believe it's employer, yeah, employer,
and you go to this dossier, right?
It's in French, dossier.
And then you go to employee details.
You can find the CEO name.
You can find the brief description.
Cargo provides food, agriculture, financial and industrial products,
and financial services.
And they are worth $10 billion.
And that's getting a deal for this company, for this recruitment company.
You also have a title, which is manufacturing, maintenance,
or reliability supervisor.
You have the description and text, right?
So they have everything right here.
And you also have images.
You even have the, sometimes you could find the CEO photo URL.
So you could personalize the hell out there.
You could feed this URL.
It could have like a filter.
If a CEO's name URL exists, you can feed it into DAL 3, right?
DAL 3 basically is just an image generator.
And you would say something like, based on this image,
generate like a one-liner, right?
Let's say the CEO name has like a button-down shirt.
You would say something, I don't know,
like a personalized outreach line for that, which is crazy, right?
This is how you could leverage AI.
And then what you would do now, so this is what it does.
It basically takes the CEO name and you can feed it to the e-mail finder.
Now, what you can do, for example, I'm just going to go to the job.
I'm going to go to employer, and I'm going to take name.
So the name is the company name.
And I would go basically to e-mail finder.
And I put the company name.
And I would go back to my system.
For example, I'm going to go to employee detail.
And I'm going to go and copy the CEO name.
And I would basically go back and paste that in here, click search.
And then there you go, we found the e-mail.
Now, I just found the founder's e-mail.
Now that I found the founder's e-mail, now everything is like 95% all the way there.
The system actually cleans up the company name.
So even if we have Cargill LTD, it's going to clean up to be Cargill.
And then the job title.
For example, let's say we have a job with this manufacturing,
manufacturing reliability supervisor.
I can go back and I can just run this module only.
I'll put this right here, click OK.
Now, it's an abbreviation of the job.
I can use this.
I will get the first name of the person, the CEO name.
And I can generate the dream e-mail.
Now, what I can do is hope you're doing well.
I heard company name is looking for this job title.
And then what you can do is reach them out on behalf of the recruitment agency.
And then you do this full time.
This is just a copy that I used to show you guys the example.
And then, yeah, I would essentially just do that.
And I would have a soft CTA.
And then I would just add the lead to a campaign instantly.
So this is how easy it is to find companies.
This is what I call warm leads.
To find companies that are actively looking for the jobs that your service can solve.
For example, in our system agency, what we do is we can replace SDRs,
account managers, account executives, CROs, basically all of these job titles
that send like 15 e-mails a day.
Now we can send 17 times the amount of e-mails and outreach that we can do.
So I hope this makes sense for you, Axel.
Let me know in the chat.
Okay, guys, we have 24 minutes left.
So what we're going to do now is we're going to answer the questions in the chat.
Let me know if you guys are locked in in the chat by typing something.
This was great, thanks.
Hope that was an amazing answer.
Yes.
Another thing, since I am thinking about it, guys.
Another thing is I got to stop reading chat because it just overwhelms me.
Locked in AF.
Another thing is here's a hack for you guys.
And I never talked about this.
I'm probably never going to talk about this on YouTube just because I want this to be like,
I want this to be our little cult, which is why I want this community to be only 200.
So another thing, as you can look for companies that are basically looking for those jobs,
and instead of working with a recruitment company, you can target, let's say, a SaaS company, right?
And in the onboarding call, or like before the onboarding call, sorry, the kickoff call, on the sales call,
you would essentially just go ahead and ask them if they are currently hiring for a few jobs, right?
And then you would have, this is something I used to do, there's a little hack.
So you'd have a recruitment client and you would have companies that you would reach out to,
and you would just connect the two and then you would be a middleman.
And it's extremely, extremely easy for you to build a campaign for them because this is what I call perfection,
is you have two clients and you connect them both, right?
I hope this makes sense for you guys.
You connect them both and now have the highest leverage, right?
So basically, you have two clients and you just connect them both, right?
So you can basically have a campaign that's always targeting recruitment,
and not a campaign where you target other industries and then you basically get them to book a meeting.
Yes, exactly, be the middleman of the deal and take commission for being the middleman.
This is a little hack that I do and I don't think anyone actually does this.
I don't think, even if you go to X or Twitter, whatever you want to call it,
nobody actually does this.
Most lead gen agencies, they just go ahead and just reach out to people.
But yeah, this is the beauty of, we are a growth partner, not just a lead gen agency.
So yeah, now let's go ahead and through the questions.
Vishal, yes, let's go ahead and answer your question then.
So, hey Saad and Saad, always a privilege to be here.
The privilege is mine, my friend.
Did not see the thread, so here are my questions.
Prompt idea to use the job description to write Icebreaker for niche recruiting clients.
Yes, so you would have a prompt to use the scraped job description.
Yeah, so for example here, I have let's say a go back and I'm going to run this.
And for example, bundle three instead of bundle one, I'm going to go and I'm going to go to job description.
I'll have the beauty, like before we answer Vishal's question is, look guys, we have the city.
So the crazy part about this is in our generate email, we have this, but I've specifically included this for you guys.
I hope you're doing well.
I heard company name is looking for job in job location.
So like the responses that you're going to get are highly relevant, one and two timely, right?
So, OK, I'm going to go to bundle three and then I'm going to go to job is the description.
So you have the description so we can do basically feed in this data into GPT or club.
And based on this job description, generate a one line description, one line icebreaker that is matched pattern to our service,
which is connecting their company with their ideal clients, which are companies actively looking for these candidates that they are searching for.
Now, this is just a prompt that I just pulled out in my head now, but I will have to do some problems engineer for you guys.
Let me know if you guys want to put this in the classroom.
I'm going to be able to do that.
And it would be basically a knowledge base for us.
So we can just copy and paste.
So that would be like in like like an MVP, like just how I would set this up.
Right. So what I would recommend you guys is whenever you do your prompt engineering is always have an offer.
Right. And that offer try to merge that in the icebreaker.
It really is amazing. So you'd have an icebreaker that has your offer like a thoughtful question,
non-service question based on the data script from the job title, all the website script data.
And you essentially have two lines that has everything and have your offer there with a soft CTA.
And this is like how you achieve like a really great copy.
You could do it on a scale.
OK, another thing.
So we have 20 minutes.
OK, I think we should have more weekly calls, guys, just because there are so many good questions.
Automation to update existing clients on campaign progress daily.
Update existing clients. What metrics include?
So update existing clients on campaign.
Yes, that's that's really good.
So we have it actually in the classroom.
Let me go to the classroom.
So there's this system here I go, I believe it's in.
Blueprints. It's this one right here.
I think it's all.
Instantly, AI analytics.
So this system actually can get you AI's analytics.
And another thing I thought about it, right.
So I have an idea for you.
So here's what I would do.
So what I used to do is I would basically this is the beauty of having everything on your end.
So I would give the clients a ClickUp dashboard.
And since I have an automation in the back end that adds the leads in my ClickUp list of each interested lead or booked lead,
you would have in ClickUp, there is something called dashboard.
And that dashboard will have the metrics of basically weekly booked meetings that I would book them.
Right. And then I would send them this analytics every seven days.
And it really, really, really makes your service extremely, extremely high quality.
So you would essentially do something like this, but it was really worth it.
It's worth building the automation and like adding this to the classroom.
So I'm just going to add this to my to-do list and then add this to the classroom.
Another thing.
Claude's prompt for cold emails written entirely by AI.
Yeah, that's a really good question.
We talked, me and Ishael in the end about this, and I'm going to work on this.
So what inputs are needed?
Example, yeah, LinkedIn post, company website content, LinkedIn post section.
Okay.
How much would you pay for a...
Obviously, you can source a VA from, I don't know, like a Philippine or something.
I used to source them from Philippine or some other countries.
Typically, it's pretty cheap.
You would hire them off work, right?
It's probably...
Well, in my case, I used to pay them really well just because, you know, it's the best way.
It's good to pay them well just because whenever I'm trying to hire VA,
I don't like to hire...
Every time I want a service, I want to hire a VA.
I want to work with someone so I can just be my friend and I really want to pay them well.
Just because when you pay people well, they're willing to do better jobs.
And at the end, like, you know, when you are good to people, they're going to be good to you.
Yeah, so whenever I provide as much value, I always notice that the universe is just...
substantially just rewards me in some way.
I don't know how that works, but yeah.
Okay.
You roasted my copy for a client earlier today and told me to tell you the offers.
Yeah.
So I'm going to go back to the thread and then I'm going to reply to you with a customized loom, my friend.
So do not worry.
Okay.
Benefits over bigger agencies.
Commission rates start at 65.
Okay.
So this is the offer.
Question for the end.
You can work your process screen from leads.
Yes.
This was an amazing...
Thanks, man.
Okay.
The next question.
How would you position your commission for being the middle?
Do you take a percentage of the LTV?
Yes.
I would essentially take like more than 15 to 20 percent.
Right.
So let's say, for example, I have a recruitment company and they are looking for a software developer.
And I would like target the other company industry, which is, let's say, for example, a SaaS.
Right.
And typically like startups.
Right.
They are looking for software engineer.
And then what I would do is I would say something like, hey, I have candidates that are ready for you.
Right.
And what I would do is I would negotiate a deal with your community and I would say, hey, I have a ready to pull the trigger client.
And then I wouldn't tell them, obviously, the amount of money that they're going to pay me, obviously, because it's your entire deal.
Right.
This entire thing that you do.
So let's say, for example, I'll be like, hey, I will get you a hundred percent clients.
Right.
It's going to be like a real client.
It's not a booked meeting.
So you're going to have to pay me from five thousand dollars, like from five to six thousand dollars.
Just because you understand that the recruitment companies, they make a shit ton of money, guys.
Like, we will never be able to justify the amount of value that we provide to our clients.
Right.
Just make sure you do that because it's going to help you in your sales goal to understand that the client actually needs you.
You don't really need the clients.
Right.
They really need you.
So always break out of that belief system that you're coming in as you're trying to pitch your service.
You're coming in as a consultant or as a growth system, like consultant.
Right.
We're going to basically provide them a solution that's going to make them a shit ton of money.
It's going to grow their bottom line.
You don't have to rely on paperclip ads or referrals or word of mouth or any referral scheme.
So you're providing so much value.
So you have to come in as a someone, you know, that has authority.
So this is what I want you guys to do.
Yeah.
Our friend Edward.
When you send emails out for a campaign, do you use your own company domain or reach out to theirs as if we're in the middle, man?
Depends, man.
Really implemented both methods.
But in terms of being a middleman, I would recommend you use your own domains.
Right.
So because you're the one who actually is being middleman.
Right.
If you go to my website, I have a very clear value prop growth partner and I would recommend you guys do the same.
So what I would do is basically I come in as like an agency that already has leads.
So now that I transitioned into a product service.
Now, when you first get started, guys, I wasn't product size.
Yeah.
But then once I acquired a bunch of clients are like start product sizing.
Now, I really like have basically I provide them 10 ideal clients in 30 days.
It's actually clients.
And I also front load the ad hoc sales system build when in reality it's not really ad hoc.
I just copy and paste a template for them.
Right.
And I just record a video like a like an S.O.V. video and something like this.
Let me show you like a video documentation such as this.
And I just use AI for this, honestly, because I just don't want to deal with it.
And I just show them how the system works with a 10 minute video walkthrough.
And that's pretty much it.
And then I have here expensive new clients, pause or cancel anytime.
Basically like a subscription.
And usually I never get this.
Right.
I only pitch for the commit to three months and save $2,000.
So once you get some clients, you can eventually like transition into a product size service.
But you can never do it because you won't be able to do it because you have to get into the agency sales process,
which is getting into the calls before you actually get a client into a product size service.
So, yeah.
Okay.
So how does that work for the email warm up phase for three weeks?
Does this mean we wait for them to be ready for their domain?
Now, it really depends.
So let's say, for example, you are being a middleman.
What you would do is you would buy the pre, like you would buy the domain off instantly.
So you would use either you would go to inboxology.
And I believe if you make a post on the community, there's a bunch of people that are actually buying pre warmed up emails.
Right.
They were really like there's a bunch of platforms.
And then another thing there is pre warmed up emails, which I love.
If I go back to my emails and I click on add new, there's pre warmed up emails.
Right.
So click continue.
You can essentially buy the pre warmed up emails.
So there's 286 domains remaining.
You would essentially just buy one of these and go ahead and get up and run in.
I've done this.
I still do it.
And it really helps with front load much value instead of waiting the three weeks.
But for your own clients at first, if you haven't got any first client yet, don't do this.
Just send the emails first and then worry about this later.
OK.
Do you read a redirect pre warmed up emails instantly domains to the client's website?
Yes.
When running a campaign, that's 100 percent true.
Do you see that the emails are less active than the domains with the name of the company?
Honestly, yes, it's less effective.
But by what?
Like five percent?
Because they have some generic name.
Five percent or 10 percent less.
But it really offsets the three week waiting period.
If I had to wait three week period and but get a client and offset that with volumes, I would still do it.
Right.
Just depends on how you see it and how depends on like if you wait it right.
Let's say I wait three weeks, but I provide as much value.
But I offset that with volume and I get my clients who say, wow, this guy actually delivers results.
And then I'll shave that off and then I'd wait three weeks.
Right.
For me, this is way higher than this.
Right.
OK, guys, we have seven minutes.
Do you guys have any last questions?
Go ahead and tackle that.
So I hope you guys are having a good time like I'm doing.
Let's see. I didn't if I missed the question right here.
I believe I didn't.
So, yeah, just pulled up, but we'll watch the recording, bro.
For sure. For sure.
For sure.
Jesse. Hey, Jason.
Thanks for this. This today answered a lot of the questions I had.
So I have a question.
Thanks for the value.
Hey, Justin. OK, for sure.
Sorry, I don't understand.
But for the first time, would you recommend buying the pre warmed up emails?
First customer? No, I wouldn't recommend that.
I would basically for your own outreach.
But first, I want to understand, is it for your own outreach
or for the first client that you will onboard?
Just because it's highly dependent.
For the first client, I onboard.
OK, for the first client you would onboard, I would honestly,
I would 100 percent buy them the pre warmed up emails.
Just because reason why is I want to basically not get out of the park
with the clients just because I value retainers and I want you to get
the retainer for them from the clients.
And since they will see that you're going to provide the value at first,
think about it, they will be willing to work with you more.
So let's say, for example, well, it depends on how much they're going to pay you.
Since you would pay something like from fifteen hundred,
the minimum is fifteen hundred all the way to, let's say, three thousand dollars.
You would go into that medium.
So let's just go in the middle and go for like two thousand dollars.
Go to instantly.
And for example, you would get, you would have, for example,
that's like thirty, forty five dollars.
Let's say you're going to pay a hundred dollars for that, right?
That's that's still like an internal cost of two hundred dollars.
And you're going to send right away.
That's that's still cheap.
So I would go for the pre warmed up domains.
Hundred percent. Right.
Just because the amount of value that you're going to basically get
and provide for the clients is extremely, extremely high.
And they will really love you for that.
Trust me.
So how does the pre warmed up work for the system?
From what I see, those pre warmed up domain names look so spammy.
I don't like I wouldn't worry about it too much just because instantly they redirected.
Like I did.
I never had a problem with that.
I really didn't.
I really didn't.
So let me show you guys a campaign that I ran.
I think it was let me see.
I have to log into another account, basically.
Maybe maybe I'll leave it for the next weekly call.
Basically, I used to send this e-mails and I used to work with this.
This company is called Lead Loop.
And it was basically a B2B company of a manufacturing A.I. platform.
So what they used to do is basically they would have a software and that software
specifically for manufacturing companies just because manufacturing companies have extremely detailed processes.
And they like an A.I. software that helps them with that.
But what I used to do is I get them pre warmed up e-mails and then it works just fine.
Like I just redirect everything.
And like typically like in my messaging, I would say something like, hey, we do this.
Here's our offer.
And then once they click on the e-mail, they would not the e-mail, the domain, they would copy it and just get redirected to the
basically the domain domain.
And they wouldn't care.
Honestly, like I never had any problems.
I never had like a lead response or like, hey, why is your domain changed?
You know, just because they like the level of personalization that we reach out to and just, you know,
how I would like I recommend you guys use is extremely, you know, it offsets everything.
Like think about it, guys.
Like most companies that receive them, like even me, like I receive a ton of cold e-mails and they're all shitty, man.
Like usually when they receive them, I'm like, hey, you should you should watch my videos on YouTube.
How to write better copy.
So, yeah, guys, let's end with a win.
And got two more clients signed to build last two days.
All works.
This makes me super happy, guys.
You guys have no idea how this makes me happy.
Like, like this is where I really get my stuff from.
So make sure you put in a post about it so I can ping that.
That's really makes me happy.
And it's like a warm hug.
So I hope you guys found half value in this video.
Video.
So I'm still I'm recording a YouTube video.
I hope you guys found value in this weekly.
A call.
I'm going to make a thread just after this weekly call and then make sure put in your questions.
And obviously the first questions are going to be prioritized.
So thanks so much for joining this call, guys.
Love you guys to death and I'll see you in the next week.
Cheers.
Thank you.
