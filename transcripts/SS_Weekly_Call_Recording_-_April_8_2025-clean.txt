Hello everyone, hey <PERSON>, hey <PERSON>, Hyperlab, I believe it's <PERSON>, hey <PERSON>, hey <PERSON><PERSON>,
good to see you man, let me admit everyone, yes sure, hey <PERSON>, <PERSON>, <PERSON>, congrats man.
Yes sir, if you guys don't know <PERSON>, just closed a deal, which is awesome man, I've
been waiting for this one for a long time.
So <PERSON>, finally we get to meet man, face to face, let's go, AI agent, hey brother good
to see you man, long time to see, make sure you guys mute yourself and let me just add
<PERSON> because he had a really good question, <PERSON>, we have a packed session today, amazing
questions like always, you guys are always have great questions, good to see everyone
here, hope you all had a great weekend, so we're just going to wait until we basically
just admit everyone and then I'm just going to go ahead and share my screen and answer
all of you guys' questions, hey <PERSON>, how you doing, let me know if you guys can hear
me well, audio everything is good, housekeeping, okay perfect, all good, okay so I'm just going
to go ahead and share my screen right now and let's get started, so can you guys see
my screen, let me add the chat right here, okay perfect, so everything is good, okay
perfect, all right guys so the first question we have from our lovely friend <PERSON>, how do
you deal with tough clients, I've got a second client in a row who wants to oversee and analyze
every step of the way in creating a campaign, they always disagree with everything and want
to do their way because they know better, for reference they've been sending 70,000 emails
per month with zero to one meetings booked, also they got 18 domains blacklisted, I refunded
a client like this recently, what should I do with this one, I'm already too stressed
because of them and only worked with them for five days, yeah I remember man, I believe
you mentioned this in like a past weekly call where you had like a similar client where
they were basically just over analyzing everything right and I've had this before, I've had this
before with a recruitment client a year ago and I've had like this exact same situation
where I would just send them, like I would show them the copy right, back then I was
just showing them the copy and like I would send them like a Google doc here is a copy
that I wrote for you etc etc and then they would essentially just over analyze everything
and they would also like, like they would tell me why are we adding sends from my iPhone,
why can't we just do this or do this or this you know, so once in a while you're going
to have a client like this, it's just part of the game you know, but the best way I found
to deal with this is just to set clear expectations upfront okay, so looks like since it's, this
is like a recurrent event right, you said they always disagree with everything and want
to do everything their way because they know better, so the best way man that I found to
essentially just front load like just to like save you all that hassle, save you all that
headache is and you during your kickoff call, you want to set the clear expectations okay,
so you want to make sure one define your availability okay, two you want to make sure that the client
always like they have to know that you're going to have to take care of everything okay,
so make sure like whenever you onboard the clients let's say they're in the kickoff call
say hey I'm going to be available let's say like Monday to Friday from 10 a.m. to 2 p.m.
if you have any questions just tag me on slack or whatever your communication channel that
you use and maybe email etc. I personally use slack okay just tag me and I'm going to
be available from 10 to 2 p.m. and if you have any questions just send them over and
I basically this is what I do I like a batch all my clients from 10 to 2 p.m. okay, but
in this case it's probably going to it's not probably it's definitely going to be a communication
issue I'd like to guess that when you first onboard this client you didn't clearly like
the communication was off you know I can see this so it's just part of you know it's just part of
the game some client like once in a while you're gonna have a client like this okay,
but my recommendation is always always make sure just to position yourself in a way that if they
keep you know pinging you every single time and over analyzing everything that the campaign is
not gonna work tell them hey in order for me to actually deliver the results I need to actually
focus on actually doing the work good morning rather than you know going back and forth you
know in messages okay so just make sure you define this in the beginning and I've had a
couple instances when I had to refund a client like this I remember when I used to do like a
weekly call with a retainer and they would essentially like they're not as cognitive of
my time I used to do like a one-hour consultation where I go ahead and like basically let's say I
have a retainer clients every month we do like a weekly call we reassess their position and their
copy etc and they would essentially want to hop on a call with me more than that you know like
once every month they would be like hey guys can you like a mute yourselves to photocopy that
other check I already photocopied for where is the photocopy everyone the one you didn't photocopy
the one that was did I not give it to you I don't see it I sat down I don't know where you are this
is what I got up there right there let me just mute everyone right here mute okay so yeah the
idea here is you want to basically just define you know you're availability one and you want
to make sure you set clear expectations upfront and you the way you're gonna frame it is gonna
say hey in order for me to deliver the results right I need to work on this you know I need to
like devote the time and energy for this instead of going back and forth and messages okay and
since now like what do you do so what are the actual steps that you should be doing so I want
you to ask yourself do I want to continue or just end it be brutally honest if the stress is already
high it's probably not worth it and make sure to have like a clear process when you onboard them so
you'd say hey here's how my process works here's what I'll share with you and when here's what I
need from you I'll take care of everything example no micromanage in every word email domain or
overriding strategy unless there's a good reason okay so this is what I would do man again it's
just part of the game sometimes you're gonna deal with some clients like this you know but I think
in your case it's time to qualify your clients more because you don't have a pipeline issue you
already have the clients you have like I think you have like three to four clients right now so I
think it starts it starts to you're gonna have to start to qualify your clients more you know yeah
and if a client just bothers you like this it's just the best way to just refund them it's only
not worth the money you know okay so I hope this helps man and yeah like TLDR always have a clear
process during the onboarding here's how my process works here's what I'll share with you
and when define your availability make sure you have clear expectations upfront which is why I
would recommend you guys always frame yourself as a growth partner and use like a professional
language with clients okay so it's gonna differentiate you from all the service providers
because most service providers you know to be frank to be frank our shit you know you don't
have a clear process they they essentially text clients at 2 a.m. which I wouldn't recommend at
all right so in the first call first like kickoff call you know just make sure you have a clear
expectation what the client will essentially get during the process yeah okay so with the
next question which is a pretty long question here we have hey sad hope I can finally make
this time without a cell without a sales call interrupted missed a couple calls just catching
up got a few questions for you you automate Google Google Drive folder created for onboarding if so
what do you usually include yeah I do man usually what I do is I create a folder and then I create
a Google Doc out of template it which is going to be the copywriting and then I also automate
the brand research and the best ways to actually show you is I'm gonna walk you through my mix
scenario so it's called my process onboarding so I'm gonna go to scenarios and I believe it's
process onboarding yeah this is the one yeah so let me walk you guys through my entire onboarding
okay so okay sales call finished they paid now it's time to go for the onboarding so essentially
what I do is just send them the onboarding form that everyone has yeah and I get I get on a call
with them during the onboarding and what I do in order to commend you guys do just to save you all
the house all that headache is you want to feel like you want to take their hand and essentially
fill out the onboard onboarding form together okay so together you're gonna be like hey it's
gonna take us like 10 to 15 minutes to fill out this form let's go ahead and do it together I'm
gonna walk you through all these questions etc so they oh they share their screen you walk them
through step by step on the onboarding form and the cool part about this automation is that it's
it's gonna catch the responses okay so once you the decline just finish up you know filling out
the responses there's a module that's gonna watch the responses I'm gonna create a drive is gonna
create a copywriting document so basically gonna be just a like an empty Google Doc where you're
gonna essentially just pre-draft the copy they're gonna use in their campaign and then what you well
what I would commend you do and this is what I do is essentially I add the responses in a Google Doc
so instead of me just going to type form every single time and just looking over the responses
that they filled I can just have them in a Google Doc and I can essentially just have my own clients
had their own Google Drive right and their own copywriting documents and their own onboarding
responses and then essentially I just go ahead and just click on that Google Drive and essentially
just stay there so I don't get overwhelmed with my many clients okay and this the next thing is
once we create a copywriting document and we create the onboarding responses we create a
channel for them okay so the channel is like this it's gonna be my process is this is their
company name right so the company name along with dash my process playground okay so this is the
channel that I created for them so it's for example like example company dash my process
playground and then again I said the topic which is gonna be hey company name or hey first name
welcome to your communication channel and then I emailed them back the link which is a hack that
I used to use so I don't think it's worth it to buy the you know the the enterprise plan because
in order for you to add someone to your slack channel you need to get the enterprise plan now
I only stick to the pro plan and what I do is once we fill out the form together during the call live
they receive like they receive the email right like at that at that moment and the email there's
basically here's your link to join your slack channel and click join right so they click on
that link while I'm on the call with them right and they accept it and then again they receive
this text right here which is hey first name here's a quick rundown of what you can expect
our best way is to stay in touch via slack I'm available from 10 a.m. to 2 p.m. Monday to Saturday
for any questions or updates I'll send you progress update twice a week so you're always in the loop
of what's happening looking forward right so the cool part about this is that they receive that one
on one so on the call with them so they always be like sad wow that's that's crazy automated how
are you how are you able to do this you know so it's kind of like a cool way to impress your
clients and it's also you know frames you as a as someone who's been doing this for a while you
know and then those clients are not going to be able to step on your toes in the future you know
so they receive this defined availability while I'm still on the call with them you know
which is kind of like a really cool way to you know position yourself as a growth partner and
there's not just a random service provider okay so what they what they do essentially is just
click on this link right here and they join and they already like the channel has already
been created you know the topic has been already set and then the the availability has already
been set so now we just finish up the onboarding if there's like credentials that's what I need
to get access to I just finished up I just finished the call with them in like 25 minutes
30 minutes and essentially I just get up and running as quickly as possible what I what you
can add and I used to do this for a while like a long time ago is you can add like a module right
here from OpenAI that can generate like a example of domains abbreviations for example like get try
and then you feed that to AI you can add it to like another google doc for example you know
so you can just go ahead and basically just copy the url and just purchase the domains for them
okay so I hope this helps man let me know in the chat
yeah
so you'd have like a basically a but just the channel they're going to see all the members
no it's a private channel okay it's a private channel it's going to be there
so the only person that's going to be there is you and the clients that's pretty much it
and it's typically either going to be the founder or maybe like their managing partner or something
you know in my experience
yeah so you create a google drive you create a copywriting documents along with some sort
of brand research just because you want to basically have like an like just a google drive
for them yeah you need the pro version yeah 100% yeah you need the pro version for that
but in order for you to add a client to your slack channel with the enterprise
so this is like the hack that you can use just send them the link and they can essentially just
join using that link and that link you can send it to every client which is the cool part about
this automation okay so once you create a channel with that client right that channel has a that
channel has the url of that link right here okay this one
the slack invitation which is the cool part of this automation because it's stable
so you need the pro plan for that
yeah you need the pro plan
for slack do you add all your clients to one pro workspace with private channels per clients i've
been creating separate workspaces with three or four channels each yeah man i think this
answers your question yeah you need the pro plan for that and i believe if you sign up
the paid plan i believe if you sign up they're going to give you like 60 days
after the 60 days you're going to have to get the paid plan yeah
keep in mind only do this if you have more than three clients okay
you have a really nice like a really good question here how do you handle monthly costs like instantly
mailbox that's are covered by setup fee and retainer from month two yeah so the way i usually do this
is i cover all the costs from the from the setup fee right so they usually pay like an upfront fee
and then i just use that cash collected and i just basically just get them the domains it's either
going to be pre-warmed up domains or i just go ahead and like buy just random domains off name cheap
or like pork bun and i just hook them up to my own instantly account okay and in terms of the retainer
here's a hack that i would recommend you do so depending on what offer you currently run in
you want to use that cash collected from the setup fee and basically purchase everything you know the
mail inboxes etc etc and then when you want to transition into a retainer you'd say hey
um i see no reason why we can't scale our outreach even like five times more and
you you would essentially just upsell them really hard into that retainer
and then let's say let's say they pay you like 1500 for that setup fee okay and use some of that
money to get the campaign up and running once you deliver those five book meetings or whatever book
meetings you've promised okay now it's time to upsell really hard you'd say hey um i see no
reason why we can't uh you know scale our outreach even even uh even further and um we can do this at
x amount of like x amount per month and this is where you sign them up to like a three months
retainer okay this is what i do so the cool part is you need to
really really deliver the results let's say they you promise like five book meetings
right so those five book meetings you'd essentially have to deliver them
as quickly as possible and then you have the total right to upset really hard okay
um do you set up separate instantly accounts per client um so i've experimented with both approaches
the best way i found is to just get my own instantly account one it significantly lowers
the cost right uh because i'm using my own instantly account i just purchased the 1000
uh per year so one thousand dollars per year and i'm using my own account and i'm using my own
the 1000 uh per year so one thousand dollars per year and essentially it's like you like a yearly
plan and i just uh essentially just give them like their own organization right and i just
share with them the campaign analytics i think this is the best way to keep your margins high you know
um
yeah um another question you have is what's your go-to way of sending lead background info to
clients before calls yeah so this is highly dependent so i think in your way it's time to
systemize the process um what i used to do is uh what i would do is i basically just during the
kickoff call i'd say hey how can you can how can we handle lead responses that we actually don't
know like we don't know how to actually respond to these leads so essentially what i used to do
is i'd have like a automation in the background that waits for each interested lead and if um and
i add it to my you know to my click up account you know to my click up crm and then if it leads one
wants more info essentially just forward that reply to the clients you know and the the client
just handles their prize you know sometimes they handle it sometimes they just email me back and
say hey here's uh the response that we want to respond to that to that leads because sometimes
a lead would respond and you wouldn't know like essentially like uh like what to respond with you
know because sometimes those niches are really really specific and even if you know the the
business like that you release business like inside and out some some questions can be really tough
to answer so what i would recommend just forward that reply to the clients okay or you can do this
you can essentially just uh send them back to slack okay so we'd have like a watch web hook
you know that watches the interested replies and then it's gonna have ai determine the engagement
which is going to be lead wants more info and then you can just send it to the slack channel okay
yeah
um what's your go-to way of uh okay we answered this how do we stay organized across multiple
clients what's your typical weekly workflow yeah so nowadays is uh different than back in the day
now i uh what i do is i basically just have like two to three hours where i batch all my clients
typically it's gonna be in the evenings right um but back in the day um what i what i used to do
is essentially and what i would recommend you guys do when you have multiple clients okay
is you want to like um batch all your clients right so batch all your clients let's say for
example uh you would bash them in like three to four hours during the day and then you would
send them campaign analytics every like uh let's say every friday okay of the campaign
you know that's uh that you've been working on it you know and um how do you stay organized
it's definitely going to be the right answer for this to have like a crm where you have you know
your your clients in one folder and your own pipeline in another folder okay and this is
gonna come in into the crm that's gonna that i'm gonna be launching in the community in just a few
days yeah so you're gonna have like a space so what i what i do is just i use click up and i
have a space right a space where i have my own pipeline right that's fully automated too and i
have another folder right each client has an entire folder where they have like the their own code
that they paid me along with the book meetings that i've booked for them you know and uh essentially
every lead that response is going to get added added to that crm okay and then each
stage of that lead is going to be leads for once more info lead interested until it's lead booked
you know book the meeting okay so this way not only you stay organized but only you can share
that click up dashboard with the clients and they can see in the in real time you know the
book meetings that you've booked okay so i see a lot of people are actually now past yeah past
the getting their first clients which is awesome so now now it's time to scale yeah
yeah don't worry jason it's going to be very very simple you know because i always recommend
having a pipe like having like a crm that's very very very simple you know just because
you don't want to like automate everything you don't want to have like a super super complicated
process okay yeah so you're going to have like a you know like a workspace and click up that's
going to have a space for your clients and a space for your own agency right
okay do we ever connect your crm to theirs yeah um it's very very simple um during the
kickoff call uh you would want to share access to their api you know whether they're using
hubspot et cetera and then you just connect that with instantly so whenever like a lead
books a call or whenever a lead is interested you just add that lead to their crm using api
very very straightforward
so they are all awesome questions man yeah okay jason said in order to keep your funnel
flowing with leads how many campaigns are you starting new each week do you keep adding buying
more mailboxes to get through the 5k leads in two weeks or are you rotating inboxes through your
campaigns i'm assuming if the campaign is doing well you just scrape more in the same niche yeah
that's correct how often do you branch out to new niches on the one thousand dollars uh instantly
plan how many contacts are you allowed as they have to delete i have to delete leads now as i
close and on the 25k yeah so um what i do currently is um i'm not sending as much just because uh you
know i'm more like i'm more focused on more of consulting with these clients i have a couple
retainers that i've been working with for a while right now more than six months um and these are
these are clients that you know i already nailed their process they understand their products i
know their business in and out right so what i would recommend in your case um keep buying more
in boxes right just because in this case right here let's say you run a campaign okay and you
have like interested leads and let's say you book like in 30 days you book like 10 meetings okay so
why like why branch out if you can just add more volume you know
that's my thought process like why would you like what would you why would you just
leave a campaign that's already working right and there's just branch out
now
keep in mind when i when i when i was scaling my agency
i was just basically like i had like i would run a campaign for like three weeks just one campaign
right
yeah so i wouldn't branch out at all i wouldn't go over one campaign every two weeks you know
because you want to you want to denote the health of the system right you don't want to just keep
branching out because this is how you actually like uh you know get overwhelmed with campaigns etc
so you want to stick to one niche right stick to one niche and keep refining your copy etc as you
go you know this is what i would recommend and in terms of mail inboxes once you get
once you get like uh initial responses the offer is landing etc it's just time to
up the volume then and the way you up the volume is simply buy more inboxes and just send more
so this is essentially what i'm doing currently for a client you know so this is a client that
i'm working with and uh it's called um this is a client that i'm working with it's actually a member
in our community which is awesome it's called companion so essentially it's a um it's a it's a
product that essentially just uses ai to clone your own voice right and essentially this is a startup
and essentially this is a startup right and this is with no personalization no whatsoever
the only thing that i've done is i scraped 10 000 leads and uh usually what i do is i have like a
initial phase and the the initial phase is basically just the copy with no personalization just a good
good offer right just because i want to squeeze as much juice with no personalization nothing
just a clear offer so i can see you know what's going to happen and then if there are if the you
know if the results are not the best then i would add like a personalization line you know
i would always recommend you guys do things that don't scale at first
okay just because you want to squeeze as much juice from the lowest hanging fruit
before you actually upgrade you know
um how do you handle the recruitment niche and recontacting leads that's uh that did not reply
but a new job posting have been posted and you want to reach out again this is after the three
emails initially sent would you reach out would you delete all no replies for after a week or so
you can load them again um i wouldn't recommend i wouldn't recommend you recontact leads
um after they did not reply yeah um ideally we'd want to wait at least a couple months
yeah just because when i used to do this they would get annoyed
yeah
yeah it's better to just stick out to new leads you know you can you can reach out to them and
and like a couple like let's say like after 30 days or so you know it's best that way and
what i used to do is just cross reference the last email i would say hey like i would add them
like into another campaign right and in that campaign i would say hey um we i've initially
contacted you like a month ago and i just wanted to touch base and i saw you like i saw your job
posting on x or y i thought reach out you know this is way more relevant more way more time
this is what i recommend you do yeah it's better to to reach out like a month later or so and you
have a better chances of closing closing yeah yeah no worries man i've had booked meetings
but always get stuck on them waiting to paper results as we know cash flow is king yeah that's
true what's your best way to overcome that and would you work with them or show that you are
not willing to work on a paper lead payment plan um that's a good question then so
if it's my first client i'm willing to work on a paper lead model because think about it like
just think about a long term if it's going to take you just one or two clients to compile a case
study and then you're never going to have to worry about this ever again i'm willing to take that
you know
i think it's the best way to do it you know
um so um i'm curious like how does your sales call go i would love to i would love to see like
a recording or something so i can spot maybe there's like a communication issue or something
because ideally you would want them to yeah ideally you would want them to pay at least
like an upfront fee to get up and running as quickly as possible you know and the best way
i found is just show them the ry calculator you know again if it's your first one to two clients
totally just go for the paper paper lead you know just because you're going to compile a
case study and you're never going to have to worry about that ever again you know
okay uh sven sad just closed my first deal post here quick tldr below five minute loom and other
posts explains more any advice any advice on how to approach this how do i make sure that i don't
fuck this up thinking i need to make a clear roadmap for myself first a then b then c etc what
are your first thoughts that could help me make sure i crush this and i give them every reason
to collaborate for their scale up our engagement so the best way to actually just go on uh we're
gonna go on a call together uh for your first client reward i'm gonna walk you through step
by step on actually what to do you know so this is the best way to actually approach this just
because i need more context on actually what like what you promised etc and what their initial offer
so just give you guys some context um the first client reward essentially we jump on a call right
and you walk me through every single thing their business like their current offer and we just
basically brainstorm the best business positioning for them and uh what are the loot leaders in
databases that i would recommend you go for along with a copy etc and essentially you would you
would pre-draft like just something like it doesn't have to be crazy just something that you would
write like a copy and then you would send it over to me and then i'll just make a like an edit for
that and then just basically just send you the finalized copy along with the positioning and
it's gonna set you up for success for the campaign okay so we're just gonna hop on a call and just
walk me through it and uh that'll be it
okay so we have a couple more questions here
last week i had two meetings and went and both went something like this
uh the first was with a guy that i could work with and it's solely my truth based on your
advice to he sells wrist protection for kettlebells and wants to get them into cross-fed gyms around
yeah my task would be to send an offer to the owners to pitch this he let me some time to think
about it while he put some emails to warm up and said he would pay me monthly and percentage based
yeah that's a really good offer yeah i'm gonna pay you monthly along with some sort of commission
yeah okay the second meeting was with a guy that sells a complete infrastructure of content funnels
etc and his target audience is non-tech companies fortune 1 to 500 okay that's interesting his
monthly revenue is 80k and the project will start in june since he has to put something in place and
he said he is willing to pay a percentage based on uh and his only concern is depersonalization
to be good enough how would you proceed in this situation yeah my approach for this
is basically um he sells wrist protection for kettlebells and wants to get them into a cross
fake gyms around us yeah so yeah 100 i would scrape google maps yeah i'll scrape google maps
for gyms around the us and then once i scraped that i would just run them through the email
finder that we have i would also leverage exa i wouldn't i wouldn't use uh linkedin or apollo
for this you know because you want to scrape gyms uh the best way is just scrape google maps yeah
so scrape google maps and um you would have another thing you would scrape exa for this too
you know and you would just build like a list of five to six thousand leads yeah
so the second meeting was with a guy that sells a complete infrastructure of content and funnels
yeah so his target audience is non-tech companies fortune 1 to 500 his monthly revenue is 80k and
his project will start june as he's only like his main concern is personalizing to be good enough
yeah the best way here is to just have a personalization that slowly transitions into the
offer yeah i can help you with that just um make sure you post in their offer like a in detail
and i'll just generate a like a prompt for you so the way i do this is i just feed ai the initial
offer right and then i uh i just basically match pattern that to the prompt right so if you guys
like uh saw the the personalization that we have here which is personalization
prompts for personalization this is the one that we use
craft a personalized tool line icebreaker based on tile for inference and this is the one relate
these insights to the service being offered so in your case you would say hey relate these insights
to the service being offered which is going to be the offer of your clients now we have building
sales systems specifically designed to connect them with your dream clients now we'd put the
offer of your clients here okay and then now we can basically just match pattern that prompt
with the offer and you're going to have a super personalized outreach
so i hope this helps next step for you is just basically just uh make a post about their offer
i'm going to help you with that okay uh vichal my friend said how uh could we build an automation
for recruitment clients to scrape all new job posting from each day for example at noon identify
key skills from job descriptions um reach out directly via linkedin slash mail hey first name
if you'd found your job title in job location in 24 hours with experience and skill set would that
be relevant yeah we actually do have a system for this and uh the one who actually built this is
aaron if you go to the classroom you will see it in the onboarding actually if you go to
best of sales and mastery this is the one i believe that this is the one from aaron yeah this is the
one it basically allows me to keep a campaign running without worrying about scraping new leads
only contacting camp companies actively looking for him for my solution this system is based on
sas idea scraping companies looking for sdrs or similar roles uh on job portals like indies or
linkedin so essentially the scenario one runs the actor every three days it fetches job postings
for open sdr or similar positions on indeed or linkedin published within the last 72 hours
the scenario two takes the extracted data validates the job posting to avoid contacting companies that
are not looking for roles related to our solution and extract the company domain extract the full
name of the co if missing plus find the find invalidate the emails and create a personalized
message and adds the lead to content to instantly right so this is like a crazy system this is the
one uh the only thing that would change is just the copy here okay so the copy would be like hey
if you would find you and you just map the job title obviously you're gonna have to clean the
job title um in job location which is included in the job and the job posting all the time
and then you would have the skill set here so my thought process here is you need like a
couple modules before you pre-draft that copy that takes in the skill sets and just quick like a
structures them and then you just map that into a dream email yeah
okay so we we have another question once we get a positive reply from a lead in our campaign either
ours or for our clients we nurture them to book a call and send calling after that but the lead
didn't book a call even after agreeing on the time suggested how should we uh follow up like
the intervals between these follow-ups and how many in total yeah i have a document for you man
basically it's a eight step follow-up sequences essentially this is what i use from my process
and essentially it's uh it's like a eight step follow-up we have the first one the soft nudge
and then we reaffirm in the value we have another follow-up the fourth follow-up and the fifth the
sixth seventh until the eighth one which is it has it has this uh uh intriguing subject line which
is have you given up on this and this is purely inspired from the book never split the difference
i would highly recommend you guys read this if you really want to dial down yourselves it's really
really good book i read this a year and a half ago so yeah um the intervals between these emails
is going to be 48 hours so first email is going to be this first one again 48 hours you send the
follow-up another 48 hours you send the soft no oriented question and then the fomo social proof
time constraint approach and then until they essentially just book a call you know this is
what i would recommend okay so um let me go back here okay a few questions do you follow up and to
an uninterested reply up to eight times in the same email thread or do it differently yes always
in the same email thread you know just because we want our lead to go back to the initial email
right and read through the offer so they get more context you know just because if you want to start
a new thread it's kind of like sending them like it's kind of like a new person sending them a new
email you know always follow up in the same thread okay so they just have like a reference what was
the initial email at first okay am i missing any automated fulfillment on behalf of the client's
campaign analytics booked meetings to click up is it advice to send interested lead responses so my
advice is always send them the leads that actually booked a call i think this is the highest
the juice that you have there just because clients wouldn't like in my experience if you send them
like a interested lead unless it's the lead that asks for information that you do not know
the response to let's say they ask a question that you do not know the response to you just
send that to them but it is best advice you just send them the lead that actually booked a meeting
that's the best way because this is the actual deliverable that you promised
uh third question also when fulfilling a client and warming up mailboxes do you use their name
and similar domains to your client or do you use your own and act as if you're reaching out on
behalf of the clients yeah uh what i recommend is always um use their name yeah so you would have
their offer copy and you would have the name along with company name and uh that's pretty much it
yeah all the time even if you're using pre-worn up emails always make sure to use your client's name
okay first name company name and then uh the offer on top that's pretty much it yeah it's better that
way and in terms of uh mailboxes you would have like just kind of like it's just the same thing
when you want to purchase your own domains and mail inboxes right let's say my domain is like
mayaprocess.com and it's like sad at mayaprocess.com you'd have uh your client's name or client's first
name uh clients dash first name you know all these variations along with their variations of the new
bot domains that you purchase for them okay that's the best way to do it this is because you want to
make sure that when a leads um goes to that to the website that you basically just uh purchase
for them they land on their landing page which is the initial domain okay okay so i hope this
helps man uh we have three more questions and we're gonna get go to the chat and answer all of the
miscellaneous questions that we have here can you explain the best way to navigate the microsoft
inbox issue my campaign flop because most of my users microsoft and i only have google inboxes
would you recommend would you recommend scaled mails if yes which plan uh would be the best option
yeah i would recommend using scaled mails but the issue is is here you need to hop on a call with
them and uh their pricing is very very like varies on your own instruction infrastructure so the best
way is just to book a call with them and they're going to give you a quote okay so if you click on
get a price essentially you're going to have to book a call with them and they're going to give
you a quote tailored to your entire infrastructure so in your case let's say you want to purchase like
50 email unboxes of microsoft um what you would do is essentially just book a call with them and
they're going to give you a quote or if you really want to you don't want to deal with this just get
the um get domains off go daddy and purchase domain microsoft domains off go daddy right
it's the most reliable one in my opinion just because you're getting them off microsoft you
know the main service like the main uh you're going to get like high quality ips off of them
because they're it's their own server right instead of just relying on a third-party provider
okay and then you just link that to instantly and just begin your own process okay so what i would
recommend whenever you buy your domains you would get for example five domains off name cheap and
you would use either premium inboxes or puzzle inbox to get the google domains right and then
in terms of microsoft just get the um the remaining five domains off basically go daddy
and just purchase the microsoft off go daddy the best way right if you don't want to deal with that
you can just go ahead and hop on a call with them and then they would give you like a quote
yeah just because it's highly dependent on your entire infrastructure their pricing
it's very very dependent on your infrastructure okay this is why they they don't include their
pricing in their landing page okay so i hope this helps man and we have another question here
when setting up your instantly email accounts before a campaign launch you said in a video to
set spam protection to only five percent yeah that's true why so low when the higher number
like 95 be better so the idea here is pretty simple the spam protection setting up and instantly
right is not how much protection you want it's the threshold right is the threshold at which
instantly will pause sending if a certain percentage of emails hit spam so after five
percent you're essentially just telling instantly if more than five percent of my emails are going
to spam then pause the campaign and let me fix it okay so at 95 you're basically saying keep
sending if even if 90 of my emails are marked as spam okay so this is my thought process so five
percent is safer because it helps you cache the issues before they destroy your sender reputation
just because a higher threshold would let spam issues spiral out of control hurting your domain
inbox placement and long-term of the liberty so this is why i recommend you guys always make
sure it's five percent okay if you put in 95 instantly it's going to keep sending even if
95 of your emails are marked are spam so i hope this helps um the ssm command center is great
thanks is it possible to add bulk verification i.e. post a list of emails to be verified instead
of one per request it would speed up the process a lot yeah i was actually thinking about this
before this call um so we need to ask sammy for this um i think sammy's in the chat can we
actually include like a ui for the cell system command center we can just bulk upload
he said okay
is it doable can we actually do it
yeah it's easy and i will do it of course man it's everything is easy for you
uh yeah of course it's easy it's only gonna take me five minutes
um i was thinking of just an api endpoint that accepts a bulk less not an ui
so what do you want guys you want google sheet like in million verifier or we want
it here's my thought process sammy if we can do the same thing as any mail finder we can just
basically i think this is going to help people a ton okay um i think we can just download a list
of epi file right like google sheets and then we can just drag and drop that lead list into our ui
and then we can just drag and drop that lead list into our ui in our ssm command center dot
com and we can just essentially just download that lead list along with the additional information
that we have let's say we have in the lead list first name last name company name etc
and their emails right we can just validate the emails or we can just map the full name along
with their domain and we can just find the emails just kind of like any mail finder or any email
enrichment platform does that make sense okay so you want to publish it yeah okay so okay i will
thanks man yeah i guess thanks um another video is gonna drop in just a few hours
i'm gonna walk you guys through a step by step on how i scraped 10 000 leads off crunchbase and how
we can do the same it's gonna be up in like one to two hours and i'm gonna post in the community
is gonna be community exclusive your eyes only um and um i'm using the command center to enrich
leads and write the copy etc and i'm also gonna leverage the new feature that we just included
which is the white label case studies where you guys can mention hey i partnered with my process
and we directly added x amount of money and y time frame that's gonna help you guys a lot like
close deals even faster and gets more responses in your campaigns yeah yeah 100 just and what
i've done is yesterday i went ahead and basically hopped on a call with six or seven of my clients
and i essentially told them hey i've included this in my community and if a client reaches out to you
yeah you can basically mention that yes my process partnered with that just because i wanna
i want to see you guys win yeah so don't worry now you can just use it so you can say hey i
partnered with my process and we've directly added to venture um 85k yeah and it's like 100% ethical
because you know i approve with that you know so if like a lead reaches out to me i'm gonna be like
hey yeah we worked that we worked on this together you know they can't say anything you know
so i think it's gonna help a ton
yeah so you guys can mention that and i have like a
eight or nine case studies i have one in i have one in recruitment i have a bunch in the it health
industry just because uh it's one of the niches that are really dialed down
yeah and i also have ones in sas industry i also have one in the crypto industry
and um i'm not sure i have these exact reply rates but i think i have a general ballpark
i definitely do the i do definitely do have the revenue generated i have one with connect group
which is a recruitment company in the uk where i directly added them 105k in top line revenue in
a quarter this is a huge case that you guys can leverage you'd say hey i partnered with my process
and we directly added to connect group 105k in top line revenue in less than a quarter
and i think this is gonna help a ton of people here
this is kind of like bootstrapping your way into success okay so i think um we answered all the
questions we have 10 minutes left so i'm just gonna scroll down scroll down on top so i can
read the questions that we have here in the chat um
let's see
um a few days to dcrm yes the crm is something i need right now looking forward to it yeah
yeah don't worry actually i'm gonna like i'm gonna make a post about it in the community
maybe in like a three days max just because i've been um working on you know creating a bunch of
just because i've been um working on you know creating a bunch of contents for the community
right along with a few step-by-step builds the the one that's going to be posted in just a few
hours with me would be uh me walking you through how i would scrape 10 000 leads using a few hacks
that were never shared on youtube never shared on any platform and tldr basically we're going
to be using a proxy so you're going to be using a proxy that lets you use high quality ips us ips
so you can essentially bypass the 5k leads and then we're going to scrape crunchbase and you can
essentially just scrape 10 000 leads even like 15 000 leads off just one one free apify account and
one free crunchbase account run them through the email finder which is basically zero dollars so
essentially like an entire campaign with zero dollars the only thing you have to do is just
buy the domains and you can just buy the dot info domains right it's going to take you like four
bucks which is like in total five domains like 20 dollars and for the email account it's going
to take you like 70 bucks so now it's even less it's like so it started campaigns like 150 bucks
and if you manage to close a deal worth of 1500 it's like 10 x out of y which is crazy right
okay i did two different niches in two different languages at once but best asad is saying yeah
let's just stick to one niche in my opinion because this is like a mental model you need to
basically stick to one mountain and essentially just refine it as you go you know so the main
reason like i stuck with rukmin is because i was like okay how can i like actually dominate this
niche i'm just gonna stick with it you know
i've had people tell me that they are getting a lot of same sort of emails and i thought i
tried to move away from recruitment niche and test waters elsewhere so i believe you're targeting
like just a broad recruitment niche so we can think about it like there's a lot of sub niches
in the recruitment now right now like a recruitment niche that's really popping is the ai recruitment
niche because there's like 600 i believe it's like 670 ai startups just in the us every single
day so more recruitment like ai recruitment companies are looking to hire ai developers
ai engineers etc okay so i would recommend you go for that
when you change the niche do you update your website to align with that niche i keep the same
website yeah joelle said i keep this yeah this is essentially what i do so if you look at my
website i have rebuilt growth systems for b2b founders it's b2b you know
the best way because you don't want to silo yourself okay unless you find like a repeatable
scalable niche then you can just go ahead and like just change that landing page specifically
tailored for that niche my main issue is not so much position incorporated but more the
automation and the tech part yeah i think you are in a position now to start on like a having a
clear process and yeah i think i think just thinking about this i should probably include
like a new module to essentially just have people inside that's already gotten their first client
set up i was thinking to actually like create a new community for people who are actually just
who actually like uh passed that phase but think about it now i think people already are in the
community can just basically instead of them joining another one they can just basically
get that for free and then i'll just create free content for everyone i think that's the best way
okay i think that's gonna like long-term strategy this is the best way i think it's gonna also
gonna give me like a bunch of goodwill and i think i just want to see people win and like
scale past that 10k so i'll just include it for free yeah um hey i i got a sales call in a few
hours with a guy that said he is not able to paper meeting and asked if it's impossible to
paper close i said i'm open to discuss but i think this should really depend on a his offer
price and likelihood of closing deals what type of revenue share would you aim for is there a norm
for based on is there a norm for this based on various sales amounts um i would never take a
commission deal just because you have no uh idea if they would just basically close a deal and you
would never know right ideally would want to i like align incentives okay i would never take a
commission deal unless there it's a paper lead okay always go for paper lead you know because
you don't know if a client will close that that you know that deal or not they essentially can
close it never tell you even if you can they add you in their payment processor so the best way is
just agree on a paper lead again get go get on a sales call and try to pitch a paper lead model
okay but never take the commission deal right
just because you have no control in terms of like have no control
in their closing rates you know your job is to get them to book meeting their job is to close
the deal you know so i'll just run away if it's a commission deal you know it's just not probably
not worth your time joel said i had one like this this morning i i refused him you're taking
too much risk and reason they ask is because they either don't want to pay they are not confident
in their offer and ability to close their prospect yes it's 100 true but you can still see what they
can say and try digging more yeah yeah i would hop on a call and i would just pitch for a paper lead
model yeah
okay made to help i'm not asking for to now now no problem man
okay i think premium inboxes has microsoft no they don't actually they only have google
okay scaled mail is a little expensive around 188 a month for 20 to 20 30 20 30 percent of google
and 70 percent microsoft yeah yeah so i would just get them i would just get them from godaddy
you know when you buy domains off godaddy you can essentially just get the microsoft accounts
okay i have another question i have one question if you plug the instantly api to their crm to send
info if you have several clients on the same account will the positive replies not get mixed
up to all clients no actually when you when you basically have a new organization for each client
you're going to have a different api key for each organization which is the co-power the co-power
about instantly okay and another thing even if you don't have an organization in the webhooks
you can see that you can just map in the the exact campaign okay so if the campaign
let's say the the name of the campaign is client a in the webhooks you just map the
exact campaign you know that's it and you're only going to receive the responses from that exact
campaign um i have a few lead recruitment tell me no upfront fee like zero even after telling
them i don't have profit from the fee they're still telling no fee meeting if you know meeting
so i don't really have any fun to start with them i have 300 meetings this week i was thinking to
tell them i will go back i'll get back to them next week in case i don't convert the three
meetings explain them i'm busy with new clients this week yeah um if it's your first clients
i would just go for paper lead model right if if they don't want to upfront like i
don't want to pay for anything um yeah like like i said like the first clients
it's totally cool to work on a paper lead model um i would also i want to audit your sales call to see
maybe it's like a communication issue or something right because if you provide like if you show
them the value they're gonna get right they're they'll be more likely to pay at least like a
small fee okay um let's see did you work with a website creator niche is easy to find clients
for them i don't i don't recommend working with uh i believe you're talking about websites
like web design agencies yeah i don't recommend working with them and this is a very controversial
opinion just because web design agencies just their offers just suck i mean think about it
there's a million one web design agency they don't have an offer at all like what is their
offer creating websites you know so you know what they do is they essentially provide awareness
not necessarily an offer that is revenue generated to the other party which is their ideal client
okay so i would pick a bdb niche i wouldn't work with the web design agency at all
you know i would just stay away from that niche
man you're literally giving everyone no excuse not to succeed yeah well yeah i mean this is
like uh the real juice about this community because i was part of a bunch of communities that
and i just didn't like the way they were treating the students and i was like how can i fix this
um okay let's see if you have another questions
i have one client selling a website but at least they focus on a niche restaurant but we'll see
how it goes um this could be different right yeah you can essentially just scrape restaurants
and um typically these restaurants actually they don't have a website so that might actually work
just make sure you have a clear offer upfront with them and make sure you always like frame the offer
this is a hack for you guys whenever you are building an offer for a client or build like a
writing a copy make sure always always always always to frame it as revenue generated okay even
if the offer or even if the the client's like service is going to save the other party aka
their idu client time always mention revenue because eventually like whatever lead cares
about is just revenue you know so for restaurants i would just scrape google maps
um trying to use the vina ai loom video technique to showcase to the lead to the website yeah
vina is pretty good um i believe they also like you need to book a call with them to get a coat
okay so that's pretty much it for today's questions i believe we are at one hour
we are at one hour yeah so let me know if you guys have any more questions if not
i think uh i've ran through all the questions that we have in the chat
let me see if there are any more questions here
yeah no worries jason
okay everyone so looks like yeah just send me the video just send me the video i'm just gonna
grab some popcorn yes i will man okay guys so thanks so much for coming in today's
um i have a question in the dm can you take a look after yeah 100% man for sure
um no just if we can have some training on na10 might be a good idea in the near future
might be a good idea to just put up like a full hour course on na10
because i'm pretty good with na10 i just don't use it that often
yeah
yeah yeah sammy really loves na10 i know
all right guys so uh thanks so much for coming in today's call um thank you axel thanks joelle
thanks um jesse thanks jason um thanks erin vichal no worries man oggy and um ai agents my friend
thanks um all the people here thanks scott sam again and everyone here sorry if i forgot everyone
someone here
sven again congrats on the deal congrats zach on the deal af
and uh thank you to all the note takers that we have here thanks daffy ai agents uh thanks butter
thanks victor raj and thanks jeff good to see you man and um i'm gonna upload this recording
tonight so you guys if you want to guys want to re-watch it and uh like just grab some popcorn
and see me yap for like one hour answer your guys questions and uh yeah thanks so much for
coming today and see you guys in the next weeks peace
