<PERSON>, let me just admit everyone here
Scott
Approve everyone here again
We have a very like uh, we have really good questions actually here, especially care guys questions
amazing questions man all the time I
Just read through them right now actually so we're gonna you're gonna see like my live
reaction of that of me answering these questions I
Know where is man noise your questions are always um
They're always value-packed and I like your decision-making abilities
Yeah, I see myself in you
Exactly what's up?
How you guys doing
Just wait
Just wait for everyone to come in and then we'll just dive in into everything the
Past few recordings were high quality because what I'm doing now. I'm just using OBS instead of using the shitty
Fathoms recording right so it's like way better
It's kind of like when I'm recording a YouTube video and I just plug that into euphonic if you guys wondering what euphonic is
It's basically an AI that just
Removes all the background noises. It just makes your voice a little bit clear
Yeah
It's way better now
Okay, so no issue so far when it comes to the in-house tools, right
Looks like everyone is using them
and
What's crazy is that?
The morning when I was recording a video I
Tried yeah, I tried the an email finder and
I actually couldn't get like I tried in ten leaves
I only got like three emails, and I tried the
command center I actually got eight
So looks like we've done a pretty good job
Jesse right, I believe AI agents is it
You guys's names are killing me AI agents
Sam
All right guys make sure to
Give <PERSON> his flowers because he's the core developer of the whole thing
Flowers because he's the core developer obviously
He was the one who actually coded the entire thing
All right, so
looks like
I'm just gonna keep admitting you all as you guys are come in
So I'm just gonna like share my screen and then I'll just answer in all you guys's questions
so I'm gonna share now and then let me know if
Everything is good
So entire screen and share
Okay
So you guys can see everything the screen perfect the voice
Just to give you some context what he like he basically built an
email finder
That also has the components of validating your emails, and he also built
The ESP provider right so the top process is basically
If you run through a list that you just scraped you know
You can just essentially just retrieve is it like a Google or Microsoft so if you bought like
Emails that are 100% of Microsoft or 100% you know Google you can just have a campaign
You know where you have all your leads that come in from Microsoft and then just send
Yeah, and then just send through your Microsoft accounts and the other lists can just send through your Google accounts
You know and he also built like a company
LinkedIn company scraper, I mean the guy is like crazy man
The guy is like you know
Very very talented you know
Yeah
All right, so do you guys see the screen here with the in the school tab?
Okay, perfect
All right, so thumbs up, we're just gonna get started okay sounds good
All right, so let me just
Mute everyone
I
Okay
Perfect okay, so the first question we have
Derek have you experimented with instantly's AI prompt looks like it may be a
Great way to create the personalization directly within the platform and save operations on make
Yeah, man, I've definitely tried that but the results are pretty shitty
I'm not gonna lie they use a cheap model what they use is GPT 3.5
Which is like the lowest and the cheapest so I mean if you if you really want to cut down on costs you can
Do it but my recommendation would just straight up just using instantly just using your make calm
Even though it's gonna take a little bit of operations, but keep in mind like the quality of outreach is like
You know 50% of the entire copy, you know 50% is the offer along with the risk reversal etc
And the personalization, you know a clear value prop is the other 50%
So I think you know, it's like losing the force for the trees, you know, so I wouldn't recommend using instantly's AI prompts
Even if like I tried to like give it a couple of instructions, but the quality is not the same
You know when you use like a cheap model
You need to give it a bunch of examples like three to four examples and
Still, you know, the results are not the best. It still sounds like AI and you got to realize like
If you really want to cut through the noise and actually like book meetings
You really need to use like a personalization that sounds like human like 101 which is exactly what I would recommend
I wouldn't recommend you some shitty personalization. It's because I cringe when I see people use this
Sorry, I saw your LinkedIn I received that all the time in my emails and I also received that in my LinkedIn
People would just send me I saw your latest post about high ticket sales system
And it just feels like it's AI, you know
like I would rather they just send me like a just a
Generic message other than like a shitty personalization and sometimes they send me a really good personalization
I know it's a I but it still doesn't sound like AI just because I've like a
I'm all day every single day
You know prompt engineering like I can tell when it's AI even though if it's it's not AI, right?
So I wouldn't recommend using sleaze AI prompts just stick to Claude, you know the best the best best best best AI model to
Generating text space outputs is gonna be Claude 3.7. Senates. It's the best, you know
If you really want to cut down on cost just use Gemini Gemini is pretty good, too
It's like virtually like like it's extremely extremely cheap. It's like
0.005
Dollars for like 1 million tokens. I have so you can just stick to Gemini and I wouldn't recommend using instantly as a prompt. Yeah
The next question is can you showcase a cell system? You will build using new
Cell systems mastery tools with epiphy lists to email finder verifier and XA for personalization or however you deem it you deem fit for
Max results. Yeah, that's a really good question, man
I'm actually gonna record this video tomorrow for you
I'm gonna tag you and I'm just gonna upload it and what I'm gonna do is I'm gonna build a list using XA
along with other platforms maybe like a crunch base or
LinkedIn or etc and then I'm just gonna use every single thing just from our email finder, right?
I'm gonna do it. Yeah, and I'll tag you so rest assured
Can you show us the system you use on make.com in your business for getting leads to instantly and what others do you use?
Yeah, man
So this isn't that I'm currently using is the one that I record a video about
It was basically two days ago and is extremely extremely powerful. It's the company's hiring for sales, right?
so I
Believe Kai is also
Scraping companies hiring for sales, right? So what I would recommend here's the the last one that I'm using
Let me just walk you through it
This is the one
It's
Yeah, this is the one. This is the step three and this is step one and I believe
This is part one, yeah
So essentially what I what I've built is a I just
Input a search URL from a LinkedIn job scraper, right?
And then I just send it through a type form and then I run an actor once that actor runs, right?
It catches the responses here
It catches the responses and there's a router
So if you scrape LinkedIn jobs, right if you use like the advanced LinkedIn job scraper or you use the paper results one
There's like a multiple you're gonna you're gonna find that multiple job postings don't actually include the hiring manager or the recruiter
So I just have a flow here that just makes a request to the AXA API and
Then it just finds hiring manager at company name, right using their LinkedIn profile
Using their LinkedIn profile because it's included in the job posting
Okay, and then I just use AI to retrieve the full name from the data
and then I just feed it to either an email finder or like
you know any email enrichment platform you want and
Essentially, I just get the first and last name and I again I send it back
I send it to another flow in make right just because I want it to be extremely scalable and consistent
right
so I send the email found the job title the company name etc, etc and I find and I send it to the last flow so
The last flow just catches a web hook
parses the responses and then it's just basically
Cleans the job titles. Okay, just because I don't want business development representative just like here you see
Junior software engineer and I just clean the company the the job titles and then I perform a search by
Basically scraping their website, which is website here and then I generate a high quality personalization
Okay using the text and again, I just have my dream email and
That's pretty much it
Okay, and then I just send it to instantly and if you're wondering what kind of scraper I use
So this is the one I use it's a paper results. This is the one that I would recommend
Kai to use instead of using the advanced one just because the advanced one you're gonna use your cookies
This one is amazing. Actually
If you sign up with a free
Apify account, they're gonna give you five dollars. You can scrape 5k
Right just off a free account just because it's a paper results, right you go here you see a
Thousand results per $1
So this one is really really good, right and I believe they also remove the duplicates for you
So I think you're gonna mention that you mentioned that new question. So they delete the
The duplicates for you, right by this guy, right? It's called curious coder. All right, and also it doesn't use us your
Cookies it owns it like it uses in our own cookies. All right
So this is the one that I'm using currently. I'm just scraping companies hiring for sales
I'm not doing it that much because I already am bottlenecked with many clients. So it's like a
It's very hard for me to fill out nowadays just because I have so many clients and it's just I just can't take in anymore
So I'm keeping my pipeline quick quite lean, right?
So this is like essentially what I'm doing right now
Yeah, man. So this is like essentially what I would recommend
Currently. Yeah, if you really want to scrape companies hiring for sales
I know many people in the community actually are doing this. They're scraping companies hiring for sales and they're high intent, right?
So that's kind of like my answer to your question
Okay, so
my first
Email campaign in a few days coming into an end. I got a lot of negative replies
Also, I sent out emails to 3.6 K elites one sales call but had I had an idea
I thought I will scrape leaves a bit different now
I want to scrape LinkedIn posts on SDR jobs and the company details and just run a campaign from them
Yes, but I don't really know how to build out it
I just talked about it, but I don't really know how to build out a forces full-on system with personalization and company cleanup
Also, I don't really know if I should create my own prompt for it. I'm really clueless right now
Also, some people said that the recruitment niche is really saturated right now
So I don't even know if I should stick to it
man, that's like
You know, everyone is gonna talk about every niche is saturated, right? So
My recommendation here. Yeah, it's a good idea to scrape LinkedIn job for SDR LinkedIn posts for SDR jobs
Essentially you would use the same system
Okay
and if you really don't want to like nerd out about this and you really want to just get up and run in as
Quickly as possible, right? So you would just go to LinkedIn jobs. I'm just gonna do it right in front of you
So LinkedIn jobs, I'm just gonna use my own LinkedIn
okay, and
you would go to jobs right here and
Then
Wait till this to load looks like I'm when I'm sharing my screen and and zoom is just
engineering just start acting up
Well, maybe LinkedIn is uh
Okay, so we're gonna go to the title and
Then you just type in sales representative
Okay, and you have already 4,000 results. So these are all companies hiring for sales. Okay
So my recommendation is not do not filter at all
Just scrape these four thousands because if you think about it after you enrich those leads that's it
Be left out maybe like just on top of my head like
2500 max maybe depends on like how many emails you can find you're in is very very highly relevant and then you just copy this
right and you need to go back and
Then just paste that in here. Make sure you always
Toggle scrape company details and just leave this empty
Okay, it's just because you want to scrape all these four thousands go to run options
Make sure it's always no timeout and sometimes you're gonna get like
The sometimes the scraper is gonna stop. Okay, so make sure you resurrect all the time and then click save and start
okay, and
Essentially with a free epiphy account you can scrape all the way to 5,000 leads just because 1,000
Leads equals to one dollar. Okay, and the cool part is that the scraper just used its own cookies
So you don't have to worry about your own LinkedIn
Accounts getting bans etc. Etc
So the cool part is you're also gonna find the hiring manager sometimes not all the time but just use there you go
So you're gonna find sometimes job post your name. Sometimes you're not gonna find it. It's undefined
Okay, so I would recommend just use the flow and I'm just gonna I'm just gonna tag you in this
Thread and maybe like I record a quick loom for you
Just give you like a little walkthrough right and then you just use XR to enrich the hiring manager
Okay, and the data is pretty accurate, right? It's tested
If you really don't want to go for a comment
because I believe you hate recruitment just because in the last call you told me that
You just don't wanna you just don't want to target recruitment. Yeah, man
You can like you can just discard that if you really don't want to do it, you know
So trust your gut feeling was sort of recommend so don't don't go for a commit
just go for companies hiring for sales as the ours doesn't have to be just sales representative, obviously you can just go to
LinkedIn and
you can just type in marketing manager or
marketing specialist marketing manager is still a
Big signal that they're expending, you know
So yeah, that's what I would do marketing manager marketing specialists
You would compile leads from here and you'll also use Exxon
okay, so you compile less of Exxon of companies hiring sales and make sure to include not recruitment company because obviously you do not
Want to deal with recruitment companies, you know, so just make sure you do that in Exxon
Okay, and let me just walk you through how you can do it. So go to Exxon and go to websites
Okay, and then just type in companies hiring for sales
In the USA for example not recruitment and
Employees what I would recommend
We want to we want to talk to a 1 to 250 for example, yeah, so once you do that
Exxon is gonna give you the prompt right so hiring for sales positions not a recruitment company
Okay, and make sure you scrape about 1000. Okay, it's gonna take you like 20 to 30 minutes
So you'd compile leads from here right and then you would also scrape like I said
LinkedIn jobs and if you really want to build another list you can go to Apollo
Okay, go to Apollo login
And
Essentially, I believe this is the accounts. I'm not really sure
Have no idea
Hmm
Okay, so you just go to Apollo and then you would essentially just toggle hiring
Okay, go to hiring and then just type in sales
Okay, just because Apollo is a keyboard search system
Just type in sales don't type in sales representative because you might you'd miss out in a couple nuggets
Okay, just type in sales and it's gonna retrieve all the job postings in the sales positions
Okay, so you can build like a pretty big list using XR LinkedIn Apollo
Yeah, this is what I would do
Again, if you really don't want to target recruitment, you can just discard now
Even though recruitment is like one of the best niches man
Okay, so max I made my ideal profile ideal client profile for recruitment agencies to help me tailor my messaging as I learned more
About the industry. Could you please review the documents?
I made so I can enhance the my resolution in there what prevents recruitment firms from falling asleep at night
Okay, and let me just do it now, man
Okay, I like the presentation
Yeah, okay, I legally vow to add five new clients to your company in 60 days without prooflessly
And
Legally blind guarantee if you don't onboard five clients, we're fond all your money
Okay, assuming ticket ticket price is 3k at 30% of close rates
No show rates is 20% a good price is 10% close value per book meeting. Yeah, that makes sense
I need to book 18 calls for five clients to be closed. I like how
You're using a mental model called
system thinking you know as you basically
You take a big idea and you just basically chunk it and
You reverse engineer from
From the end in you know, so five times 315 K. Okay 36% of their revenue
If they close on board more than guarantee will be compensated for our contribution of those 36% of the revenue
Yeah, that makes perfect sense. Yeah, so good job on that man
Okay, so recruitment and staffing agency are under pressure to rapidly fill roles with quality candidates while maintaining strong relationships with client companies
Problems agencies waste hours manually reaching out to both candidates and clients making it hard to scale outreach efforts. Yes
Generic non personalized outreach results is an unpredictable flow of quality candidates high competition
So what I would recommend just reading through this
this is I
Wouldn't recommend you basically
Mention this in your copy just because when I'm working with our recruitment agencies
They're like they're not as tech-savvy. They're used to talking to people and sales calls etc. They're more like, you know
They're more casual etc. See they don't really they really don't like, you know
Abstract ideas when you when you try to explain these automated systems
Okay, so always frame it as the end result, which is presenting their ideal candidates in front of their
Ideal companies are currently hiring for those candidates. Okay
The gap holding them back solution. Yeah, okay. I
Like how you framed this man
We built fully automated cold outreach systems for recruitment firms specifically designed to represent you
Yeah, this is the clear value prop your candidates and connect you with targeting companies actively looking to hire
So this is the clear value proposition. Yeah, this is the real juice here
Offer a performance-based guarantee example no results no fee or a trial period to reduce the perceived risk or of adopting your system
Okay integration without disruption. Yeah, it is huge. It's a really good juice
Yeah, so if you can basically integrate that with their their own existence CRM
As it's very easy for it's very hard for them to say no, okay
Okay, so you've also included the mindset shift yeah, this huge man I would really recommend you mention this in the
Sales call because you want to sell a outcome. You do not want to sell aces
Okay, always make sure you frame it as an outcome. Okay, whenever you were trying to sell something
Emotional triggers current states. Yeah desired states
Yes, that's that's really good man. If you really can leverage this in the sales call, right?
You would crush it
You'd skyrocket your closing rates
Yeah, I can see you put a lot of energy and effort into this man, yeah
So the next step man now since you have already like you have
Everything like put up together. The next step is basically this is a great offer
I'm curious how much you really want to like a price this. Okay, so I'm getting a price 2,700 and
The second 50% off upon book in okay. Yeah, that makes sense
He saw does this offer and numbers look like a great deal for recruitment agencies, yeah
Yeah, this is a perfect offer 100% yeah, I would I would this is something I would I would personally run. Yeah
Yeah, so the next step man is just pre-draft a copy and just post it and make sure you highlight the
This one right here
presenting their strong candidates and connect them with targeted companies actively look into higher and
Just make sure you don't mention the no results. No fee in the copy
Because you don't want to trigger the loss of aversion which means if you really mention a refund
right
elite or
a client will start thinking about refunds, you know, so it's kind of like a
Psychological aspects of human nature when you mention failing like a failure
they essentially just
Our brain is like just match patterns
Our brain is like just match patterns that and to a potential failure in the in the future
So don't mention that just say purely off-performance. Okay, just because you don't want to trigger that aspects in their psyche, you know
So this is my recommendation man great work, okay
Do I okay amen said do I need to buy domains?
Apart from my main domain if I'm using pre warmed up inbox and boxes from puzzle inbox for instance
Do I need to set up masking proxies from my main domain if I'm using for the email inboxes?
What's the kiss? What's the risk cost ratio? They're like
Do we need to buy domains apart from your main domain if you're using pre warmed up inbox and puzzle inbox?
You're pretty fine unless you want to increase your ascendant volume. Then I would recommend getting main domains, but
You're pretty fine if you're just getting started for now because I believe this is your first campaign
So just use the pre on the inboxes get some clients first and then you can if you really want to like up the volume
Then you can essentially just buy domains continue sending and let the new bot domains just warm up for like the 1421 days
Okay, do you need to set up masking proxies for the main domain? You don't we do not need to do that
The only time we're gonna need to mask proxies if you're using more than three email inboxes per domain
Okay, my recommendation just stick to two email inboxes per domain, right? Don't don't don't go for three. Just go to okay
Two email inboxes and you do not need to mask proxies for now, right? If you're using 40 email inboxes
Two email inboxes per domain. That's more than enough. Okay, so you do not need to sweat this out
Okay, so our friend Kai with the amazing questions
Hi, Scott. Thanks again for the free in-house tools and the AXA AI discount code recently of our community. No problem, man
Quick context. I'm really I'm running a cell system for a crewman company in the Philippines
Okay, interesting. How are a few questions that love your thoughts on limited leads issue
I scraped 17,000 job posts took three or four days only in jobs
But only ended up with 900 leads uploaded instantly with personalization, of course, and not all job posts has been recruit
Has the recruiter information? Yeah. Yeah, I told you man when you scrape those jobs
Sometimes the recruiter or the hiring manager might not explicitly just mention their name, you know
So we use accept for that. Okay use the system here, but I just showed you that enriches the
Decision maker right so you would get the data set item for example, so it's great
If you really don't want to use the entire system, just put your basically the the data set ID here
Okay, and then just map your connection and just make sure
To add hiring manager right at company name and I believe let me just try one and see
So
Let's unlink I
Believe this one has yeah, let's just try the first offsets offset the first one
And I believe we're gonna find D
Okay, so this is the one there you go
If an X I just went ahead
Is it this one?
Not really sure
Company company company name, let's just map the right data
Yeah, so name
Okay, I
Believe we're gonna
Let's see
So results
Okay, so there you go, so
This is the title and it's hiring manager at zero and the cool part is you also feed in
The LinkedIn so XI reach through the data and sees okay. Here's their LinkedIn page
now I'm gonna match pattern that and
You just feed into AI
Basically, and then they just feed it to an email finder. Okay, for example
And then you just run once
And I believe I need to yeah, I need to revoke access
Yeah, I need to revoke access so let me just do it
So you can just see it
And here's my API key
And here's my API key
And click on save okay, okay, let's just try I
Believe any mill finders gonna find it
Yeah
Yeah, so any mill finder couldn't find this but I believe if you just
Go to use all found emails is gonna find it
So the cool part of that email finder too is just it only gives you the emails that were are found and also valid
Or maybe any mill finders just sucks and ours in our in-house tools that are better
Let me see
Yeah, so it couldn't find it so it looks like uh, yeah looks like any mill finders just couldn't find this
Maybe we can just use our own one
Just keep in mind that's not only not all the leads that you're gonna scrape
We're gonna find the emails. Sometimes the catch-all it's virtually impossible to find the catch-all emails. Okay, so we can just use our own
Okay, just use the
The cell systems mastery command center and I find better results actually using it
other than hunter and
Find the email also any mill finder. Okay, so Sam did a pretty good job, you know
Okay, so what I would recommend
here is
Not only don't just don't like don't scrape just LinkedIn jobs you can also scrape well found
Right. So well found also if you go to well found
Essentially just it's a also a job board in websites
right and
The way you're gonna scrape well found you're gonna scrape it's using this scraper right? It's called well found premium scraper. Okay
This one right here you'd also scrape dice I believe it's dice. Yeah
Yeah, it also you use dice so dice well found LinkedIn and you'd also scrape
Indeed, right? So make sure to diversify your lead scraping platforms
Do not be reliant do not rely on one single platform. Okay, make sure to use like a couple platforms to build your list
Okay, and also use AXA make sure to not include the recruitment agencies. Okay, and
This one right here that I showed you the paper results
This one
The paper results LinkedIn job scraper. Actually, they remove duplicates for you. So that should that should help
Okay
Okay, so
Given your intensive sending schedule. I'll finish the campaign under two weeks and I already started the campaign today
Yes, would you keep working with this client despite the low lead volumes or focus only on markets like the US where Legion is easier
What we're doing this what do you do in this situation? Yeah, man, it's tough because in the Philippines
You're not gonna find like a lot of leads, you know, there's just how it is, you know, because you're just basically it's just one country
So my recommendation what I would do is squeeze as much juice for this client, you know, and then
You would just
Work with markets in the US, you know, just because it's easier. Yeah, so make sure just make sure to squeeze as much juice from this
client
but in long term
It's gonna get tough to find leads, you know
It is because it's just one country, you know, you're limiting yourself to just the Philippines
So yeah, I wouldn't I wouldn't just stick to just one country. I would just expand to the entire US
Okay, because if you really want to scale you we need you really need to do it, you know
As we reach out to recruiters usually one recruiter posts multiple job openings for different roles
But instantly only allows one email per contact to avoid duplicates. Yes, that's true
I was just leaving money on the table by not reaching out to them about the other roles. They're hiring for actually
No, man, what I would recommend is just
Make the make your copy a little bit vague. So instead of saying hey, I saw you're hiring for an SDR
You can just say hiring for sales. Okay, just because some recruiters would be hiring for like an SDR BDR
Etc. Just say hiring for sales. Okay
I believe you just mentioned that
Yeah, is there a way to send just one email that mentions all the two to three roles are hiring for yeah
This is what I recommend just mention sales. Okay
It's way better that way. Okay, just because gonna remove all that headaches for you. Okay, and
That cool parts is that
Even like sometimes you would say I saw you hiring for an SDR, right?
Sometimes they wouldn't be interested right so you want to
You don't want to shoot yourself on the foot
So if you my thought process here is you want to like make sure to say sales, right?
Just because you want to include all the job titles because sometimes let's say they post a job about hiring for an SDR
Maybe they place that candidates. Okay, you never know. So if you say sales
Potentially other candidates that they have right
Okay
Might be already in-house ready, you know
Advanced LinkedIn job scraper issue. I'm using the advanced LinkedIn job scraper, but it only captures partial results
Example LinkedIn jobs shows 800 job posts
But the scraper only scrape only 400 and I said is successful when I click resurrect a scrape 1000
The many are duplicates when I check with Google Sheets. Yeah, you just use the paper results, man
Yeah, this one like doesn't include duplicates this is the one that I would recommend okay and
Thinking just we can just build our own
Perfect LinkedIn job scraper which may or may not we're currently building
I'm not using a VPN using brave plus Google any idea how to fix this or what's causing it?
Yeah, so this is what I would recommend that just use the paper results man
And then scrape also XO scrape dice scrape well found and also use Apollo filter
Hiring and there's just type in sales. Okay, and make sure just build as as many leads as you can. Okay
Booking calls once a lead agrees to meet do you usually send them the booking link? Never man. Never send them a booking link
Or do you book the time for them or just ask for confirmation?
So there's really two ways my recommendation is that people get you know
They there's kind of like this ego thing where they don't like when you send them a book like a booking link
What I would recommend it just suggests a time. Okay, so what I would do is say
Hey, thanks for getting back to me first name. I'd love to set up a time to talk
I'm available on X or Y which day works better for you. Okay
that way you're still nurturing them and you're kind of like you're still thoughtful and mindful of their time and
They really appreciate it and you have the highest leverage at that point, you know
So because they're already replied they're interested
So it's better to not just send them a book late like a booking link, you know
Usually when someone just sends me a booking link, I I really don't like it. But when they suggest a time
I'm more willing to hop on a call, you know
so just suggest the time and
Don't send them a booking and booking link right off the bat. Okay, it's way better because I want you to maximize the results
I want you to squeeze as much juice because you have the highest leverage when they are interested. Okay
Yeah, I noticed when they send the link they just disappear. I told you yes
So just suggest a time. My best recommendation is just saw basically
Suggest a time
Okay, so I hope this helps man. Let me know in the chat
Okay
Okay, perfect
Okay, I'm just gonna answer you guys a question in the chat and I'm gonna answer a debate question
From Jason. Okay, it's a big question. Alright, so let me read through this. I keep getting rate limited with Gemini
Yeah, I think it because you have a lower tier API
Why two flows
Okay, so I believe you understand
Okay, Kai said I created more than 10 API accounts yesterday and I get flagged. Yeah
If you really will don't want to get flagged. Here's a here's a hack. Let me just show you it's a little hack
Specifically for salesman's mastery you need to get this extension. It's called
Web share proxy extension. It's it's free web share
Proxy and
Just sign up they're gonna give you ten proxies, you know for free and if you click on it
Just click connect and you're good
This is how you scrape without getting a rate limited and it's like it's free just sign up with Google and you're pretty good
to go
Okay
I didn't actually it was AI
Yeah, yeah, do you think it works for international company as we need to find the
Recruiter in the Philippine not the recruiter in France or something. Yeah, yeah, it works. Yes. Yes
The recruitment companies tend to focus on a niche of hiring roles still new to the the niche
Yeah, it depends man because there's like multiple sub niches in the recruitment niche, right? There's companies. Okay. Here's another hack guys
Reach out to
Recruitment companies that are looking to
Hide at the top to place AI roles
Trust me
just because
Every single day there's new startups AI startups, especially in us. I believe it's like 600 every single day and
The volume just increases, you know
Yeah, so recruitment companies that specialize and
basically
recruiting candidates such as
AI developers
Prompt engineers
You know
How do you avoid having multiple people be interested in the same time slot if you're suggesting times and there's lag in them coming
Back to your am I missing something?
Yeah, well, I mean at first you you want to talk to everyone right so you really want to make time for every lead
You know
But um, it really depends. So, you know in my case now
I have SDRs that take in sales goals, you know
But when I first got started I would talk to everyone, you know
Like I would have like a meet like a seven to eight meetings during the day
Like you you want to talk to everyone?
And make sure to fix your delivery your cal.com availability
Okay, just make sure
Everything is fixed and I have that in the I think it's the day eight
Building out your fixing your cal.com
availability, right
How do you know they recruit for AI you just find the recruitment companies
That recruit for a high you can just scrape recruitment companies and the AI industry
Right and the way you're gonna do it crunch base
Because there's a filter called AI artificial intelligence intelligence, yeah
you can also use accept with that and
You can also use here's a hack to go to Apollo
Go to the industry go to the recruiting and staffing industry and just go to keywords and put AI that's it
Yeah, but I if I offer 4 p.m. 430
To five people what happens if two of them want to hop on the same hour?
Well, it depends on your calendar man, right?
So for example, like if someone suggests I don't think you're gonna get this this issue
and I've never had this issue and
Even if it happens you can see you can slot them out in like a you know, like a later maybe
But I'm curious how can you get this issue I've never had this
Okay
Yeah, yeah exactly some people don't show you can just rescale it the word yeah
So it's possible to get outsource this old school. Yes. Yeah, I
Will start my first campaign for cyber security tomorrow and it's just in targeting the niche
yeah, they're very tech savvy go hard on the personalization use a founder to founder tone and
You can explain just a little bit your offer and the in the initial email, right to emphasize in the AI
Because they're really they're they're really interested in in the AI niche, you know and
Don't be afraid to pull off a whimsical or
presentation
Five to ten minutes explaining the system in detail. They would appreciate it just because there are tech savvy. Okay
I
Believe you missed this question. She let me just read through this context you are you suggesting we book in we
Book in a meeting for a lead instead of sending them a book in link
Oh, so you okay, so what you mentioned is that if you send them a book in me like a link
You wouldn't have to basically qualify them, right?
That's what I this is what I believe I believe you talk about qualification
So, please I would like to know how to outsource the sales call. Yeah, you would hire someone off Fiverr or up work and
I know how to do it or you can just outsource off indeed
You
Okay, so after we agree to meet at 1 a.m. On
Tuesday we send them the link to book or book it for them. It's highly dependent man
So there's not really it's not either or you know, it depends on the lead. My recommendation is just suggest a time
Okay, just to be safe
Okay
To be safe just suggest a time. Okay, don't send them a book and link. Okay
Sometimes a lead would say
If you like suggest the time there'll be like, okay
Can you send over a book and link then you would send them like a book and link?
This is what I would recommend, you know, just because I just don't want you to miss out on leads. That's the thing
Yeah, hopefully this makes sense
Okay
Um, yes, perfect. All right
Yeah, Sam, I can just hook you up with a couple I
Really? I know you hate sales calls. I know
Yeah, I'll give you like a recommendation what kind of is the ours to go for yeah
Sam is kind of like the builder. He's like the technical
He's like the technical CTO, you know, he doesn't he doesn't like sales calls
Yeah, I understand man totally understand I
Get it
Okay, so we have I believe we have the last question from Jason so really good question
And we also have a question from Alex
Okay, I have a few calls and not managed to get the clients to sign and close the deal
I thought that I had explained the system well enough
But perhaps I had not impressed on them enough the value of the system a couple of common
Objections I get are been burnt before yeah, that's common. I already have people scraping job boards, but how are you different?
That's a lot permitting if you divide the
1850 by five book meetings, I'd appreciate maybe a sales call recording. Yes work on that
I have looked at Vishal stuff and used his questions, but there must be something I'm missing
I understand this is vague, but I'm but a bit of direction for the calls would be great
I'll do my best to be there, but I have guests over. I'll watch it later on. Yeah, no ways, man
Yeah, so when elite says
I've been birthed burnt before typically it's
It's a it's a
There they're saying that because of two things one they're really been burnt before and two they're just trying to
Basically test you okay
So you at this point you just the best way is just to highlight the value
Okay, highlight the value and you'd mention past clients. Okay, so in your case in your specific case
I know you've worked with multiple clients before so what you would do would just highlight the like a
Past clients and here's what I would do
Here's a hack again. There's a lot of hacks in this recording
What I do is this essentially what I've done with my first
Sales call as I said
You're essentially in the meeting right using the same methods
That I'm in that I'm gonna implement to get you meetings, right
Just say that in the sales call, okay
Can highlight that could say hey the same way
We're gonna the same way. I got you in a meeting today in our sales call is the same way
I'm gonna get you in front of the decision makers. Okay, and
In fact, I have a couple more meetings after our call
So rest assured the value that you're gonna get invest in in our sales system is gonna be highly justified. Okay mention that
The second one is I already have people scraping job boards, how are you different? Yeah, the thing it does is this pretty easy
You can just say
Yeah, I
Understand that's you have multiple people scraping job boards, but
Are they using the same level of personalization that are we use that are we using and the same level of automation?
That we are currently using at a fraction of the cost. Okay
This is how would this is how we would frame it's okay and make sure to highlight everything in terms of ROI, okay
That's a lot per meeting if you divide
1850 by 5 to 8 meetings. Okay. This is where you showcase the the ROI calculator the Vishal one
Right. This is really a powerful man
You just mentioned that if they invest 1850
It's just if they get like the pet like depends on like let's say the onboard
From five booked meetings they onboard just one they've completely justified the entire investment. Okay
Especially for a coming if you guys don't know recruitment
They make a shit ton of money if they place just one candidate they get like
Probably like 30% of that candidate's entire
yearly income so
If you if you target AI recruitment companies that that
That are that have candidates such as AI prompt engineers AI developers
These folks get paid like over 100k a year
You know and they collect like a big commission out of that. Okay
So you take like five to ten percent of the commission deal
Okay
So, I hope this helps them
Yeah in terms of sales recording
Yeah, I hope this helps them
Yeah in terms of sales recording
Yeah, i'll i'll i'll make sure I record one
For my own
And then i'll just put it in the the community and i'll dissect it even if I say something wrong i'll say
Okay, here's what I said wrong
Don't do this
Okay
Okay
Um and objecting that communicates low trust basically say anything that yes exactly
That builds trust
The growth partner line is yeah, man. This is how you differentiate yourself, you know
This is how you differentiate yourself from the glorified freelancer mindset, you know
We hate agencies too. We only yeah, we actually yeah
We actually care about our partners because we only continue to make money when you make money. Yes
Perfect, man
And make sure you always just mention
Purely off performance. Don't mention a money-back guarantee
okay
Mention performance based because there's this thing that's called laws of aversion guys
Is that when you mention a potential negative aspect of a situation?
the human mind just
basically, uh
Because the human mind doesn't differentiate between illusion and reality, right?
Okay, yeah, don't mention that just say purely off performance, okay
Um
Objection handling better don't front loader. Okay
So my typical sales call is
Um
I hop on sales call I say thanks for getting thanks for hopping on call with me what made you
Uh hop on a call, etc, etc
Okay, and then I just go a little overview of what the system does and then I just price
I just pitch the pricing right away
And the reason why is because
most people
are so used
to
Five minutes before the meeting
This is when they hear the pricing the big question the price
So when you front load that puppy
At the first like the first 15 minutes now you got like 30 minutes of the objective handling
No
So you can literally really get down into it?
So my approach is a bit different than vichal's but you know, there's not one there's not either or okay
There's hundreds of ways to close a meeting, you know
Yeah, so, uh, how do I price uh on a call I have basically I always so what I do is I always have
A price in my mind, okay
And I anchor the price so for example, let's say I have in mind that I want to pitch 3k I pitch 4 500
Okay
And then
If they say okay, that's too much
I make it seem like okay
I'll lower the price like a ton and 3k is like the initial price that I went and like that I had in my mind
Okay, so make sure you anchor your price all the time
This is a principle that I under that I learned from a book. It's called never split the difference by chris voss. Okay
It's just a usual sales method
When you anchor your price and what I do is I always have the contracts ready
Okay
Always have a price in mind and have your panic already ready
Okay, because ideally what I want you guys to do I want you guys to be sharp and actually close during the call
Okay
Ideally the best case scenario is closing during the call
We can still close after you send them the full of the proposal
But the ideal the ideal scenario is closing during the call, which is why I pitch the price right away
and if they say
um
Not really not on the pitch deck. I just say straight away and make sure when you say your price
Don't try to fill in the the silence say the price and literally just
If it's quiet for like five minutes, let it quiet
Because if you try to fill in the void
You're you're making it seem like you're not confident about your pricing okay, and there is really no
Universal pricing, okay
It depends on how you like how much money you believe
The deal is going to be if you can present it in a confident way
It doesn't really matter, you know
If so if you believe that pricing is really worth it you can present it in a very very confident way, you know
So I hope this helps ma'am
Best way to state the price. Yeah, obviously, um, you're gonna have to highlight everything in terms of ry
Okay. So when you showcase the ry calculator
you would ask them like
the typical
Leads a typical deal size and they tell you for example for each client
Let's say the each client they onboard they get like
five to six k
Right, you would say hey for for an investment of x, right?
1853,000 here's how much you can get?
You know
And you've completely justified your investment with us. Okay
Always make sure to transition from their benefits, okay
Don't just straight away say hey, okay, here's the pricing, you know
Transition from the value
to the price because
price is
you know, it's uh
Like any price
They like whatever price you you're gonna pitch
Is going to be directly correlated with the value provided, you know
I see some people sell sale systems for a client three to five months. Yeah. Yeah
After they cannot scrape new leads and they find new clients. Um
Uh, typically
my clients
Um, okay. So let me give you guys like um, like a real
truth, so for me
When I first got started the average
Ltv of each client was three months. Okay, and then after that they churned so
The the ones that actually churned were the ones where
They just uh, you know, they weren't patient enough with the process
Okay, so what I mean by that, let me just admit
Uh, what I mean by that is
It takes a little bit of time for them to find the cold email formula that works for them. Okay
So typically it's like three months. Yeah
But um
What I would recommend you guys do once you get to enter a certain level of pipeline you can sign clients up to six months
Okay
This is what I recommend
Don't worry about this now
Typically the ltv is going to be
That's right, like three to four months, you know
Yeah, yeah, no worries man, I believe I missed max's question
If pricing in each appointment booked is 10 of close value
Is that how you say the price yeah, yeah exactly so you would say basically, um, so for example
Let's say you close a deal. Let's say we get you five booked meetings and you close
let's say you're shitty at closing you just close one, right and let's say you're
um, like they once they onboard a client they get like
10k, okay
Because these b2b companies like they like the amount of onboarding is crazy. Okay, especially if you work with like a high ticket
Uh company you would say hey if you just close one
Right, you've already like and you pitch like 2k. Let's say you pitch 2k. You've already asked like 5x ry
right
So make sure always come from the the ry calculator and then transition into your pricing because it makes sense that way. Okay
Um, yeah, so hopefully this helps let me know if you guys have any more questions before we end
And
And I believe that's pretty much it and by the way axel, um
Well, we're gonna talk about your uh client fulfillment later. So don't don't worry, man
um, i'll make sure to give you the
The best juice as possible
Yeah, no worries kai
So hopefully you found you guys found some value in this video in this video
I record contents every single day
Hopefully you guys found value in this uh weekly call
Yeah, no worries patrick
So I believe these are all the questions though, oh, yeah, I forgot alex
Um, yeah, let me just answer this question because I want to make sure all you guys questions are getting answered
I do lead magnus on linkedin got some people interested in the automation. I have a call on thursday with a client
He works with accounting services and the offers are to manage ar ap payroll, etc
Yes will be the best ap5 scrapers to find new job listings like accountants payable and how?
Uh, would you proceed?
Yeah, that's interesting
Also, the problem with linkedin is that even though I can book meetings and get people interested in my offer
I can't really niche down on something. My main concern is of course
Getting my first pin clients perfect. Yes smart for now so I can scale
Do you think I should focus on getting clients from linkedin since it's my main source and I already have a following
Or just stick to email campaign. So in your case, man, you have already a following
So why not like why not just use both? Okay?
Use both use called emails, right and then still use linkedin, you know
Don't stick to just one
Because you already have a following
And I wouldn't recommend niching down now
Because I want you to talk to everyone, you know
I want you to get as much cash as possible and then you can worry about niching down, you know
it's kind of like uh, it's it's the same as
Driving a car at night and trying to look over your shoulder. You're gonna crash at night, you know
You're just gonna crash. So make sure you just focus on you what you have now
Right
And then don't worry about niching down just yet, you know, you want to talk to everyone
No, okay
and in terms of uh
Scraping accountants, I believe there's an epiphy scraper for that
um on top of my head
I don't uh
I don't have like like a scraper like just ready, but
Maybe i'll just do some research research and just send over something that makes sense
Obviously, I have to like do a couple of test integrations for you
So kind of so i'm sure that this is gonna work obviously i'll just send uh send it over to you
So i'm just gonna add this to my to-do list and just uh dm you, okay?
Um
Why
This guy man, just kidding in case your heart dropped. Yeah, I was like what
Uh
Feel free
Yeah happy after
Took me 10 seconds to realize like, uh, my brain went silent. I was like what
Okay, guys, so that's pretty much it I believe all the questions are being answered let me just uh
Um
Yeah, that's pretty much it I believe okay
Okay. Yeah. So thanks so much for coming in today's call. Hopefully you found value in it
And uh, yeah, that's uh, pretty much it. Thank you all for your time
Thank you kai, thanks max, um, thanks, um patrick thanks, um
Deon
Let me just uh, thanks sam
Uh harpreet's hopefully I didn't butcher your name roger
And uh, who else yeah axel
And uh zach ai asians
Scots
Perfect
Scots
All right guys, so
Thanks so much for coming and uh, cheers guys. I'll see you guys in the next weekly call. Okay?
