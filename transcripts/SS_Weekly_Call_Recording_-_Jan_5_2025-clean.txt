Hey <PERSON>, hope you're doing well, man.
What's up?
Let's wait for everyone to come in like always.
Hey <PERSON>.
Let's wait for everyone to come in.
Looks like people are just getting into the meeting.
What's up, man?
Good to see you here.
The win, great man, thank you.
How's the campaign going, <PERSON>?
Hey <PERSON>.
Hey <PERSON>, how are you?
Happy New Year to everyone.
Sorry I'm late.
Happy New Year, man.
Hey <PERSON>.
What's up everyone, hope you had a great weekend and you guys are having a great week ahead.
So let's just go ahead and start answering all these questions.
I'm just going to share my screen and then we're going to get started.
All right, so let's get started with the first question with our friend <PERSON><PERSON>.
So you had mentioned I need to email 5,000 leads for industry to get replies.
But I'm having a hard time finding enough leads to hit that.
I'm using Apollo, but I can't get to 5,000 leads per industry on emailing.
I'm also getting replies saying we get 20 emails like this per email per week offering the same services.
How do I address that?
Now I've already sent you a DM before, but I'm just going to re-answer your question.
So you can't find 5,000 leads per industry?
Well, I think there is a problem with the filters that you're using in Apollo.
And if you can't get 5,000 leads, I would use the other platforms like LinkedIn Sales Nav.
I would use LinkedIn Sales Nav along with <PERSON><PERSON> Finder.
And yeah, I would get 5,000 leads using both of these platforms.
It's impossible not to find 5,000 leads.
Like you told me, you were messing up with the filters using Apollo.
So yeah, that's the issue.
Another thing is you're getting replies such as we get 20 emails like this per week.
It's definitely your offer.
So please just post your offer in the community and then everyone is going to like roast it.
So yeah.
Another thing is the second question we have from <PERSON>.
So with follow-ups to your initial outreach, if it gets no response, are you looking for minor personalization?
That's a great question.
What is the best copy that you found to be the highest response?
Does it differ from industry majorly?
You've also mentioned this in a video.
Just wanted further detail for a better understanding.
Okay, so I have a whimsical for here.
I can answer your question and then chunk it up and basically give you each.
I'm going to we're going to tackle this together.
So first thing is with follow-ups to your initial email outreach, if it gets no response, are you also adding minor personalization?
I wouldn't recommend you do that.
So in my campaigns, here's the exact same like there's an example campaign on our brand, right?
I don't add any personalization in the second email.
I wouldn't you do that just because I've implemented in the past and I think I answered this in the last weekly call.
You don't have to add personalization in the second email.
Just have your normal follow-up, which is going to be basically saying what you said in their first email while adding more value.
It's going to be it's either going to be like a case study or like highlighting the last main value proposition that you add in your first initial email.
Such as, hey, we built this system that generated X.
Here's a case study.
In the second email, you would really emphasize on the case study and you would push for a call, right?
And the initial email address email email is sent.
So you said, hey, would you be interested in more information?
Typically, you would wait like a day or two for the second email.
And then the second email, you're asking now for a call.
You're pushing for a call like, hey, is this worth a 10 minute chat?
What's stopping you from hopping on a 10 minute chat?
Because now you're basically the second email.
Because once they go back to the last email, they're going to be like, hey, this person actually just sent us a non-pushy email.
They're just asking if they want more information about their business.
Right?
So here's an example that I have.
So I have like my usual personalization.
I have a clear value proposition.
Hey, I am Saad, a better automation specialist.
I built a sales information that brought in X in revenue last month without spending a cent on ads.
And I think there might be opportunities where I can leverage my expertise into helping company name grow.
Would you be interested in more information?
So this is like a non-pushy CTA.
And I basically have the second email, which is, hey, first name.
Nodgen, as I know this can get lost in the shuffle, would love to help company name grow.
What's stopping you from hopping on a 10 minute call?
As you can see, there's no personalization here.
Right?
Just because I don't think it's worth the effort, time, and energy that you're going to put in personalizing the second email, it's just not worth it for me.
Right?
Like I've tried it before and I didn't get any results from it.
Right?
Like the most important thing is focused on the personalization in the first email.
Right?
The second thing is I have another split that you can copy.
I'm going to include the whimsical after the call.
So I have something like, hey, I work with companies like company name driving growth through automation and cold outbound, helping them scale fast.
And I have something like usually when I reach out to decision makers, most clients or referrals would have not consulted with plus 200 six to seven figures, six figure companies as of now.
Regardless, would you be open to hearing how I helped X generates this amount of money last month using a sales that I built?
Like I said, I'm emphasizing on the case study and I'm saying, hey, if so, would love to have a quick coffee chat so I can show you how I could do something similar or better for company name.
And I have another personalized line, which is if my emails aren't your thing, just let me know.
Like I said, this is just a generic follow up.
I wouldn't recommend you personalize the follow ups because it's just not worth it.
All right.
So what's the best copy that you can get to the highest falls?
So as always, there is no best copy just because what works depends on manufacturers guys like the offer, the value proposition, the call to action.
Like my recommendation is just use the formula outline that I included in the master class, which is what I personally use.
And we're going to go through it together.
So this is going to be teaching lessons.
You can understand how to like write better copies because there is no like there's no there's no like best copy.
Right. There's only like a few variables that we can use.
And this is like this is a filter down from so many hours, so many hours done in the last few months, basically the entire twenty twenty four.
And this is exactly what I.
Come and you guys use.
So this is the step by step formula.
So the personalization that are you that it's specifically tailored for what we are offering.
Right. We are asking a question, addressing a pinpoint or demonstrate in a clear value.
Right. So the problem that we're using that's included in the classroom.
Is what I would recommend you guys always use as a sales agency.
And the best part about it is it's highly personalized to anyone there.
It's a generic one. Right.
And everyone can use it.
And it's not going to be like the the result is always going to be different from everyone in the community, which is not something like everyone is going to be using and going to have the same results.
Right. So like let's read through this personalization.
So congrats on Arrows recent growth in the health care sectors.
I was curious about how navigating the unique IT challenges of long term care facilities strategies you found more effective for winning their trucks.
So this is what I mean by good personalization.
Guys, we're asking a question for addressing a pain point.
Right. And demonstrating clear value in one line.
Right. And then the second thing is we have a clear value proposition, which is this one.
I'm sorry.
Automation special specialist.
I build systems for X that brought in an extra venue last month without spending a cent on ads.
So we're merging the clear offer with the clear value proposition in one line.
So we're basically front loading value instead of just writing a whole entire essay, just one line.
That's all you need.
And then we have social proof, which is X.
I built a sales system for this company that brought in this amount of revenue.
Right. If you don't have case studies, that's like that's OK.
You can just say I built a system that generates X without spending a cent on ads.
You can essentially just frame it for your own company or you can create a hypothetical case study.
Because you have to understand, guys, that the systems that you're going to be building are extremely powerful.
And each client that you're going to onboard, no matter the amount they're going to pay you,
it's virtually impossible to explain to you how much these companies are spending to acquire one client.
So if you can get them one to two clients, it's like giving them gold.
So you have to understand this and you have to put this in your brain.
The systems that you guys are building and we are building this community are extremely, extremely valuable.
Just because companies spend so much money on ads and paperclip ads, basically.
And there's so many aspects of that that we help them with.
You just have to keep that in mind.
It's going to help you reframe your mind into coming in as a service provider or a growth partner that is extremely valuable to any business.
It's going to help you with your pitches. It's going to help you on your sales calls.
It's going to reframe you and give you this confidence.
So again, we have a soft CTA. We never ask for a call in the first email.
We ask them if they would like more information.
Again, this is the best way I found asking them, would you be interested in more information?
And I'm including best or thank you, whatever you want to say.
And then I'm just adding my first name last in my company name.
I also include sent from my iPhone.
OK. Do you prefer this over an email signature as the email signature decreases delivery?
Yes, I never include a signature.
I would never recommend you guys do that because, yes, it does decrease delivery.
I'll just add something like here.
First name, last name, my company name, sent from my iPhone, plain text, no HTML.
OK. How are you tracking?
What further outreach are you doing with the leads that do not respond to your emails?
I heard a call I mentioned once about a newsletter or some sort of system like this.
Yes. Do you do anything like this?
Seems sensible as you want to maximize what you want to get out of each lead
or do leads that don't give responses usually want to usually just go to waste,
get marked as not interested.
So that's a good question, man.
So typically, here's a whimsical for you.
So typically, if there's no response from a lead, want to keep following up until they until they tell you, no, I'm not interested.
You always want to keep following up.
So if there's no response, keep following up.
If they show any sense of interest or more information, then you're going to have to basically just go ahead and reply to them as quickly as possible.
If they're not interested, you keep following up, keep following up until they are not interested.
Then you're going to opt out.
You're not you're going to stop.
You know, basically just reach out to them.
Now, if there's there's like a bunch of leads that are in out of office, I would essentially what I would do just add them to a list and a Google list is going to be out of office.
And then I'll reach out to them once they are back.
So I've done this before in terms of the newsletter that does is basically you can do that, but only if you are sending at scale.
Let's say you're sending like more than 10,000 leads a month like that.
That would like that would make sense to add all of those leads that let's say they are not interested at the moment.
Add them to like a newsletter.
You can basically just send them a newsletter each week to nurture them with whatever service you are selling or whatever offer you are running that time.
Right. So let's say you are running an offer of, let's say, a paper lead or whatever offer you are using and you reach out to a bunch of leads.
And let's say from 5000 leads, there's like 200 or 300 of those people.
They're like, hey, thank you for the offer, but we're not interested in a moment.
And what you can do basically just add them to a list.
Right. And then you can reach them out every week, sending them basically lead magnets or valuable content about your offer and basically just nurturing them until they will reach out to you after.
So you can squeeze a bit of juice from that.
Maybe like out of 200, maybe like five or 10 would actually convert, which is pretty huge.
Because you remember you're selling like two to three K deals.
So that's that's essentially what she's doing, which works.
Another thing. OK. Last one.
After the leads response to their first email, this is my current default response.
Hey, allow me to provide context. I'm Jesse, founder of Echo.
I help businesses currently targeting insurance and finance connects with their ideal clients without spending a cent on that one.
I see we have identified with upcoming opportunities for business name is ICP by targeting this signal as you can isolate key decision makers exactly when they need coverage.
Result in warm leads and increase appointments.
Yeah, that's pretty good.
This approach scales across other ICP in your portfolio.
And based on our results, which with the truth financial increase their monthly meetings by X and added specified amount to their pipeline, I'm certain you'll see our white box.
That's pretty good, man. That's a really good response.
This is essentially what I've used to use before.
Before when I when I didn't have like a VA, I used to have an AI responder.
And guys, you only do this at scale, right?
If you're just getting started, not do this unless you are having so many responses and you're running like a big, big, big campaign and you have an offer that you've noticed that it's working right.
So I wouldn't recommend doing that.
Would you recommend instead of pushing for a call pitch and that I will send them a loop down the case study more and building curiosity or the cold pitch.
Here's better to save them for future responses.
Yeah, man, the loom the loom system is really, really powerful.
Yeah.
What you can do, basically, if there are a lot of sceptical or if they want more information, you can record like a two or three minute loom or go through what you do.
And it's really going to help your conversion because typically people that see those emails, they're just receiving an email that don't know who that like who that person is.
And that will really like help with curiosity and it's going to give you authority and it shows that you are a real person, not just someone to send them an email.
Right.
So that's a really good.
That's a really good tactic to use.
A few clients are using Google Sheets to track leads and manage follow ups.
They need automated personalized sequences, both onboarding communication flows.
They base reminder follow ups and integration with existing systems.
Yeah, that's huge.
What would you recommend for this sort of stuff?
I know is popular, but does something like HubSpot make more sense for all the follow up and sequences?
Yeah, I would definitely use HubSpot for this just because it's more tailored for them.
I wouldn't use ClickUp.
ClickUp is a project management tool to track in tasks and also leads.
But I wouldn't I wouldn't for this level, I wouldn't use Google Sheets.
I wouldn't use ClickUp.
I would 100% just use HubSpot.
They have like an automated sequence for every step.
So I would just use HubSpot.
I would also use there's another one.
I believe it's Salesforce, but I would only use HubSpot for this.
This is like I would never use Google Sheets for this, which is because it's going to get cluttered.
And there are other platforms that are better off than ClickUp for this sort of stuff.
So I would just use HubSpot then.
So I hope this helps.
Hey Saad, I would love to discuss the options of selling B2C.
I fully intend on using the B2B process outline,
but wanted to potentially help out a customer with some B2C lead jet.
For sure, my friend.
So I have a whimsical for you that I was taking notes 10 minutes earlier.
So here's how to get leads for B2C.
Basically, these are the main things that I would do to get leads.
So typically when you want to target B2C guys,
you have to understand that most companies that are targeting customers,
they essentially have lead lists and email addresses that are already ready
and compiled from months of working.
When you want to get those lead lists, it's very hard to find B2C leads.
It's very hard to find email addresses of people.
So the only way to get them, in my opinion,
is to build newsletters, gather email addresses.
Let's say you're a company, you're going to build a newsletter
and then you're going to offer free value to these lead lists.
They're going to sign up to come into your website, typically in some sort of inbound.
And you're going to gather all these email addresses
and you're not going to pitch them out right away.
You're going to offer some sort of value.
So this is how I would gather the email addresses.
The second method is I would scrape subreddits using APFI.
So typically you would go to Reddit and you would find a bunch of people
that are basically creating subreddits of the exact niche that you are in.
And 100% you're going to find that on Reddit.
There's subreddits of everything basically.
So you would scrape all of these leads, maybe comments,
and you would feed that to AI and retrieve the ones that are match patterns with your offer.
It's going to be a little bit advanced to do so,
but it's definitely doable using make.com and just chat.gpt.
Another thing is you would scrape Facebook pages using APFI.
So I used to do this a lot. So you'd go to Facebook pages
and you would find people that are interested in whatever service you are selling.
Typically it's going to be communities.
Another thing on top of my head is what you can do is you can scrape Facebook comments.
Let's say there's a competitor that's selling the exact service that you're selling or product.
You can just go ahead and scrape all those leads that are engaged in the comment section.
These are one lead. You have to understand.
And you can just scrape them and leverage the competitor's offer into a better offer.
You can come up with a better offer for them
and just say, hey, I noticed your comment and competitor's name actually have this.
So I used to do this. I used to run a campaign for a client
and I basically scraped LinkedIn's comments and I scraped all of the followers of that company.
And I reached out to them and I'm like, hey, I noticed you're following this company.
We actually offer X, which is better than Y.
And it's a huge leverage. But keep in mind, you cannot do this at scale
just because there are not going to be so many leads.
There are always going to be just 2,000, 3,000, depending on the company's followers.
So these are some hyper personalized campaigns that you can run.
And typically, the client is going to ask you for these campaigns.
So this is very specific.
Another thing you can do is you can offer lead magnets as free value to gather email addresses.
Like I said, these are all the ways that I would do to basically get leads for B2C.
Awesome. So I need advice on deliverability.
Running 45% open rates with mixed domain setup.
Yeah, man. The first issue that I'm seeing here is 45% of open rates.
Why are you tracking open rates?
Like you should never track open rates because it's going to mess up with your delivery.
One. And two, open rate doesn't mean anything.
Open rate doesn't mean anything.
Like the only thing you should be tracking is reply rates.
That's all. Open rates is something you should not care about at all.
So yeah, so I think that deliverability issue is because of the open rates.
So I'll just disable open rates completely.
Because how open rates work is they add like a...
Basically, they add an HTML code that retrieves the information if the email has been viewed,
which is a schema. It's called a schema there.
So I wouldn't track open rates at all.
With mixed domain setup. So 9 old plus 9 pre-warmed up.
We're splitting into two campaigns to test if all domains are the issue.
Yeah, man. 100%. 100%.
I would split the campaigns.
I would have a campaign that is pre-warmed up with nine emails.
And then the second one is going to be just emails that you personally bought to know where the issue is.
And I would disable. So here's what I would do.
So let's just split the system.
Let's just go ahead and use first possible thinking.
This is a mental model. What you can do.
First thing is disable open rates.
We're just going to have to fix that first.
And then we're going to have to split the campaigns into two.
So we're going to have the first campaign with nine email inboxes that you personally bought.
And then another campaign with emails that are pre-warmed up.
And then keep everything the same. Lead source the same.
Copy the same. And then just test that and see the issue.
And then just keep us updated.
All right. So these are the questions that we have in the thread.
Now what we have to do is basically go ahead and just through the questions that you guys have in the chat.
And make sure you post all of your questions, guys, here so I can just go over them.
So I got some positive replies.
This is a question from our lovely friend, Eduardo.
I got some positive replies today asking for more information from the first email.
What's the route to respond to get a call?
Yes. As directly for a call or send some actual information?
Yeah, like I said, the asking for information is a great thing, man.
You're going to get responses like you were going to get responses.
So the first thing I would do is I would send more information.
I would never push for a call.
I would send more information typically on the offer that you have.
So, for example, if you can, like Eduardo, if you can just paste in your initial email and what they said in the chat,
and it will show you exactly what to do.
And it's going to be like a learning lesson for everyone in the chat.
So it's live so I can like essentially tell you what I would do.
So like, but like, you know, my actionable, my actionable step would be like just sending them actual information, right?
Because if you push on a call, it's going to sound like you're just basically just fishing them and they will trust me, which is the entire points, right?
We're just nurturing them.
We're just basically bringing them into our into our space.
So I would send them an actual information, right?
And then subtly ask for a call, right?
I would say something like, hey, here's what we do.
Here's our offer.
Right.
And is this worth a chat this week?
Would you be interested in a 10 minute coffee?
Okay, so here's the copy.
Check in if you had a chance to see this quick notes for you.
I built a system generating 18 qualified leads in 30 days with zero spend on ads.
Results uncertain are certain.
No risk.
I personally get you 10 booked calls in 60 days of launch.
Are you paying nothing?
Could I send you over more information?
Well, more information relevant to best practice.
Or if you prefer.
I'm a bit awkward on a call, but if you're game, I can walk you through it.
And then hi, please.
Please send over info and pricing sheets.
Yes.
So now what I would do.
Yes.
Yes.
So I can help on a 10 minute call.
Our pricing typically ranges from 2000 to eight thousand dollars.
That is fully refundable.
Guarantee.
Remember, that is complete.
Depends on what you do.
Yes.
That's exactly what I would do.
I'd like to be effective with you.
And no things and no things like your ideal customer LTV and core offer for a win-win
situation.
So you only pay for if you get real revenue.
I recommend we hop on a 10 minute call.
I'll be brief as I'm not a high pressure salesman.
I would love to figure out exactly.
So this is a great response.
I would respond with that.
Or what you can do.
Yes.
Yes.
Yes.
Yes.
Yes.
Yes.
Yes.
Yes.
Yes.
Yes.
Yes.
Yes.
These are the pre-drafted ones.
So you can send this or you can say something like, hey, our pricing is typically based
on results.
Right.
So I would recommend we hop on a 10 minute call.
No strings attached.
I'm not a high pressure salesman.
And I'll give you a proper code.
So you can say this or you can say what you just sent over in the chat.
So both are valid.
And yeah, man, that's exactly what I used to do and what I'm still doing till this day.
So awesome, man.
OK.
So Jason asked this question.
You can start a sub-sequence inside the same campaign and then this will direct all of
our out of office and you can send them a new email in a week and then another, et cetera.
We could do the same for no response, I suppose.
Yeah.
Yeah, man.
I've never experimented with these sub-sequences.
I always had like an automation that just retrieves all these out of offices and then
adds them to a sheet.
And I can just start another campaign and say something like, hey, I reached out to
them.
I reached out to you.
Basically, let's say like whatever period we had, like two or three weeks.
Does this still work?
OK.
I know you touched on this on Twitter, but how long should you wait about until real
results come after where you can then act and iterate copy?
I would only change the copy and change variables once the campaign is finished.
I would never change anything else, right?
Until the campaign is like until I'm finished with five thousand leads, then I would change
and iterate a copy, right?
Just because five thousand leads is the test that you're running, right?
Like you're going to get responses.
You're going to get a lot of responses from five thousand leads, man.
Trust me.
Trust me.
Please do not change anything.
I'm begging you.
You're going to get results, right?
Because at that point, it's not your copy.
It's not like none of that.
It's your limited leave, right?
It's your thoughts telling you, hey, we should change this.
And it's not.
Trust me.
You're going to get results.
Okay.
So what are your thoughts on lead magnets?
Do you drop them in the first three sequences or wait for the engage interested leads phase?
What are some lead magnets that actually work?
That's a great question, man.
So I've experimented with lead magnets.
And what I used to do basically is I used to have something like this.
So I had a notion page, right?
Such as a lead magnet, right?
And then I would have let's say I would send this right away.
I would only send this if they say, hey, I would like more information.
I would send them this notion doc where I have all my basically it's kind of like a funnel in a way where I have like basically all I front load my entire wall.
I call this wall of success.
I added all my case studies.
So if I click on this case study, I have basically like I essentially just send them like everything right here.
The replies I was getting, how I did it, detailed solution.
And they can essentially just go ahead and read through this and you're like, wow, this is amazing.
And then I have basically how do we compare?
And I have a 30 minute call.
And I've had multiple people like multiple leads that come in and just after they read through the case studies and they see this, they come in and they book a call themselves.
So I would really experiment with this type of lead magnets.
Right.
So I would never send this and the three sequences.
I would always just send this after there's some interest.
Another thing you can do is you can experience with lead magnets when they book a call.
So I've done this in the past, too, is you can essentially pressure them.
Once they book a call, you start sending them like basically what you do, nurture them into how you would build that system for them.
And it's going to be basically let's say they book over next week.
Like you'd send them like maybe like one to three emails explaining what you do.
And then at the end you say something like, hey, excited for our call.
Right. Just to keep them like keep your like just keep you keep yourself in their mind.
Right. And they come in and the call basically educated about what you're trying to sell.
OK, so. Let's see.
Jason Pace said a reply high side.
Here's what I got. If there's any other advice about it rather than what you have said, let me know.
Thanks. We work with guarantee that you'll get at least five clients in the next 30 years.
We don't pay. I like how you find the client.
That's a huge number of clients. Are you sure that's right?
And the staff in business? Yes.
That's a huge number of clients. Are you sure that's right?
And the staff in business? Yes.
I would always. Yes, I would always.
I would always like would always basically.
Thank you, Aaron.
Yes, it's a huge number of clients.
Are you sure that's right? And the staff.
Yes, I would always basically be so true and make bold statements.
Right. Like I said, these clients, right, once like once they're going to pay you, you're going to get the results.
Trust me, Jason, because in the back of your mind, you're going to be like, shit, I don't want to pay them back.
So first, get the clients no matter what.
Just get the clients and then worry about delivering later.
Trust me. Like every time I was skeptical about something about a bold claim, I've managed to fulfill it because once you get the clients like you will fulfill it.
Trust me, you will do anything to fulfill it.
So I always make bold claims.
And when they say, do you routinely do that? Say yes.
Always. I would I would I would want to see the copy that you use, but I would always say something like, yes, we we've been working with companies like yours, like company name, and we routinely generates X amount of clients.
Each month without spending a cent on ads or without any paperclip ads.
And the cost per acquisition for clients for us is significantly lower than any paperclip ads or any referrals claim or basically any lead generation method.
I would always, always, always confidently have bold claims.
What are your thoughts on real estate in my city?
In my city, there are so many property dealers.
They have a variety of properties from different developer and sell them based on the client's needs.
They have big pockets.
OK, man, you could definitely run a campaign for real estate.
One of the key things I'm going to give you basically some gold I'm going to give you is, first of all, they like phone calls better than emails.
So you can essentially so you can target real estate agencies.
Right. And you could.
Well, it depends on the offer.
Right. So real estate agencies like you really need to have a great offer.
Right.
So I would definitely run a campaign for them.
And it's 100 percent going to be a paper lead model with a upfront fee.
Right.
And I would never in terms of real estate, I would never like focus on features of our cold email system or like sales system.
Always focus on the benefits.
OK, so that out of office flow is interesting.
How would you track those replies?
So basically, what you can do is you would have a filter that retrieves all those.
Out of office responses and make right.
You'd have like a watch responses and then you would add them to a click up list or like a like a sheet like a sheet.
Whatever you want to add them.
And then it's going to be as is going to be marked as out of office.
Basically, then you can just have another mode like another scenario, which is going to be a search rose.
And it is going to retrieve all those leads from the out of office and add them to a instantly campaign, which is pretty straightforward.
It's pretty easy.
And trust me, you're going to squeeze a lot of juice from that.
Interest lead replied.
If there is zero cost on my end, I've been burned with way too many times by people reaching out to lose and reaching out to lose any more funds.
So I definitely have an example response for this.
And you can you can you can basically just you can like person them into a call, even if they say we've been burned like depends on how you reply to them.
Hey, man, I've got two questions for you.
How many big operations are needed to validate to validate and personalize 10,000 lead?
Is there a way to reduce the number of operations required to complete the entire process from validation to person?
So there's a method that Eduardo came up with.
It's pretty smart.
I'm going to like DM you after this call.
I'm going to send you the post.
So essentially what you would do is you would validate the emails manually.
Well, it's not really manually.
It's going to upload them and click Validate, which is the same thing as automated.
You're just going to click one button and upload them.
And you're going to you're just going to validate all the emails and add them to a Google Sheet.
And then you're going to basically run them through a company name formatter.
It's definitely going to take a little bit of operations in make.
Yeah, so I'll run them through make.
It's going to take maybe like two to three thousand operations maybe because keep in mind like from 10,000 leads.
Is it going to be 10,000 leads email validated or just 10,000 leads in general?
So I'm assuming 10,000 leads.
Like probably like from them it's going to be like maybe like 4,000 are not going to be valid, maybe like 3,000.
So it really depends.
So I would chunk this up.
I would remove all the validation from make.
So that's definitely like a third of the scenarios operations.
Another thing is I would personalize.
I would yeah, I would just remove all of that.
And then the personalization is the one that's going to take up a lot of operations.
So yeah, I would just get the joint secrets coupon where they give you two hundred and forty thousand make.com operation.
And in terms of 10,000 leads, it's probably going to take like maybe 8,000 to 9,000 operations.
Yeah, just on top of my head.
I'm having trouble getting up to 2,500 leads in Apollo, even when targeting three countries.
What should I do?
Also, after skipping the initial 2.5 leads, could you demonstrate how would you filter to extract more?
So the first step is you want to track every filter that you've implemented first.
The first thing is like always have in your sheets or whatever, like notion, Google Docs, track the filters that you used.
And then if you can get past that, I would just go abroad.
I would not.
It's probably because you are going a little bit too niche.
So I would just niche up a little bit, go a little bit abroad just because yeah, I would just go abroad or just use LinkedIn sales navigator.
Right.
And I would use an email finder.
So could you go into retainers versus paper model performance again?
What would you recommend for the beginning?
So first thing is I would retainers like retainers guys.
I would never pitch retainers first just because you want to front load as much value.
You want to knock it out of park.
First campaign, you have to basically deliver results.
And then once you have results, then I would upset really hard.
So typically what I do is the first campaign, I would have very, very low expectations on my end, but I would have bold statements on their end.
I would let's say I would say something like I'm going to deliver five book meetings in 30 days, five book meetings with pre-qualified decision makers.
Right.
And what I would do is basically I would once I deliver those five book meetings, typically I would try to deliver them in less than 30 days.
So 30 days.
And let's say try to deliver them in like 20 days or 15 days.
Right.
So let's say, for example, you bought like pre warmed up emails.
What you can do is you start sending right away, start getting them the results, and then you start sending them the meetings.
And then I would upset really, really hard.
And I would say something like, hey, I see that there's an advantage that we have and looks like the offer that we are using, right, is resonating with our target audience.
How about we scale this?
And then I'll enter my Trojan, basically a retainer.
And I said something like, hey, if we can, I see some potential in increasing the amount of meetings that I'm going to provide.
Here's what I would recommend.
Right. And it would be like a bullet points.
And it's just copy and paste template.
Honestly, I would say something like, hey, we can increase the volume of our sending and we can expect.
There's a lot of meetings and I would include a code and it's going to be a retainer.
And trust me when they see that the meetings are coming in, people are greedy.
Humans are greedy.
They would want to pay more.
Trust me, this is going to sound like, you know, a little bit Machiavellian, but this is how businesses works.
Like, this is how business works.
Like, think about it.
When you're a supermarket, like you are constantly bombarded with offers.
Like even when you try to pay for your groceries, they're trying to sell you another thing.
Right.
So I would always do that too.
Like I would leverage the sales tactic.
Always make sure you front load as much value at first and then upsell hard.
Right. Say something like, hey, I see like a potential adding more leads.
I can send you more leads.
Right. But here's the pricing.
Right.
So I found like a lot of good advantages when I'm using this tactic.
So I would recommend you guys use it too.
Because the entire purpose of us starting a sales agency is because we want to make money.
So you want to leverage as much profit as possible.
Because think about it, the client is going to get the meetings.
Right.
At the end of the day, like the amount of clients that they are going to get is very, very high.
Like you can never justify your basically your service.
All right.
What's the best strategy to get clients to real estate agencies?
Because their clients are customers, not businesses.
I was thinking about running a LinkedIn called Outreach Campaign for them personalized voice digits.
Yeah, then like a LinkedIn called Outreach Campaign, that would work personalized voice messages.
So you would use like an AI voice agent.
But I don't know, man, like I don't like AI voice agents.
I think they're going to die out pretty easily.
It's kind of like how like most shiny ideas are.
Just because we are so like this is so early when it comes to AI agents.
Right.
So you can definitely do that.
I would I would test.
I would enter for the markets.
I cannot like tell you exactly the results.
So I would just let the markets give you the actual results of that.
But in terms of real estate agencies.
Yeah, man.
It's BTC.
It's BTC.
So like I said, their clients are going to be customers.
So the only way is the best strategy to get clients to real estate agencies.
So I have to give that some thought, man.
And I'll maybe I'll send you like a whimsical or something.
But like I said, it's BTC.
I wouldn't recommend you do that.
Just because it's not like the best niche to go for.
OK, so Jason asks, do you find Claude is more expensive than Chad GBD?
I have noticed that you have started going more with Claude.
Yes.
It is a personal preference or you find a better result.
I'm finding better results with Claude, man.
Claude is just really good at writing emails.
But in terms of market research, like let's say tasks that require something very heavy,
I would use Chad GBD.
But yeah, man, if you want to lower costs, I would just use Chad GBD 4.0.
If you have credits, just use Claude.
And you can get Claude.
You can get like probably like $500 worth of tokens if you sign up to Atlas as a startup.
OK, that retainer upsell pitch.
Can you upload a post on that?
Yes.
I wanted to go and get that to the next level.
I'm actually going to make a post about this, man.
And I'm going to also include the template that I've used.
Right.
I still have the like what I use to them.
And the upsell pitch is very powerful, man.
Trust me.
Trust me.
It's very powerful.
Can you go a bit more and how do you structure it when it comes to software costs?
Do you pay for it or load it on the client?
Also, when building a campaign for a client, do you buy domains and warm them up for three weeks?
And then start sending or do you go to the pre-wound up route?
So that's a great question.
So in terms of structuring when it comes to software costs, typically what I've found,
the best bank for your book is you run everything on your end.
Just because I have the instantly yearly plan.
So I have the instantly yearly plan.
I bought once.
I bought it once.
It's like maybe like $900.
So I have everything on my end.
So I don't have to pay.
So let's say they pay me like $3,000 for a campaign or like an instant bill.
What I'm going to do, like you're going to have to pay for instantly for them.
You're going to have to pay for MIG.com.
So why am I doing that?
I'm just going to put everything in my account and just deliver the book meetings.
So I found that I can keep more like 90% of the margins that way.
And like you're not going to pay for other platforms such as, I don't know, like Apify, Apollo,
because it's already in-house.
It's in your agency, right?
So I found that I was keeping like 90% of the margins just because I don't have to pay for anything.
Like I'm just paying for my internal costs, in-house costs, right?
And if you're smart about it, you can lower the cost, like you can really lower the cost.
Really, like actually.
If you don't want to pay for Apify or Apollo, you can just have accounts, like burner accounts,
and just basically scrape 5,000 leads for them without paying like $0.
Or you can just get 2,500 leads just like using the Acreaper,
which is going to be $1 per 1,000 leads, right?
So yeah, so do you pay for it?
Some clients will ask for the system build, right?
So I've had an offer that I ran that you guys can run.
So here's a good offer for you guys.
So what I'm going to do is basically tell them, hey, I'm going to build that system.
And it's definitely just going to be like a template of the high ticket system that I show you guys,
which is going to be the Apify, Apollo, Mail.so instantly.
And what you can do is you can record them a video explaining how it works, right?
And then what you can do is tell them, hey, in order for the system to be built,
you can pay for these platforms to just make instantly, right?
And then you can sell them the system along with the booked meetings.
But this is what I've...
It's not going to be 100% scalable, right?
You can basically just front load that, hey, I'm going to build a system along with the booked meetings,
which is an easier sell because you're providing so much value for the clients
other than booked meetings.
And this person, this agency is not only providing the booked meetings,
but also providing the in-house system.
Because some agencies and some clients, guys, they would want the solution to be in their business.
For example, they don't want to have someone that comes in and just delivers the meeting.
They want a solution in their business, right?
They want you to come in every time.
So, yeah.
When building a campaign for a client, do you buy domains and warm them up for three weeks
and then start sending or do you go to the pre-warmed up route?
So I usually go for the pre-warmed up route, most likely,
just because it's easier to deliver value, right?
You can differentiate yourself pretty easily from all the lead agencies by saying,
hey, while others are warming up the emails, we're actually delivering the results.
And it's the easiest way to interfere with the markets and know if their offer is going to be valid or not, right?
So if they pay you, let's say, more than $2,000 for a campaign, definitely buy the pre-warmed up emails.
And then introduce the retainer, right?
This is like a Psyop.
You come in and you just basically take everything else, kind of like a crusader.
You're coming in.
You are offering as much value and as quickly as possible, and you're obsolete really hard, right?
So let's say they pay you $3,000.
What you do, you buy the pre-warmed up emails.
It's going to take like two to three days to start getting results.
You're just going to like scrape the lease, then write the copy, personalize, launch.
And basically, since it's pre-warmed up, you can send like 500 to 600 emails.
And then like really ramp up the volume to 1,500 emails per day, then pause.
And you're going to get so many replies, right?
And then you can then nurse into a call and book those meetings, send those meetings to the clients.
If the client sees the results in like, let's say, a week, they're going to be like,
holy shit, this guy actually knows what he's doing.
And then when you enter the retainer, because the emotions are high, the results are there, so you can upsell.
OK, so how do you negotiate a percentage on a sale with a client?
On the first month, we've delivered the results on how do you track their sales?
So I don't recommend tracking their sales just because they have to add you as a user and their Stripe.
And it's typically very hard to track the sales.
Sometimes they would hide it. 100% they will hide it.
So I would only add their card and Stripe payment processor, and they will get charged per meeting.
So if there's a meeting that occurred, they're going to get charged automatically.
And I would typically introduce this not the first month after the retainer.
So first month, I would deliver the book meetings, and then I would basically just introduce the paper lead model.
Jonathan says, Claude is so much stronger with the copy, in my opinion.
Yes, man. Yeah.
So Eduardo said, yeah, I'd really like to know deeper into getting past that couple of client states.
Plus 10 clients, perhaps hiring once it makes sense. Will you be releasing an advanced model for that?
Yes. Yeah, I'm thinking about releasing something for past 40k, past 30k.
And it's definitely going to be more advanced for people who are already pulling clients using cold email.
And then it's definitely going to include some of that.
How many inboxes do you buy for new clients?
For new clients, it's the same thing. It's going to be 15 inboxes.
And then I'll start sending 300 emails, 400 emails first day.
And then I'll ramp up like crazy at 1,000 to 1,500, and I'll pause for one day.
Upsell psychology of their initial due close maximizes cash extraction scale, et cetera.
Yeah, man. Exactly.
So once you have a bunch of clients, once you deliver them the results,
there's something that happens, which is called the Paloozer Effect,
where everything is working in one direction and you're making a shitload of money
just because you are frontloading so much value up front and you're upselling hard.
Therefore, you're making more money and the cash flow is coming in.
And cash flow coming in, that means long term you're going to profit a lot.
Because what I would always recommend is looking at the cash flow other than profits.
Okay, so warmed up emails from Instantly are very limited in domain variation.
You couldn't find one potential client using the company name.
Is there a workaround around this? Yes.
I would use Inboxology to get warmed up emails.
It's very cheap, so I would use that too if there is no Instantly email account.
Domain variation combined for potential clients using his company name.
Is there a workaround for this?
Yeah, like I said, I would use Inboxology.
And then there's another platform called, let me see, pre-warmed up email inboxes.
There's another platform.
Let's see.
If someone is using other platforms, just please put them in a thread in the school community.
What I'm going to do is I'm going to compile some of the best platforms I'm going to include in it.
Just because a lot of people are transitioning from the buying domains to just frontloading the email addresses that are already pre-warmed up.
Silly question.
Man, there are no silly questions in this community.
So this is the last question that we have.
So how did you apply to make JointSecret if you already have an account?
Just start a new one, copy some blueprints across and use the credits about JointSecret today.
Yeah, man, like this is essentially what I've done.
Just create a new account and then basically just go ahead and sign up for a new account and copy the main blueprints that you want.
Yeah.
You can essentially buy multiple JointSecret accounts and you can get many, many accounts.
I use Legacy Enterprise Gmail account so I can use maximum 300 email boxes, no monthly costs per mailbox and one admin console.
I have a guide that sells them if anyone wants that.
Yeah, man, I would love you can make a post about it on the community, Eduardo, so anyone can come in and basically just reach out to that person.
That would be very helpful.
All right, guys.
So thank you so much for coming in today.
I appreciate your questions.
These were some really good questions.
We're getting into the advanced topics of cold email.
And yeah, thanks, Jason, like I have a fucked up voice.
Excuse my French.
I was pretty sick.
So thanks, Axel.
And by the way, Axel, just send me a DM and let me know how the campaign is working.
Let me know so I can just go ahead and help you with the reply rates personally.
So yeah, thanks for coming in.
It's not fucked up.
Talk to yourself.
Create your reality.
That's so true.
Yeah, that's a really good advice.
Yeah.
So anyway, so thanks so much for coming today and I'll see you guys in the next week.
Cheers.
