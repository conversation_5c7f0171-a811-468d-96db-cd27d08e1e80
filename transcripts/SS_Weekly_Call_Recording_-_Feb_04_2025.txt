Hello. Hello, hello, everyone. Hope you're all doing well. Hello, <PERSON>. Hello, <PERSON>.
Hello, <PERSON>. Hello, <PERSON>. Note taker. Hey, <PERSON>. Hope you're doing well, man.
Hope you're all doing well. Hope you're all having a great week. Good afternoon.
Let me know if you guys are hearing the audio. Everything good?
Hey, <PERSON>. Hope you're doing well, man. Hi, <PERSON>. Perfect, perfect. Looks like a lot
of people have launched their campaign this week. Hey, <PERSON>. Seems like a lot of people
have been launching their campaign this week. Like a lot I've seen. Yeah, no worries, <PERSON>.
Hey, <PERSON>. Hey, AI agents. Hey, <PERSON>. Hey, <PERSON>. Looks like everyone is in. You
know which way we want to come in. Perfect. Yeah, Fathom to Create a Proposal. Are you
guys recording your sales goals? I would highly recommend you guys do. I always record your
sales goals just because you want to know when you don't say the right thing. Yes. Always
make sure you do it. Yes. So it looks like not a lot of people actually looked through
the last post. So there are no questions in this thread. So no worries. We can just go
ahead and answer you guys' questions in the chat. I can just go ahead and answer them.
Just because I think it's more interactive that way. I think most communities when they
post a lot of questions in the thread, typically people have to go like do a little bit of
research before. But this way you get like real time my answer, like my thought process
without like actually thinking. So I can just give you basically exactly what would be in
my mind at that moment. So yeah, guys. So feel free to post your questions in the chat
here. And let's just tackle one of the well, like every question. Okay, so first question
we have is when doing the two campaigns, do you find that the broad campaign is fine with
Apollo in sales now? I'm guessing you're talking about the looking for companies hiring, right?
Yeah, so and then the targeted one is the and then the targeted campaign is fine with
the jobs. Yes, yes. Yeah, typically what I do and what I've been doing lately is a variation
a variation of lead scrapes from <PERSON>. So it would be Apollo and I would use this filter
right here. If you guys haven't seen this in Apollo, you can actually get a ton of leads
of companies hiring. You just got to have to look up the right filters. And if you guys
have been seeing the journey on YouTube, like I just this entire week, I've been recording
every single day, like a video of a target, like whatever target we had, we had seven
days. Like we targeted everything like I even showed you guys how to get leads or e commerce
using Apollo, which is like a few hacks. When you research leads in Apollo. So we did e
commerce, we did marketing agencies, we did SaaS companies, we did crypto industries.
So I think a lot of people here in the community are wanting to target crypto. And this is
a booming niche nowadays. Because you can go ahead and scrape crunch base. If you guys
don't know, you can go ahead, scrape crunch base for recently funded companies, you can
feed that data into a new finder find companies. But yeah, that's let me know if you guys want
to see that. But yeah, let me remove those filters. For example, you would just go to
this filter right here. And it's job postings. Right. And the cool part about Apollo is they
have like a key like a keyboard, keyboard, like a keyword type search. So you don't have
to put in the exact word like exact job titles such as sales representative, you can just
put in sales, and it's going to filter out all the job titles that include sales. Right.
And in our case, you want we want all the jobs that include sales, sales rep, sales
representative, representative, you can add another thing, just add BDR. Right. Because
as you guys can see, there's BDR, lowercase BDR, capitalized. So that means that the key
the keywords system, right, it just fetches like the entire keywords that you're going
to feed in. Right. So if you're in sales, it's going to filter out all the keywords
in sales. Right. You're going to you're going to be left out with a bunch of leads. And
then what you can do and then you can just basically remove all the filters that you
don't want. So to answer your question, yes, I use Apollo and I use also LinkedIn Sales
Navigator. Right. Both of them like a combination. I would scrape 2500 here, like 3000, whatever,
like 3000, 4000 here. And then I would go to LinkedIn Sales Navigator, I scrape another
4000 and it instantly just filters out all like if there's a duplicate, sometimes I mean
like they like some lists would have like maybe like 50 to 100 duplicates, just fine.
And then I'm not and I and then I use the scraper. The other one. Oh, yeah. Oh, you
guys are not seeing the screen. And. OK, I'm just going to redo it again. That's fine.
So in terms of Apollo, let me know if you guys can see it now. OK, perfect. In terms
of Apollo, there's this new filter here, which is called companies job posting. So this one
job posting. So instead of just looking for sales, the development representative or whatever,
you can just add sales just because it's a keyboard based platform. You can just put
in sales and it's going to filter out all these sales jobs that you want. Another thing you can
use this filter right here, which is technologies. Now, depending on you, like if you have a client
that uses some type of technologies, maybe they want to target companies like e-commerce, you can
just look for technologies such as Shopify, WooCommerce. This filter is going to find all
the companies that are using those exact technologies. Now, give that some thoughts, right? Even if
it like let's say a client has a SaaS, for example, you have a client you've managed on board
the clients and they have a SaaS, you could look for companies that are similar to whatever his
past is. And you can just target them and you say, hey, you would come up with a better offer for
your clients. Right. And then you would come and you say, hey, our offer is better in whatever
software stack you're using in your website or whatever technology you're using. We built this
X software. So these kind of like things that I do whenever I'm fulfilling. And then I've used
Apollo and I would use LinkedIn sales navigator, right? Both of these platforms, like I said,
and I'm typically good to go. Another platform that I'm trying to use lately, which is called
Golden Leads. So this is great. So Golden Leads is basically a platform we can get unlimited amount
of leads from LinkedIn sales navigator. But the pricing is quite expensive. Right. And I didn't
want to introduce it to communities because not many people are going to be able to paper this at
first, just because like I would rather get your first clients, right? Using the traditional
methods, then worry about scaling afterwards. So if you really don't want to scrape anything,
you want to extra data, you don't want to deal with the hassle of scraping, you can use this one
and you can pay as you go. So you can basically pay for all the way to 2,500 leads. So let's say
you want to scrape 2,500, right? You just paid $37 per like 2,500 lead. The other alternative is
using Fansom Buster. So you'd use Fansom Buster, which is like $69 a month, right? If you go to
Fansom Buster, they just charge you for hours, right? So for their solutions, there's LinkedIn
sales navigator, which is this one, sales navigator exports, and they only charge you per hour. So
execution time, right? So it's like, they give you like a number of fansoms, right? Which is like
actors, I guess you could say. And in terms of timing, is the amount of time of the execution,
execution basically. So let's say you run a scraper and it takes 30 minutes to scrape 2,500,
you're going to slash off 30 minutes from here, right? And they give you 20 hours. So this is way
more cost effective. But the only problem is you can't scrape more than 2,500 leads per day, right?
Because you're going to risk your account get banned. Now with golden leads, you can scrape
whatever you want, because they are they have like some sort of affiliates with LinkedIn, they can
scrape whatever the hell they want, basically. So this is a Chrome extension. And this is like a
pool of leads. And then, yeah, but let me finish your question. So I can just give you the time and
energy you need. The campaign with a yes for company hiring. Yeah, so I use a combination. And then I
use the other one that I showed you guys, which is advanced link. This is this one is like really
good, man. Like I love this scraper. I've been using it like nonstop. And you don't need LinkedIn
sales navigator guy, you just need your LinkedIn cookie, just your LinkedIn cookie. For example,
like it would be a keyword jobs such as SDR. Like imagine scraping like in LinkedIn, currently,
there are 3500 companies looking for an SDR. Like saying like, for example, like if you scrape these,
right, and like 40 to 45%, there's the poster full name. Like you're pretty good, right?
You're pretty good. You're like left out like 1300 leads. And keep in mind, these are warm leads,
right? Yeah, warm leads. So this is like an like what I would say, like leads that are ready to
actually like be our cold email system or our sales system, right? So this and companies hiring
in your gold, right? You have a really good list. So hope this helps them. If you have any additional
questions, let me know. So James, what would you recommend for a student in terms of chart and
structure as a standard? I would always recommend you charge in terms of value, right? Just think
about it. If you get started, the first price that I've charged was about 1400 or so. So
at that moment, I was like really scared. I was like, maybe if I maybe I'm charging more,
maybe I'm charging less. But you should always charge in terms of value on what you could
essentially like deliver. Just think about it. Like the clients, let's say you're promising them,
I don't know, like five book meetings a month. Like just think about it. If five book meetings,
if they manage to just close one or two, like their average deal size, for example, would be
like 5K. So that's already like you're already like justify your entire existence there. But
I understand that at first you have this limit in belief because I did. And I know most people do.
I would go for something around 1400 to 1500 for my first client, like from a starter.
You need to get used to like big money coming in. And then once you get used to it, you're going to
start saying, hey, pay me 20K now. You're going to stay at them in the sales call.
But yeah, I would charge along the line of 1500 to 1600 if you just get started, right?
Just because I don't want to give you just like a like a nuanced answer.
Like I would give you like exactly like a number. Like I would start from 1500.
I've seen other people go all the way to 3000. But I mean, it depends if you really can like pitch
that on a call and actually like believe that. Just because pricing is like an interesting
subject, right? Some people would charge like 3000 and like they would believe that like this
this is 3000 and some clients would be sold. But because we want to make sure we have all our,
you know, we want we don't we don't care about extremes. We want to like we want to make sure
we have all the best. We want to maximize our results. I would go for something around the
middle. Maybe like 1500 to 1600 max. So yeah. Hey, Yusuf. Hey, everybody. I've configured a
campaign to send 30 emails per day. One week instantly sends only six emails. Is this because
I'm doing follow ups? Perhaps follow ups are not counted in a campaign analytics. Yeah, man.
In terms of follow ups, right? Looks like so you shouldn't worry about like the campaign analytics
the first week, right? So just my recommendation. If you're sending 30 emails, right? Send 30 emails
per email inbox. I believe you have like how many emails do you have in your campaign?
Yeah, let me know and I'm going to answer your question. Okay, Edward.
For client appointment management, I've decided to use call dot com. Yes. Great choice, man.
Call dot com is the best. Allow me to be the manager of the account and just send an invite
link into their calendar. Just wonder if you've used it and seen any problems. Actually, that's
a really good choice, man. So in the teams, you allow me to be the manager of the account and just
send an invite link. Yeah, that's a really good choice, man. I've never seen like any problems
with that. You just have to basically communicate that with the clients and it will be gold.
Like I've seen your progress, man. Like you have a great offer. You have a great copy
and I think you already had some. Do you have any positive responses yet?
Okay, Travis said in a few instances on sales calls, the client is you the meaning they get
delegating an outsourced legion, not really this seems for for S E. I feel like selling the system
is like a product pitching, whereas in the service is selling the outcome. Is it actually
tougher to sell systems versus legion services? It is tougher to sell legion services than systems
in my experience, just because selling legion services clients just see the end results or
the outcome of whatever they're paying for is a meeting. But when you sell a system with it,
they can see like they can download that blueprint. It's like a product, right? They can
see it. They can see the modules working. It's more like a small something like they can see,
right? Not like, okay, here's a meeting. Just come to that meeting, right?
So in my experience, it's a little bit easier. But what I would recommend is during the call,
you would you would include it. You would include it, right? It's going to be a hard
it's going to be like an easy sell. You would say, Hey, I will, I will basically
deliver you the meetings. And I'm also going to you also get to keep the system to write,
which is even more. And you would include that in your guarantee to say, Hey, even if I think you
did that in your offer, because when I read it in the morning. Yeah. Jason, how do you typically
fulfill takes to fulfill just approximately I'm worried I'm running out of time. And I had a few
issues. Yes, I know. Well, if I'm if I'm basically back then well back then when I I used to get,
you know, I get pre warmed up emails. So now I'm still we're still getting pre warmed up emails,
right? So the best the best way is to not worry about warming up, right? Have like a really good
onboarding like the best onboarding like once you have a few clients, like you're going to have to
develop systems guys, just because I've seen like a lot of people are getting clients and it's time
to like scale up, which is like that is that's actually pretty good. Like you're getting clients
in like a month. Like it took me a long time to get a client which is so like you would have a
great onboarding where you have the great like good questions. Like do you have the questions
that I have in my onboarding? Make sure you all like copy and paste them. And then it would start
you would get started like the next day, like you would have pre warmed up emails, and then you just
start with market research for them. You just start at like the next day. But you know, it
typically depends on like the scope of the project.
Um, how to scrape leads for local businesses like dental clinics. Yeah, there's two ways you can
either use apify and use something like you go to store. I'd use like Google Maps.
That's one right here. Google Maps extractor. So we'd use
this one right here. And I think it's yeah, I think it's cheap. It's actually free. Yeah, it's
free. So you would scrape basically all of these you would put in. They have like a search term
here. You can even filter by precise search. Yeah, you can even include the Google Maps URL.
You can even include the Google Maps URL. So you use like an apify scraper that scrapes Google
Maps. Another thing is you would use Phantom Buster, which is this one. I believe they have
an option here to go to dashboard. And I think it's an scrape leads.
Yeah, I think it's this one. Yeah.
Fudge John business scraper. So if you go and look up this website, I think
a business scraper.
I believe this is the website.
So yeah, let me check this one out.
Yeah, this is the one. So let me see.
So I believe they have a scraper here here in Phantom Buster. But let me I'm going to follow
up with you after the call, so I can just make sure I give you the right one. So yeah,
man, you would scrape Google Maps. I don't think you can scrape Apollo just because local
businesses, Apollo is not the best. Right. But yeah, another thing you could do is just on top
of my head. You could you could you could use this one right here.
So like what I would do like in front, like if I were you, I would just go to apify,
and I would look for any scraper that has Google Maps. Right. Just because in your case,
you said you want to scrape just dental, right?
Dental clinics. Yeah, because these are B2C. They're not B2B. So LinkedIn or Apollo are not
going to be the best bet. I would just go for like a Google Maps scraper like a Google Maps scraper.
Yeah, you just have six emails. So yeah, three thirty emails per day. It's like 180 emails.
So yeah, 180 emails. I think I think you are excluding the follow ups. So make sure you go
to the settings and make sure you are actually included in 180 emails. So show me your your
instantly account after so I can see like send me a screenshot.
Yeah, could it be that you have campaigns in different time zones? If your first campaign
starts before then the time zone campaign on the same day, you may use all your daily emails
for inbox before the second campaign. Yes.
Justin said, it's just curious, do you pay VA to scrape your leads? If you do, how much do you pay
and are leads good? I don't I don't recommend do that. Do that just because I had like very bad
experiences back then. Like I hired someone off Upwork to scrape me leads and yeah, I had like
so many battles straights in the campaign. Yeah, like I would be like, I would be like,
straights in the campaign. Yeah, like I would recommend that.
I would recommend doing that. Just scrape your own leads or have someone
that you can train, right? You can show him, hey, here's how you scrape.
And then they can do your job instead. But in terms of buying lists or hiring a VA to do that,
unless you give them like an SOP, like an SOP, okay, go here, do this, do that.
Yeah. Hey, Eduardo, if you're doing well, man.
How many how many inboxes do you allocate per campaign to deliver the appointments would be
the standard 15? Yes, 15 inboxes. Like I do the same thing, right? I do the same thing,
5,000 leads for every onboarded client. Some agencies do less, but for me, I just,
I found the best. Like my formula is always 5,000 enriched leads, 5,000 emails, 5,000 leads.
And then I'll just start sending. Yeah. 15 inboxes. Same formula, man.
Thanks, Todd. I have a prospect that needs to scrape e-comm brands running ads. Yeah. So ones
that need lots of ad creative done. They're on UGC app, creative agency. How would you go
about finding these leads? Yeah. That's an interesting one, man.
How do clients before like that? The best bet, the best bet is scraping a Facebook ads library.
So you would go to FFI and it's Facebook ads library.
Keep in mind that you're going to get duplicates. There's just part of the game
because there is a lot, a lot of companies that are running multiple creative, you know.
Yeah. So you would put in the library here of the companies, right? And then you would scrape also
add details. And then what you would do is you would feed in the company name to any mail finder,
right? And then you would use instead of using search by, which is this one.
Yeah. You'd use search for a company's emails. This is the one. So it's going to give you like
a bunch of emails, right? And then you would just essentially, in my opinion, like you would send to
multiple, right? So we'd have like multiple emails in the same campaign of the same domain.
Just because you don't know who is the decision maker, who is the CEO, right?
So you would search by company name and you would just put in the company name
and they will give you all the emails in that company name.
It's like the most straightforward way to do it.
The above example about the dental clinics, is there a way to help dental clinics
fight new patients? I guess that is not BDC. Yeah. So there's not a BDC. Yeah.
Yeah. When it comes to BDC clients, like most of the big companies are actually targeting them.
They prepare a list and it's usually some sort of a newsletter where they get people there.
So you would like, that's a tricky one, right? BDC is like, it's very hard to find data there
and not most people can run BDC just because there's like some bullshit with compliance
because you cannot send emails or spam emails to non-company domains.
What's up, Eduardo? How's the campaign going?
So let me see if there's any question here. Going great. Happy to hear that.
I can see you've been sending lots of volume based on our conversation.
Okay. Let's see if I, if there's any question here
that I missed. Okay. Have you had people that declined a call but wanted the proposal?
I tried to get them on a call, but they have a good, they have gone cold now.
So they said, send me a proposal. And then, then they, they've gone cold.
Yes. I'd keep following up, man. Yeah. I'd keep following up, man. We have in the classroom,
I think it's this one, proposal, follow up. So this is the one.
I have like a pre-drafted emails for you guys and in the classroom, send and follow up after
proposal. So I'll just keep following up with them until they, they're basically telling me,
no, this is what I do. I keep following up. Sometimes they don't have time.
This is why I recommend you guys send a proposal during the call,
right? If you can send it during the call, like that would be the best bet.
Right. So in the first call, you would say, hey, I can send you the proposal and we can just go
over it together. Instead of me sending you the proposal after the call, and then it just gets,
you know, just thrown away. So yeah, in your case, I'd keep following up, keep following up,
and eventually they're going to respond. Right. Eventually they're going to respond.
The keys just keep following up.
Um, yeah, I have several proposals out and similar people have gone cold. Do you push to have them,
to have them close on the call? Yeah. Um, I definitely keep pushing. Like I just keep pushing
if they don't, like if not, let's say you like, if you don't answer, like after a few follow up,
you can say like, Hey, um, just wondering about that for the proposal absence days ago. If you
want, we can just hop on a call. We can go over it together. And if you have any like objections
about it, we can just go ahead and address them. And then typically if you keep following up,
they're going to answer or trust me. But the best way is to send them the proposal during the call.
This is what I like. The way that Vishal showed that the other day, like it was great. Like he
would essentially have a presentation there and he would show them the ROI. Right. And then at the
same call, he would send them the proposal. So that's even like that makes you qualify heavily.
Right. But, um, I'd like, I'd still keep following up until the eventual response.
So that's more of a generic proposal during the call. Yes.
Realistically, how many leads can you expect to generate for clients? So we know what we could,
uh, so we could be, uh, unwise to get to you more than five. Yeah. Well, man, it depends on
the client's projects, right? Like, uh, depends on the markets, depends on a bunch of things.
But, um, for example, most do five to eight. Would you say, yeah, I would stay on a, like,
uh, like a, like a range where it's still, you're still pretty sure that you can get them,
those clients. Right. So five to eight, five to 10 is pretty good.
For me, it's even harder because I, I say, I'm going to get you guys like five clients. So the
volume that I'm sending that I could be sending or is like significantly harder.
So, yeah, and then, uh, I would, uh, I would definitely just go for five to eight. Yeah.
Five to eight, five to 10. And then, uh, yeah, because it really depends on their offer,
their product market fits, you know, like it's easier for us to get clients, right?
Because it sells some agency, like you could start like a campaign tomorrow and get clients
pretty easy, like in one day. Like someone can literally come into the community,
just look through the threats, copy, like a copy, like just copy and paste a email there
and it would get a response and they can get a book a meeting. So easy. Um, at the moment,
I have nine email inboxes. I've got another 27 inboxes currently warming up and will be ready
next week. I started my campaign yesterday, but can only send 270 emails per day.
Should I pause the campaign and wait for all to be done or should I just let it continue
with the nine at the moment? Um, I wouldn't stop. I would keep sending. Yeah.
Yeah. You can send 20, 20, 70 emails. That's fine. Like, uh, I'll just keep sending. Just
think about it. You could land a deal for those 270 per day. Right. So just keep sending
and, uh, once you said, uh, the others will be ready next week, then you can essentially like
blast everything else, but don't just lose, don't need like a lose the force for the tree.
Just keep sending. Hey, Raj, I have to send a proposal for hurricane niche. And this is
my first possible clients. Can you share what proposal template and Benadoc to share? And if
we have a make blueprints, um, and yes, I can send you my own, uh, proposal templates. I'm
going to send it to you and the EMS, but in terms of blueprints, yes, we do. We have like a watch
documents. I'm going to link to you the video that we have in the classroom. We have a watch
documents. We're using penadoc and then just waiting for the client to sign. And then we're
just sending them the onboarding instructions right away, which is extremely simple and easy.
Good luck, man. Hey, Jason, uh, I tried to personalize the proposals that it speaks to
them rather than a generic one. Maybe I need to rethink how I do it. Um, yeah, in terms of
proposals, man, I don't like to overdo that. I don't like to like, like talk too much in the
proposals just because, uh, in my case, I found the best results as when the proposal is extremely
professional, it has like, it's straight to the point. Like there's no bullshits. I don't like
include in like most people include your, your problems, our solution, you know, market research.
Well, I really think that the client is going to read that they, the clients only care, especially
like, like things that really want to pay you money. They don't care about that. They're like,
what is the price? How much can you guarantee? And can you get started? There you go. That's it.
That's all that matters. Even if an enterprise level deals, right? So I would only include like
the main important things. I would also include milestones if you want to. And that's pretty much
it. You don't need to add any additional jargon. Did you record any sales calls in the course? Yes,
man. Um, so basically, uh, I was with this guy last week and I was like, uh, so I run a community
and I want to record this sales call with you, but most of them were weirded out about it. But
thinking about it, man, I'm just going to record it. I'm just going to do it in any way.
Uh, another hack I have seen from a closer, never send a proposal via email, book them. Yes. Yes.
That's a great idea, Yusef. Yeah. Like, you know, your stuff.
Yeah. This is why I don't like sending them a proposal via email,
but, um, yeah, you can, in your case, let's still follow up, right?
Wow. You guarantee clients. Yes. Yeah, man. That's even, uh, it's even harder, right?
Uh, good to know. Uh, thanks. He means leads book meetings, not clients. No,
actually clients, actually clients, like people who are going to pay them.
So it's like, uh,
it's like, I'm like a different
and the process that they see, uh, just because I think, um, well,
I know that I, like the case study that I have in the it space, like it, especially it and recruitment,
like, think about it. Like, uh, the case studies, once you guys will have a couple case studies,
they're going to do all the work for you. Right. So there was a deal back then. It was like one of
the biggest deals that I've closed and it was in, uh, like, in, like an it, uh, health company.
Like an IT, uh, health company, which is the, basically the niche that I recommend you guys go for.
Yeah. Yeah. Typically these are like a little bit higher, right? Like, uh,
they, like those companies have more like a product fit.
Yeah.
And the process that they see deposits of response notification from Slack. And then they see, uh,
they agree that the clients is qualified and send booking notifications,
making sure I have the optimized flow and best client experience. Yes.
Okay. There's great advice about a proposal. Yes. Yeah. I don't like the ones, like, I don't
like when you add like a couple things in a proposal. I don't think that's, uh, I think
that's pushing it. What's up, Axel? Hope you're doing well, man. How's the fulfillment going?
Yes. Basically I can, I can charge more. Yeah.
So the goal for you guys is basically have enough skin in the game so you can able to find like a,
once you crush it in a, like in a niche, you would start productizing nights, but that's not,
that's when you, like you, like you basically break through the 10 K a month. Once you're
doing 10 K a month, then I would start like productizing, like I'd start productizing my
service and you would have already like a couple case studies under rebuilt. So the fastest way,
the fastest way, uh, to actually like do this is pick the niches that we always talk about in the
community, right? Like a B to B niche. And then once you acquire just two to three case studies,
right? A couple case studies, you would basically have like a skin in a game and you would have a
lot of better for falls. You have a little bit of cash flow. Then I would recommend your product
size, right? You'd private project size your service and you go online in niche. For example,
let's say you manage to close a client or like, let's say you manage to close three clients in
recruitment, then that is your calling. I would just go ahead and set like, I'll just keep like
targeting recruitment and then I would leverage all of those case studies from recruitment.
So for me, when I, like I got my first clients and recruitment, I kept saying that name with that
clients, every single email, like I would just say that and they're like, Hey, uh, you know,
let's look up this company and they, they're more likely to work with you, right? But first I didn't
have any case studies, right? Yeah. What are your thoughts on the video sales letter? Yeah,
you can send that as a loom. Yeah, that would really work. Yes. So what I used to do is I had
like a playbook, um, basically a playbook where essentially I would have like a bunch of information,
a bunch of case studies, and I would include them all in a notion doc. And then I would record a
video, walk in, like I would just read all those case studies and it would basically be like a VSL
for them. So sending them any loom is great, man. Uh, by the way, guys, let me know what do you guys
want to see in the community? Just because I don't want to say this, but maybe we can bring in like
some of the biggest space, like people in the space and cold emails that I'm friends with,
and they can host like a big, uh, like a, like an event where it's like one hour or two hours
where they come in and just like, they would talk about like, uh, everything cold email.
Yeah. Yeah. Because I'm trying, like, um, I want to like make this community
constantly updated, like, you know, which is how, like, this is the biggest thing.
I don't want just like having, you'll get your coins, but we need to be constantly,
you know, updated. This is why whenever I'm going through something in my company,
I'm just going to share it. Right. Yeah. Okay, guys. So that, that made me even more motivated to.
Yeah, man. For sure, man. Yeah.
Hey, Raj. Okay. He thought, as I mentioned, I'm about to send a proposal and I wanted to know
more about your onboarding process and how you'd go about it. I would go about it for reference.
The call ended up an hour ago. Yes. So here's the TLDR. Essentially, my onboarding process is the
same, uh, from, from, uh, I, from the moment I hit like 10 K and it's usually, um, and the call,
right. It's either I send them a proposal during the call and I close them in the call and I send
them the contract. And then my onboarding process is essentially once they sign a proposal and
pan a doc, there's an automation in the background that waits for document dots paid. Right. So once
they sign, they get redirected to my stripe, which is a product size service. So what they do is they,
once they pay, they basically, they get redirected there. And then they receive like a thank you
email. First thing, thank you email. Second thing is another email. And we add in just like a sleep
module, like, like, uh, maybe 90 seconds, just to make sure it's human. And then they receive an
onboarding instructions. Onboard instructions are an onboarding link where we basically, uh, have
them book an onboarding kickoff call. So once they paid a sign, we get up, we'll get on a kickoff
call and we both go over an onboarding form. And in that onboarding form, right, we have a bunch
of questions, such as their pain, like what would be their ideal customers, like every single thing,
like all the questions that you're going to need, basically their case studies, like a keywords,
like even include questions like, um, if you were on a sales call, what kind of questions would you
ask that ideal clients? I would also include like links of what could be their ideal clients. I would
say, Hey, give me an URL of what, like a potential dream clients for you. And then we use something
called a keyword based, uh, search system where we scrape that ideal clients website and we feed
into AI and find keywords that they are using. We can just use that in Apollo.
Right. And then, so once we do that, they get out onto my channel in Slack
and then I define my availability, which is Monday to Friday. Right. Now that I'm, uh, like managing
the community is very bottlenecked. Uh, like, uh, some clients, uh, like they, they're like,
why are you not available? But yeah, um, from Monday to Friday, and then it's like, um,
from, uh, 10 AM to 12 PM. So two hours per day. And I just give them progress updates,
like twice weekly, but, uh, some clients that I have in retainers, like are different.
Some consultant in the side, like, uh, like weekly calls, but, um, yeah, that's pretty much it.
Once they get added on my Slack, that's it. They're on in my, they're, they're in my castle.
Like I can basically just send them progress updates and everything is like happening in black.
Bro, if you have to pick one of your systems that you have built, which one would you pick?
Because I'm confused now. I saw the X and I want to concur. Yes. X on hand. Like, um,
I'm really glad I found this for you guys. Excellent. Like the best, like you can find,
um, you can scrape websites, your company news, like everything in there.
And there were, what we're doing is if you go to X,
X is basically going to be like the next clay, right? They have like a VSL or someone explains
that you can essentially look for, uh, like you want to look for companies hiring for an SDR that
are startups. And they're going to basically give you all the leads. They're going to retrieve
all the leads using AI, right? So like, this is like, uh, one of the craziest things.
It's like search engine, but like using AI, you would look for news, look for a company
and they're in the middle of doing this. So we are actually in like the
early birds. I guess I could say we are early birds with this platform. So maybe we can
negotiate a deal. As you guys can see, they have Nvidia here, which is they have cursor.
Like imagine getting a deal with these, these guys. So yeah, I would use Excel,
like a hundred percent instead of using a make request to scrape the website.
Yes. Just extra for my campaign. And it's made some really good personalization and no cost at all.
A hundred percent recommend. Yes. Like instead of like, this is like the definitive way of
bypassing that bullshit of maker requests. Like you just make a request and just like
parse that into text, but it's still a lot of older. Instead, it can just strip out everything
else and just use one platform that does everything. Right. So that's it. So that's
right. So yeah, I think we have like 10 minutes left. So yeah, guys, make sure you
leave all your questions, your next questions in the thread that I'm going to be posting
along with this recording of today.
Yeah. And then 10 is really, really taken over, man.
He saw it. Sorry for many. No, no worries, man. He saw it. Sorry for too many questions.
Just I'm a bit overwhelmed. I want to know how many emails set up as they have promised 20 book
meetings, 20 book meetings in what timeframe?
A month. Okay. 30 days. Yeah, man. You should be sending a lot of volumes.
Yeah. So how many emails do you have?
Yeah. How do you go about the client's updates? Do you have a process for it?
Not really, man. It's whatever I'm actually going through like for them. Like I would say
something like, hey, the offer and messaging has been set up and then we're doing great.
Here's an update for you. Let me know if you have any questions. That's pretty much it.
That's what clients need to know. Just like a high level overview. You don't need to like have
a process for it. I will set up with him tomorrow on kickoff call. Yes.
Yeah. Typically if you're delivering 10 book meetings and for us, like 10 book meetings,
we would need 15 emails. It would probably be around like 35 emails to 40 emails just to be
more safe. I would be more safe. I would get like at least 35 emails.
And then I would use the high intensity sending. We would have high intensity sending for a day,
a positive day, and then I would have average days. No, 35 email inboxes.
Yeah. Just because 20, like 20 meetings. Well, it depends on their offer. Like you could,
you could book them 20 meetings just by like, I don't know, like 15 emails and 15 email inboxes.
Yeah. You would send not 30 emails. I'd start with 20. I'd start with 20 email inboxes.
Not 30 emails. I'd start with 20. I'd start with 20 email inboxes.
Yes. Yeah. The high intensity is just the rest days. The rest days is where the campaign just
gets paused and they're like ESPR, like, you know, you're raising the volume
and then you just lower down. In terms of pricing, I don't know, just go for like something around
1500. Like my first deal was 1500, like 1485. Yeah. I wouldn't go over that.
You would be like, you would have like a, like a setup fee first, right?
You would have like a setup fee and then you could transition into a paper lead.
You could say something like, hey, the cost per five book meetings would be 1500. Today you're
going to pay a 50% deposit, which is going to pay for the infrastructure, which is fully refundable.
Right. So after we deliver the book meetings, then it's a paper lead. Right.
So I would do anything to get that first client just because that first client is going to get
you case studies, going to get you like, once you get that first clients guys, that's it.
Like it's going to snowball. Trust me. Cause now you know that your cold emails are working.
Now it's just a volume game.
Yeah. Let me answer your questions. My dear Roger, what is the benefit of the high intensity approach
as you compare it to a more stable one? Yes. I think I'm the, was the first, not actually,
not the first, but there are a couple of people in this piece. I don't know how they found this,
but I was thinking I was the first who actually invented this. Yeah. Just because I think
deliverability is the most bullshit thing ever. Right. If you had a, you have a good offer,
like everything else is just like the deliverability, like tweaking the campaigns,
like it's like the 10% left. Right. So in my opinion, right. And in my experience,
like sending in high intensity, right. This method that I came up with, which essentially the logic
behind this is you emulate like a, like in high intensity training, right. So you would basically
train hard and then you rest the next day and then you pause basically the same thing. So what I do,
and I still do is let's say I warm up my emails for 21 days or I get pre-warmed up emails.
Like I would send in a blast one day, let's say a thousand email. Now I cannot send a thousand emails
every single day for more than two days. Otherwise I'll burn all my domains. I send for one day,
right. And then I pause one day, right. To recover. And then I go into a stable
sending pattern the third day. Right. And then I'll send more.
So on average, right, I'm just basically going up and down, up and down. And I can benefit
from sending in high volumes without missing up my deliverability. So does that make sense?
Do you send max more than 30 emails per day? No, when on like high intensity days,
it's more than that. It's usually like maybe 70 emails per inbox.
And I just bash them up in one day.
Okay. Yeah.
Do you use the emails for another campaign on rest day? No, never do that. You should let those
emails rest. And then like you would use them later on. So this is how you actually guys get
your client's clients. It's all about speed. So like speed, speed, speed. All you have to do
is like, let's say I want to fulfill a client as fast as possible. I got the cash collected,
pre warmed up emails, like stuff from a get go, right. Those warmed up emails, I can send
the blast, like I can blast people, I can just go ahead and like start my high intensity sending
pause for a day, and then send for like, maybe four or five, like four days, like on average,
like even less. Like there's like, there's a few people in the DMs, they've sent me their campaigns
and they're like, people are getting kind of responses since like two hours in. Right? Like
I believe it was Michael, but he has a good offer. Like he's targeting corporate events planners.
And he has like a positive response, like two hours in. Right? So even like if you're sending
at that, like intensity, you're going to get so many replies. Trust me.
Frankly, I don't know. Must work judging by the results we've been getting,
but it defines common wisdom. Yes. Does it average out on number of emails?
Yeah. Finally, Jason. Does it average out on higher number of emails sent in short period of time?
Or is it front load outreach? Yes. Front load. Exactly. Front load outreach, you get responses
more quicker. Just because I'm like, for a new client, in my case, what I like to do is I'm like,
like I'd want to interfere with the market as quickly as possible. I'm like, give me data back.
So I know the offer is great.
With that high intensity is all new leads or is it with the follow-ups when it comes
time to the follow-ups? It's all new leads, my friend.
So guys, we only have two minutes left. So maybe we can, I can answer like a question or two.
Imagine all your leads. Yes. Now we just come into a inbox full of the replies.
Yes. So because this is just the best way to handle outreach lag. So the biggest devil
is when you wait for people to come in. And it's typically they come all in one batch, right?
Yeah. Yeah, this is like what, like,
Love Tuesdays.
Yeah, man. This is like one of the methods that they use whenever like,
like these are like methods that they use whenever those lead gen agencies,
when you sell like a lead gen agency, like going over 200k a month, this is how they do.
Yes. Just make sure you go into the classroom and make sure you go over all the models because I
put my heart and soul into them. And I made sure I never like not gatekeep anything. Yeah.
So there you go, guys. I had a lot of fun today. Good chats.
I think in the beginning, it's hard to get the bigger picture.
Yes. Well, just feel free to reach out and explain to you, man.
Well, I'm gonna include this recording guys in the classroom, right? Make sure you post
your next questions and this thread that I'm going to be posting in a few seconds.
And I'll see you guys in the next weekly call. Cheers.
