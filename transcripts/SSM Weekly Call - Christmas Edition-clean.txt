How are you doing, <PERSON><PERSON><PERSON>?
I'm fine.
Pleasure to be here for the first time, man.
Hello, brother.
What's up, guys?
I'll wait for the other people to come.
This is more like a classroom
lecture, right? So we keep the
cameras off, right?
Awesome.
Hey, <PERSON>.
Hey,
<PERSON>.
We're doing well, guys.
So it looks like our people are just
coming in.
Hello, <PERSON>. What's up, man?
How you doing?
Good. Thank you.
Yeah. Good time seeing you.
Dual lines in one
frame.
All right, everybody. Just wait
for other people to come in. Looks
like
not many people are coming in
yet.
All right.
There you go.
People are just coming in.
All right.
Let's just go ahead and read through
the questions that we have here.
All right. So we're going to go
through the questions that are in the
thread first. And then
at the end, guys, just put your questions
in the chat, like whatever you guys
want to ask me, like literally anything.
Just ask me questions
and I'll be able to
essentially just reply to any question you have.
So let's just go ahead and get started with
the first question. So the first question that we
have
is from our lovely friend
with the lovely smile.
<PERSON><PERSON><PERSON>. So looking forward to the call.
Here are my questions. So where do the
where do you get pre-warmed inboxes
for clients to start campaigns from day one?
Opinions on
DefiWire, like
inbox like <PERSON><PERSON><PERSON>. Yeah, okay. I know.
I know these platforms.
Why do you include sends from my iPhone?
Yeah, I will explain to you why shortly.
Do you add opt-out
information? What should
be the minimum sound size for the niche focus?
Would you recommend targeting one to three
niches at once
or focus on one for the beginning?
That's a great question. Opinion
on hybrid campaigns, LinkedIn
plus, cold calling plus cold emailing.
Would you have
a sales museum?
Okay, my friend. So I'm just going to go ahead
and answer your question. So I was taking some notes
like 15 minutes earlier. So
where do you get the pre-warmed up
inboxes? So I always
get them from instantly. So instantly
is the best.
I've experimented with
multiple ones such as
Inboxology.
Let me show you how
this works. So you go to this
platform right here. So there's
this platform called Inboxology.
So they offer you
basically pre-warmed
up emails and they also give you
like
they also do like
SPFD cam
everything like custom tracking domains
for you already.
And the pricing for that is actually
I think it's
like $3 per
email account. And they do everything
like 100% like free setup
build monthly. And you can order
basically 10 accounts. And the way
you order, they just add you to their Slack
channel.
And you just input
like example of domains
that you want to get. And they hook you up
with them. But I wouldn't recommend
using these platforms
just because you don't have full autonomy over
you know the emails.
And like the only
platform that I would trust, it would
be instantly. Like instantly is just the best.
Like if I go to instantly right here
let me log
into this account right here.
And if I go to
emails
then what I'm going to do
let me remove this. I click on add
new. They have like pre-worm
accounts. So pre-made accounts and domains
and you can start sending right away.
And 2,000 domains are made.
So you can just go ahead and get the email
domains. And what you can do is
essentially just click one button
and then you click on the
selected domain. You click next.
And then you just forward the domain
to your main domain. Which is like
it reduces the friction.
Like if you want to get
like the clients, their first
wins without having to worry about like 14
days warm up. This is exactly what I've
done. And this is what has
worked for me.
Like just get them from instantly.
It's the best. And I've met
like they have like they're deliberately optimized.
They have like high quality
US IP accounts which is huge.
Like you always want to send from
from like IP accounts that are located
in the US. Whenever you are
purchasing like
you know pre-warmed up emails
or like you know
like instead of like just
doing that on your own. Another
thing is done for your email setup. I wouldn't
recommend you use it. Just because
I like I had like a few friends
this space. And
like they said it's not worth it.
Like it's just not worth the money.
You can just do it like in five to ten minutes.
Just like set up SPF decam.
Like that's the only thing I would recommend you
do. But from the for the pre-warmed up
accounts, highly recommend. Like
highly recommend instantly.
So the next question you have I believe
opinion on inbox
is like mail doso.
So I've never tried
actually mail doso.
I think
I think I've implemented this one
and became
like a client.
But I'm pretty sure they have like
like expensive pricing. Let me check.
Yeah. Yeah.
Yeah. So there
are smallest packages like $150
and they only give you 12
email accounts. Three domains included.
One master inbox.
SPF demark. So
I don't think that's worth it. Just because
you can get the same email
accounts by just
paying half the amount of money
basically. And it's
even if it's every three months
like I would I wouldn't actually do this.
I would just get out like I would just get my
own domains just because I want to have like
full autonomy of them. Like I want to
like be able to have my domains
instead of just having like another provider
which just gives me domains that are pre-warmed up.
Unless it's from instantly. Like
instantly is the only one I would recommend because
I've used them multiple times. But in terms
of these platforms just because
I've dabbled into them I wouldn't recommend
like you do just because
I don't have enough data to back up
if there is going to be issues
like
in the future which I
wouldn't recommend.
So yeah man this is basically what
I would do. Like if I
was in your shoes I would just
get them from instantly or
inboxology. Most
I would recommend is going to
instantly number one and then I would go for
inboxology. So
like I said yeah so
inboxes these inboxes
are fine for short term projects or
testing but for long term operations invest
your own domain based inboxes for
sustainability and control.
The next question you have is why do
you include send for my iPhone? So
this is actually pretty funny. So
this trick is pretty old.
Like when you include send for
my iPhone whenever you write your copy
like it gives impression for anyone
that reads the email that you actually
sat down on your phone and you
typed the exact
email with the personalization
with the offer and it
somehow it just
increases your
reply rates. They're like
this person actually sat
down and they wrote me this
exact email and it looks
like extremely personalized
because as you guys know we use personalization
we have like we have
tone manipulation
we know how to like you know how to write
copies in this community. So when you add
the send for my iPhone it really adds like
one to two percent you know reply
rates in my
you know in my experience and I always
include it like in my campaigns
or either in my client's campaign.
I always like add send for my iPhone
or sometimes I include
sends from my iPad.
I always do like these little
tricks. So these tricks
or hacks can increase like
reply rate by one to two percent. So I
always add that.
Do you
opt out?
Do you
opt out information? Honestly dude
I won't worry too much about it. Just because
B2B cold emails are you know are often
allowed under legitimate
interest. So you can add something like
let's say there's a
the prospect that responds
by unsubscribe or
something. You just
reply to them let me know if this is irrelevant
and I won't contact you again.
You should be fine. Like these
GDPR I think it's just bullshit.
Like I've sent like
thousands and thousands and thousands
of emails and I never worked
about like
unsubscribe. I
didn't even include like an unsubscribe link
and I never had any issues with like
a deliverability. Just because the
main thing that you should be focused on
is the offer and the core
messenger. Like
just think about it like
people or like lead gen agencies
or just people that
try like cold email.
They send generic emails such as
hey like would you mind
like would you mind hopping on a call
or we do this. So like our
emails that we send are
extremely personalized.
Like people when they read them
like the likelihood
of them
like marking you as spam
is extremely low. Like
I've never had deliverability issues
and I know many people in the space like
they are like talking about
deliverability. They're all this crazy
like
hacks, crazy tactics.
But in reality if you have a good offer
people are just going to resonate to
this offer and they're going to reply to you.
Like that's it.
Like the thing to
focus on is the basics. Which is going
to be having a great offer, great personalization
and actually reaching out to the people that actually need
whatever you're selling. So this is the
core
the most important thing that you
should be focused on. So I wouldn't worry too much
about the off-taught
information. What should be
the minimum talent size for the
niche focus? I'd aim for something like
5,000 to 10,000 leads
in your chosen niche.
Like less than that
then that would be
unfortunate
because you want to have
a reasonable amount
of people in your niche
that you want to reach out to. Just
because you want to have like a sustainable business model.
You don't want to just go
ahead and reach out to people that
you know. For example I had this campaign. I used to
work with this campaign with this guy.
And they had a red light therapy system.
Like a red light therapy
and it's so hard for them
to get clients. And it's
so hard for me to scrape them leads.
So what I used to do
is I used to scrape
Facebook ads library.
And typically I was just
scraping companies
or clinics
that they were running ads
and wanting to get clients.
And this company actually
what it does it is just
helps those clinics
get more red light therapy clients.
And the problem is
that they only work with clients that have
red light therapy beds
systems. Not
all the clinics. So
it was very hard.
I was just scraping them like 300, 400
leads per
8 hours which was tough.
And then
using the personalization
then trying to find emails because
those companies essentially
you're not
going to reach out to the founder
directly of the clinic.
It's a big
company. You're not going to just go ahead and reach out to
the founder. You're going to have to reach out to someone
a little bit lower than that.
So I wouldn't work with these
companies. I did that
back
when I just got started because I wanted to get a few clients
under my belt. But always go for B2B
and go for like a niche that has
a lot of prospects such as B2B tech,
healthcare, all of these
financial services,
banking services, cyber
security services. There's so
many leads in
these niches and they typically
have a lot of publicly
available data so you can personalize
the hell out of it because these companies
leverage social media and they post on
LinkedIn. They talk about everything.
Whatever they had, their company, their recent funding,
their milestones, their case studies so you can
personalize the hell out of it.
Would you recommend targeting one, two, three niches
once or focus on one for the
beginning? So I'm going to give you the
cold hard truth. No. Start with one
niche only because once
you validate what works, you can expand
to additional niches. I get
where the behavior comes from because I used to
do it too.
Don't go for three niches.
Just one.
If you go for three niches,
it will really, really mess up your
focus and it's going to create unnecessary
complexity.
First of all, you're going to scrape so many
leads. You're going to have to scrape 5,000
for each
niche to be within KPI.
It's going to require
you more work.
Let's say, for example, you have
multiple niches and
multiple campaigns and all of
them have reasonable
results. Some are
the same.
Put your focus on three things
so you can just focus on one thing.
If it works, great. You can just scale it.
If it doesn't work, you just discard it and go
for the next niche.
Nine out of ten times
that niche is going to work.
Trust me. If you send enough emails
and it's a B2B tech niche,
you're going to get clients.
You will get clients.
You cannot
just not get clients.
Once you get a few replies, let's say
you send 5,000 emails
and you get 100 replies.
I'll be telling you to get 20 replies
that are positive. If you manage to get
20 replies that are positive, let's say 10
are willing to hop on a call.
If you manage to close
203 for a
upfront fee for
$3,000, that's already
almost $10K, which is
like 90%
of people are now making
$9K a month
using cold email.
Just purely from cold email. Just give
that some thought. You just need to
define
your goal.
What do you want from this campaign?
How many book meetings do you want?
What are you trying to achieve?
Then, what is the
quickest way to get
there? The quickest way to get
there is to start with one niche
only, validate what works,
and if it works,
which 100% it will because it's a B2B
tech, and you know how to
personalize emails, you know how to
build cell systems,
you have an idea. You are
basically better than
99% of lead chain agencies
because they're just bullshit.
You will get clients.
That's essentially my two cents.
Yeah, man.
The next question
that we have,
let's go to
opinions on
hybrid campaigns,
linked in cold email.
Like I said, I wouldn't
recommend you do that. Just focus on
one thing. One thing only.
Just
start cold email.
Just
stick to it, and you will get results.
Because if you try to juggle
lead generation methods,
you're going to end up with zero.
Trust me. I've done that
multiple times. I've had
Chinese syndrome so many
times, and the ability for
you to stay focused on one thing, which is going to be
singular focus on one thing
and one thing only, is going to be able
to give you the results that you want.
So just give that some thoughts.
All right.
Our friend
Roger
originally posted this in the other thread.
Here are my questions. Awesome. So apart
from NIC and your own iterations,
what were the sources you learned from
on your journey?
Can you recommend any other specific
creator agency?
To be honest,
I don't
follow people that have
agencies, and I don't recommend
you guys do it, just because
there are better
ways that I'm going to give you.
It's actually what you want to
do, guys.
Let's say, for example, you have
a problem you want to solve,
and it's going to be
client acquisition using cold email.
You look at
the best companies
that do this type
of service the best,
right? You look at them.
You look for people
that are
in that space, basically founders,
right? And what you
do is you just go ahead
and copy exactly
what you want to do.
And I'm not saying you go to a
company that does, I don't know, like
25 to 50
a month. You go learn from people
that actually have
done it. And what I
mean by that is go
study people who actually have done
it, like
Google, Amazon.
Go study
a...
Go read Letters to Shareholders by
Warren Buffett.
Go read Letters to Shareholders by
Amazon. Go read
Letters by
Google.
If you read these three ones, you will
be equipped by so
many aspects that you
implement into your
agency or your cell system agency.
Because it's essentially what I've done.
One of the things that I've learned
from, you know, and the sources
that I've learned from these
great companies, is that
they always think about long
term thinking. So long term thinking
is what enabled them to make a shit ton
of money.
Another thing,
it's going to be
second and third
order consequences. And what I mean
by that is basically
you want to look at
the straightest path
that will get you results. So what
is the straightest path that's going to get you results?
It's going to be sticking to
one niche, basically stickability,
for lack of better words. Sticking to one
niche. And not
changing anything until the campaign
actually ends.
So again, we're going back to
long term thinking.
You should not
have delayed gratification. You should not worry about
early data.
Like open rates, or
you have wanted to reply
today. Don't worry about that.
Look at the big picture.
Before you start your campaign,
I want you to define what you want
to do. What is the outcome of this campaign?
How many leads do I want to
generate? How many book meetings I want
to generate?
And you just essentially
build the system that does this.
So what I've
done
in the course, in the community, what I've done
is I gave you basically a standard.
So 5,000 leads, when I'm telling you to
create 5,000 leads, is basically the basic
KPI of what a
healthy system is.
So this is like
a healthy potion of those leads
that's going to get your results.
So when you send out
like 5,000 emails,
you're pretty much good to go
to assess whether
that niche is viable
and whether you need to iterate.
So just
keep that in mind. Always study people
that have
don't study people that are
just making like 50K or 100K a month.
Study people that actually
got it. Because this is the purest
source of knowledge, right?
This is what I would recommend.
Also, another thing, I've been reading through your
Notion doc, SOPAI copywriting
masterclass. So I wonder, Indro, at the
core of it, what is your call campaign
stacking? To me, it sounds like audience
segmentation. Well, yeah.
Is it the same thing or not?
If different,
can you elaborate on the essence of that
difference? So yeah, I've managed to find a way
to explain this to you in these
simple terms. So basically, guys, campaign
stacking is like sending special
texts to your friends
based on what they're going through at the moment. So
imagine you have like a group of friends.
One just got a new job. Another
one is building a business. And the third one
is planning a trip. So instead of sending
the same hey, how's it going message to all
of them, you send
congrats on a new job. Let's celebrate soon.
Good luck with the new
business. You've got this. Need help?
So each message is tailored
to what's happening to their lives. And campaign
stacking is basically taking a big niche, such as
B2B healthcare,
right? And you scrape those companies,
right? And you try to
when you're scraping those companies,
you kind of like categorize
like multiple lead lists.
So let's say you have, you're going to scrape
like 6,000 leads.
You're going to have 2,000 leads
from this
niche, but like you're going to
be a little bit targeted. And you're going to
So each message is
tailored to what's happening in their life. They're more likely to
respond because it feels like you're paying
more attention and actually care.
So this is the same way that campaign
stacking works. You send out emails
that fits what's happening in their lives.
In this example, like we said,
what's happening in that company. Like, but
disclaimer, big disclaimer,
you do not need to do this.
You know, instead of focusing on the basics,
this only works well if you've been working
for a client for a long time
and you know the niche is very
well.
So for example, I used to use this when I
was targeting healthcare IT. And
I used to basically
filter out companies that
are startups and then companies
that were established and enterprise.
And then all of them, I would
like reach out to them to
get a specific campaign and with a different
tone and a different offer.
So exactly this is what campaign stacking
in the most simplest form.
So I hope this helps.
Yeah.
Awesome. So the next thing.
In the same doc, you talk about
using breadcrumbs to make
inference to write hyper personalized
emails. These are great questions, man.
Could you please expand on how you pulled this
off a scale? This seems like something that
can easily done by a human, but I'm still wondering
what prompt automation you used
and where your
data sources. So I'm going to give you exactly
what you need. The entire system
and how you get the data and also the
like an example output, right?
Because I've ran this campaign back
in September
basically. I think I got like
30% reply rates.
So first of all, we're going to talk about where do you get the data?
So if you scrape
like LinkedIn using PhantomBot, so you're going to get something like this.
So you're going to get
company name, title, a regular company
name, summary. So the summary
is huge. You have a
big summary. So with over eight years of recruiting
and sales experience, I have a proven track record.
So people are just like talking about their Hawaiian
title.
You have the industry.
You also have the company location.
You have duration enroll, which is
extremely
great.
Duration and company, past experience.
So now, since you have this data,
what you can do is instead
of generating just one line icebreaker,
you can use this data
and add it
and relate it to your service.
So I'm going to walk you guys through
what a great
personalization and what I mean by
inference and personalization.
So basically what it means is just
making non-service level
observations. And what I mean
by that
is if I go back to my
article, like I said, so
once you relate it to your service,
so what I used to do is I take
that data and I relate it to
connecting them to their ideal client.
So once you spot these
breadcrumbs, frame your message in
a way that positions your service as a
solution to their potential pain points.
For example, congrats
on X recent success with that
clinical trials provider project.
Curious how you're planning to leverage that experience
to attract more healthcare clients.
Any specific challenges
you're facing in that sector.
So this is what I mean by inference.
So if I go back to this lead list,
so the lead list, I think I've scraped this
back in September, like I said.
So just read through this.
So, hey Donald, congrats
on expanding your medical IT services.
That's huge. Curious how you're tackling
the unique compliance challenges in healthcare
as you scale with more long-term clients.
So as you guys can see, this is what I mean
by really great personalization.
Oh, I remember I still have the system.
So I'm just gonna like
leave it in this one.
And you guys can go ahead and use it.
I'm gonna read you another one.
Go ahead and read this one. So,
hey Katie, congrats on the recent growth in your
healthcare client base. I'm curious, what strategies
have been most effective for you in
securing long-term partnerships with
high-value healthcare organizations?
And I believe I used to say something
like, hey, I'm Saad and I'm neurotically
obsessed with Cold Outbound
and I built this sales system that generates
X amount of book meetings for companies
like, would you be
interested in more information? So I still
know
like I still don't remember
to copy by heart just because I know it worked.
So yeah, this is what I mean by great personalization
and this is the system that I've built for it.
And like
you don't have to use Phantom Buster
for that. You can just
scrape their
company's webpage.
And I think this one,
let me see.
I think, yeah, this is the one.
Yeah,
I think they're both similar. I'm not really
sure, but yeah. So I'm just, you know,
I'm researching the rows.
Just like I showed you guys,
I'm just using
my API key
and then I'm validating the email. I'm parsing
the results. If it's deliverable,
right, I'm scraping the website
page. So I'm scraping the website page
and I have a personalization.
And the personalization, I'm going to walk
you guys through it.
So craft
a personalized outreach tool line
icebreaker based on
powerful
references. The message should be
directed at a potential client
using publicly available information to
connect the dots and demonstrate
an understanding of their unique situation.
So as you guys can see, when you talk
to these models, you want to be as specific
as possible. And I'm
going to include this in the Blueprint
library.
And I'm going to compile this
in a Google Doc and add it into
a thread just after this call.
So this is
what I mean by that. So the message should identify
meaningful data points such as
password experiences,
public ideas they share, or
initiative they care about. Relate these
insights in the service being offered.
Building sales systems specifically designed
to connect them with their
dream clients in a non-
obvious value-adding way.
Ask a thoughtful question to confirm the
inference and open a dialogue.
Rather than assuming the need
is fully understood. So this is extremely
long.
As you guys see. So I'm
feeding in a bunch of examples
right here.
And then I'm basically just adding
I believe this data was just scraped from Apollo.
So as you guys can see,
you can just go ahead and scrape that from Apollo and
just use the Leeds website
scraped data. You don't even have to use
Phantom Buster. Yeah, and this campaign
has crushed it. So this is what I mean by
inference. So I hope that helps.
Roger.
Which person is- I'm going to read
through these questions after I finish
these.
So yeah. Anyway, so
let's just go back to the questions
right here.
I'd like to ask you for a
retrospective. Back in August, September,
what were the key factors that made you scale
beyond 15k? Yeah, unlike many other
make money would make members
that kept out somewhere in that neighborhood,
not only ever closed single deals.
Yeah, there's many people in the
community that have never closed the deals
and make money would make. I don't think
I ever heard you speak about your journey from the first
15k to 45k. So yeah.
I've worked with crewmen all the way to 15k
and then what I've done is I've noticed
a lot of people were like targeting them.
So I was like, you know, that's
okay. I'm just going to go ahead and I'm just going to
target another, like another
niche. So I went ahead and I targeted
IT healthcare
companies that's, no,
healthcare companies that serve
IT. So this is what I've
done. And the only
thing, like
you think it's going to be some crazy
scaling strategy
but no, it's more of the same thing
basically. It's more of the same thing.
So now that I've noticed that my system
and systems are working like and I know
how to basically build campaigns
that takes me like two to three hours
to build. Now it's
the only thing that I should be doing
is scale. So this is
why I'm telling you guys that it's so
easy to get clients as they
sell some agencies. It is so easy.
Like you only have to
just
delay gratification
and trust the system. Don't change
anything. Just start your campaign and change your
entire belief system that this outreach is
going to work. Because a lot of us, like
I understand where the behavior comes from.
I used to think that way.
Just to get scared of cold email.
I'm not trying to sound
cool but I used to
it's limiting beliefs
that this cold email is not going to work.
Or this
message is not going to work and you go
ahead and change it and let's say
like a lead replies but
deep down you don't want to get on the
sales code because you're scared of rejection.
All of these are, you know, untapped
belief systems that we have inside of our
mind. And we need to crush them.
And the way you do that is
you craft a new identity of someone
who actually gets results using cold emails.
This is the main thing you should be
focused on. And that identity
of that someone is someone
who is delaying gratification
focused on one niche only.
Whenever he gets
replies
he just pulls down on what works.
Let's say you get six replies
or seven replies in a week.
That's pretty good.
In my recommendation, that's pretty good.
Just add more leads.
Add more leads, add more leads, add more leads.
And as you go
each week, you're going to start
to understand a pattern of your offer
and you're going to learn
you're going to quickly learn
what your leads want.
And you're going to be equipped
by more
objection handling abilities to reply
to them and answer them into a call.
So this is essentially what I've done from 15k
to 45k is this essentially
scale.
Sometimes you don't need
this crazy idea.
You just need to change your belief system.
And your belief system is
basically adding more
and focusing on one niche.
This is everything that you should be doing.
Everything, there's some
crazy
concept that you should be learning
or this crazy
skills that you should be having.
But in reality, it's not.
So everyone can do it.
So I hope
you guys are having
our finance and value in this
So if you guys are still listening, just
ping me in the chat right here.
It's going great bro.
Alright.
Awesome.
Roger.
This guy is so funny.
By the way,
how are you guys spending Christmas?
Mine is going to be
watching polar express
and eating chocolate chips cookies
because they're the best.
Anyways, so let's go ahead
and finish up.
So
yeah, I think these are
the entire questions that we have here.
Jason, so I can't make it as I have a
family obligation. I'll definitely watch on the 26th.
Hope you're having
a great time with your family, man.
If you could have time,
would you go over and find an ideal
clients message to get the 10 bookings
for a client? For example, I haven't
closed the deal, but our equipment company said there
are current clients and I'd need to target
similar ones. For now, here's the five
companies that we are currently working with.
And the requested
allied help,
physiotherapist,
physiologic, speech pathologist,
et cetera. Yeah, man, I'll
definitely do that for you, 100%.
Just because I don't want to
be that guy that just
puts up a roadmap
in a community and doesn't engage
with people in a community.
It's like a family here, so I'll definitely
help you with this campaign. So rest assured, I'll
send you a DM afterwards.
I'll try to give that some thoughts.
Yeah, so let's go back to
the chat and now it's time to answer you guys
lovely questions.
Awesome.
So
yeah, let's go
on top.
Follow up question. Regarding
if you warmed up emails,
don't clients ever wonder why
the domain is different than the main one?
Honestly, they don't because
I always
make sure I front load that
in the beginning. So in the sales call, what I'm saying,
I'm like, hey, I am a growth system.
I'm not your typical lead generation system.
What I do is instead of
waiting time on warm
up, what I'm going to do is I'm going to deliver you
the first book meetings
during this week.
I always frame it
like, hey, lead generation companies are like,
while they're busy doing
warm ups, we are here getting results.
Essentially, I just frame it that way to make it
super funny for them in the sales call.
This is how I frame it.
They don't really care because they only
care about a book meeting
in their calendar.
What I've done is
there's this campaign that I ran a month ago for a client.
It was a B2B SaaS
company, B2B Healthcare
SaaS.
What I've done
is they had an app
and they had an offer
which they gave, I believe it was
14 days free trial.
They had
an offer which they get
maybe like 50% off
during that month and they wanted to run a campaign
instead of running ads.
I just
forwarded the domain
name to one of the
domains that I had. There's a little hack
for you guys. If you want to
keep 90% of the
margins and absolutely
make money, like I've done, just
because there's no gatekeeping in this damn community,
there's something you can do. You can have
pre-warmed up emails of
your own domain.
For me, for example,
if I go to my Instantly,
I have a bunch of emails
as you guys can see, 100%, 100%.
I don't have issues with deliverability
just because I know the offer is going to great.
For example,
here's a couple emails that
are still warmed up and
just ready for me to start sending.
These
emails,
what I can do is I can have a
client and I would onboard a client this week
and instead of me just worrying about
pre-warming the email, I can just
go ahead and send the email from this email
without even having to
forward it.
It gets to a point where
you want to get the
clients the first wins
because you want to just
retrieve from them as much
cash as possible
where it makes
sense to not basically
buy them the
domains. So I used to do this too.
But I would recommend you do it at first.
Just get a few clients at first and then
because there's a chance,
there's a small chance they will
give you questions about this
and you might not know how to answer it.
I have a leeway to
answer this so I would just recommend
just go ahead
and get them the
pre-warmed up emails
or just buy them the
domains and everything
with their own name.
Okay, so how do I set my call.com
booking form based on the US time you want to live in
in a different country? It will be more convenient
for these clients to book according to their time zone.
Yeah man, so if you go to
call.com
you can
set this up pretty easily.
So you go to go to app
and you go to
so I have here
let's go intro call. So this is the
one and I believe
let me see
apps, I think it's advanced.
So essentially
what you can do, clients can just
go ahead
and try and
input whatever time zone they are in.
I believe you don't have to worry about this.
I've never had an issue with this. So if a client
lives in Europe or
lives, I don't know,
America
just use Argentina
call.com
is going to
put this based on their IP
so you don't have to worry about that. I'm not sure what
your question is.
Just follow up with me in the chat.
How do you follow up on these
hyper personal emails linking
clients status queue to service? Is there another personalized system
or pre-written response patterns? Yeah
there's pre-written response patterns that
I've included in the community. So
there are follow ups from
like hundreds of my own personal follow ups
that I personally wrote. And they
handle everything. Like literally everything.
Like what is this service about?
Can we know more?
What is the price?
You know, notes. You never say
the price on the email. Because any
price is too much for a client. Any
price. So never say that.
Just go to the blueprint library and
just go ahead and read through them. And if you
don't know how to, I can just link you the
documents. Like no worries, my guy.
Which personalization to use? The one you provide
in the templates or the one that AI
does in automation based on the prompt?
So it really depends on the
outputs. So
so it really, like I said,
it really depends on the outputs and
like sometimes
there's not one single
response for it. So depending
on, okay so I'm going to show you guys a little
hacks. So depending on the
personalization that you use,
example what I would do, like if I was
you, I would go to my
prompt right here.
And then I'm going to
copy this one. And I'm going to go to
Claude.
And what I'm going to do,
I'm going to do something like, oh so
there's defaulting to, okay
we can't use Claude's in that.
So I'm just going to go to chatgpg.
So what I'm going to do
is I'm going to feed in this prompt. And depending
on the niche or industry that I'm
targeting, I'm going to say edit this
to the specific industry.
So for example, let's say I'm targeting
cybersecurity. So I'm
targeting
cybersecurity
niche.
Your job
is to edit
this following
prompt to be tailored
to this
specific industry.
Prompt
or let me do
original
prompt.
And then now you
add your job
is to edit it now.
Let's see.
Edit the prompt
only
only
the results
should be edited
prompts tailored to
the specific
outlined industry
cybersecurity.
So there you go. So now
what you can do
is you can just take in like
this prompt and you can just
edit what you want depending on the industry.
Does that make sense?
I don't have stripe because
it's not available in my country. Can I use PayPal
in my proposal? Yeah, you can definitely use PayPal.
I just don't like PayPal just
because they're like
they essentially
hold your money or something. Like I heard
a lot of people that they heard their money.
But yeah, you can use PayPal like
U.S. companies and U.S.
founders they use PayPal.
You can still use wise.
Although I would recommend stripe. But
I would if you
if it's not
available and you don't
like let's say you don't have the funds or like you don't want to invest
in like illegal zoom
say address. That's totally
cool. You can just use PayPal until you get a
few clients and they can just use that money
and reinvest it back in the business. So
yeah, you don't have to worry about it, man.
I'll just get up and running. With the third
question I asked
I was thinking more about mentality
approach preserving
or modifying something. How your habits
or beliefs change as you were growing your income
so rapidly. Yeah, man. So
the main thing that I want to talk
and I'm not
I'm not trying to
show everything. It's because people like people I
notice a lot of people are trying to do so many things. Like so many
things. They're trying to up work. They're trying to do
LinkedIn outreach. They're trying to do cold emailing.
They're trying to do cold calls.
They're trying to grow social media.
And you don't do anything at the end.
And you
let's say you send a couple emails
and you don't get any
replies and you're like this cold email doesn't work.
Let me try Instagram DM.
Let me try LinkedIn outreach.
And it doesn't work and you go back to cold email.
Like I get where
the behavior comes from.
Like I used to do it. Like it's
we are so used to instant gratifications.
We are so used to basically
expecting results too quickly.
When in reality it takes a little bit of
time to stabilize. So the results of the
campaign are not going to get in the first week.
Just keep that in mind. You're going to get them in the second week.
And sometimes it just like
goes in average average and it goes
whoosh you know. So
like you have to understand this. Right?
So I would always
like have like
I used to write
like it's
weird but I used to write like entire
my Google Doc of my entire belief
system that I like had about
outreach. And I noticed that I
didn't even fucking
excuse my French believe that it was
possible. So my
unconscious was afraid of
outreach. Like I was afraid
of sending cold emails. Just because
I have like an introverted
personality. Right?
And the way I used I have
I had to like overcome this.
It's basically
what's called an integration.
And without getting too deep
about this what I mean by that is
you need to like I would encourage you
guys just put all
your thoughts in a Google Doc about
everything you know about cold email
outreach. And just be honest with yourself.
Like this is an exercise like
between you and yourself. And once you put
them in a Google Doc they're not your thoughts
anymore. There's just some random guys
thoughts. And your job now is to judge
heavily and see like
where the issue is. Like nine
times out of ten
you're gonna see that you have
so many limited beliefs that
are basically
not
letting you continue to do the work.
Right? So yeah.
Another thing is I focus
on sales systems. Like I wouldn't
recommend you guys
you know build
automations and sell them through cold emails
unless it's a sales
automation. And I'm being real
just because
there is no person that I know
that builds automations and sold them through
cold email unless it's a cold email system.
And this is just the cold hard truth.
Because clients don't care about
a proposal generator system or
like a dashboard
tracking system or like a
RAM. They only care about clients.
Right? So what is
the final output? Right?
That a company wants is they want a
client that pays them and they pays them
on time. So if you can automate
this, that's it.
Like that's the end. Like literally
that's the end. So like this is why
I understand that's like
fairly quickly even though like I've made many
fuck ups. But
I understood that hey like the
highest leverage that you have
in cold emails
or sales systems and leverage in AI
and personalization is you can get clients.
Which is going to be the
which is going to be like a sustainable business
model because clients
is the
basically how a company puts foot
in the table. They will always need clients.
So I don't know like
the end
of earth. So if that makes sense man.
Do you recommend doing outreach now
or should we wait until people are back from vacation?
So I would I would just
so Carla just she just
posted
she just posted something like
don't do outreach
I think it was December
25th. So don't
send emails between 25th and
January
1st of January.
After the 1st of January just blast
those emails.
Could you run a lead
generation business using a model for business to customer?
So if you were an air conditioning company
would you find potential customers for them?
Yes Mick.
That's a really good question man.
You could
you could and you have
really good leverage because once
you find the right copy you can just
replicate the same in each
city. So I know
this guy is called Matthew Larson.
This guy runs a YouTube channel
and he managed to scale a
cleaning company using cold
email and
he also ran
Google Ads but he mainly
using cold email.
And he managed to scale all the way to
like 5 million a year which is
crazy. And these models
you can definitely
build cold email systems for them.
You just have to know where to
get the leads. And the way
you're going to get those leads is basically
going to be either
companies
or actually you're
saying business to customer.
So it's
either going to be basically
I'm going to give you like the
noble shit way and it's going to be three
steps. So you need to establish
a little bit of social proof and what
I mean by that in order for your cold email
system to work you need to have
some social proof and a
little bit of a brand.
So when clients or customers
are Googling you
you already have established
a little bit of social proof on the internet.
That's
huge.
Because
business to customer
is definitely like
very different than V2B.
So what I would recommend is
you would
basically run campaigns
and you would get leads
let's say from Facebook pages
you would scrape Facebook
comments just
on top of my head. How would I get
this? So what I
would do if I was you is
I would look, again we're going to go
back and implement something that the great
companies have done.
So look for
a company that is
let's say an air conditioning company
and then I would just see
basically what they're doing.
I would
find five companies and then
I would see their lead generation
methods and I would also
see if they are running any
type of apps. I would see
one thing I could do
is I would go to
their Facebook pages and I would scrape
comments of those people.
And I can just personally
like the hell out of it
by taking those people.
And I would just
find those emails and I would just
reach them out. Something like hey
I found you a comment and
X, we have a better offer.
So that could
help too.
So once you
find the right offer, once you
have the right copy
you're going to
be good to go.
But keep in mind that business
customer,
although it will definitely work,
you need to look at
companies that have done it the best.
So this is what I would do.
Do you spend
tax for non-personalized male body?
Yes. So it's either
going to be hey or hi or thanks
for the time or thanks or
sometimes I would use like a different copy.
I would like slightly change it.
And
the course
material,
you
get domains
with three inboxes each. Is there any
reason not to start with more domains or even increase
the number of inboxes per domain
to increase the volume of emails?
Yeah, you definitely can. I'm just
giving you like the standard of what
you would do if you want to get started and still
get results. But if you want to scale
past that, be my guest, just go
ahead. Because you're going to
need those domains and emails at the end.
So yeah,
do you guys have any more questions? Just let me know.
In terms of
your question, Mick, I will
help you out. I'll see
what I can do. I'll see if I can
show you like how to
do it, basically.
It just requires a little bit of research.
Ideally,
with how many mailboxes
we should start with? So
ideally, you would start with
ten mailboxes.
The least.
Ten mailboxes.
Just because you want to, you know, start
sending emails and
you don't want to get into spam
at all. Like you don't want to
distribute as many emails as you
can in different
inboxes.
And you're not going to, like, you're not going to get
any deliverability issues if you use the high
ends and CC sending. Because what
you essentially do is you're ramping up
and then you chill
in a little bit and then you send it again.
Which is the best way to send
emails instead of just gradually
sending.
And make sure you always include a hundred
percent reply rate and the
instantly set up. Let me show you guys how to do it.
So go to instantly and go to, let's say
this example, and then go to
settings. I just added
this campaign right here.
So go to settings and always make
sure when you start your campaign to have
your reply rate at a hundred percent.
So what this means
is that the warm up emails that you're going to be
sending along the emails
that are in the campaigns are going
to be a hundred percent replied to.
So that means once
you send those warm up email from this email
the other party which
is going to be the server that sends the warm up
to your email account
I hope this makes sense. Let me know if you guys
understand. They're going to send
you back the email so that means they're replying
to you so that signals
to the ESPs that you're getting
replies. Does that make sense?
Let me know if it doesn't. I hope
it does.
I'm afraid
I'm not tracking.
So let me explain this to you.
So there's one
email.
And there's another email
which is going to be the Instalink server
where they send warm up emails.
So once you start a campaign
this guy right here is going to send the emails
and
it's going to include
the warm up emails
that we're going to send to this guy right here.
So this guy right here
has to send back
an email. So make sure it's 100%
right here. So we always
send back an email to this guy
here as he sends in the
warm up emails along with the campaign.
If you don't understand this man
So for setting up the mail infrastructure
we would ideally use
warm inbox from Instalink and
warm up Google inbox simultaneously.
Yes. That could work
too. Honestly
if you get out, depending on the
setup fee. So if they pay you like
let's say more than 2k
I would just go ahead
and buy the premium. Like I'll just get
the domains. I'll just get the
pre-warmed up domains.
Because it's a huge leverage.
Because now you're investing not only
in your client success but your success
too. Because it reduces
the hell out of the friction.
But yeah. If they pay you only
like 1k then no.
I know this
platform.
They also offer
premium
inboxes.
I mean
there's a million
one. Like
this. 350
It's essentially the same pricing.
If I go to choose my package
they have beginner
plan. They have 250
inboxes.
Complete technical setup. I would definitely
try these. Let me know if you guys
want me to try these and just get back to you.
So yeah. I would definitely
So scale plan.
What is the
pricing that they have here?
So one
inbox is $3.
So if you
take in like 10
inboxes. That's $3, $5. That's pretty
good actually. But like I said
you don't have full autonomy
over inboxes.
Like I would only get this if I have many clients.
And I need to like fulfill them
pretty quickly.
But yeah man. It's definitely worth a try.
Would you recommend
No. I wouldn't recommend honestly.
Like I would always either
use from instantly.
Or use inboxology.
Just because I don't want you
to just
get any issues in the future.
It will be my recommendation.
So let me just try them. And then
I'll tell you exactly
if they are good or not.
So that way it's way better.
Because I want you guys to win.
And I don't want to like
just give you something that I haven't tried.
You know.
Yeah rich inbox
is pretty good too.
I would add it
to the inboxology.
So yeah man.
We've already. Yeah we've already
It's like 6pm now.
So basically we just finished an hour.
Enjoy the chocolate chocolate. Thanks man.
So the last question I'm going to answer before I end this.
Is because it's Christmas.
So do you not like smart leads?
Smart lead is good.
But instantly is way better.
I would always recommend you guys use
instantly. Just because
I've read into
somebody's smart lead before.
It's a little bit cheaper. It's like
a couple dollars cheaper.
But I would just recommend instantly.
It's way better.
Because smart lead has
some deliverability issues.
Alright guys so if you guys have
any more questions just add
them to the thread
that I'm going to post after this
call ends. So yeah man.
Hope you guys have some value in this
weekly
call and see you guys in the next
one. So yeah thanks so much
for coming guys and happy Christmas.
