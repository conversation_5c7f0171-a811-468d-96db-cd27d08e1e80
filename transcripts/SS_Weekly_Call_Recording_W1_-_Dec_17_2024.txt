hello if you guys are here just put something in chat hello everyone hey
hey well how you doing how you doing man you guys great you can't you guys can
just put your messages in the chat so I can just read through them let's wait
for everyone to come before we get started
how you guys are doing
hey leon hey axel my friend hey <PERSON> hope you guys are doing well hope
everyone is having a great time what's up
broke past my niche block today finally man what did you end up choosing
tech recruitment yes actually I'm gonna answer your your question is actually
the first one so I'm gonna give you some like some insights just because you did
the best you picked the right niche like rest assured awesome so we should
get started just because I'm cognitive everyone's time and I want to answer all
of you guys's questions and then for the last 30 or 15 minutes or so all of the
questions are going to be answered in this chat what's up <PERSON>dar<PERSON> happy
doing man all right guys so let's just go ahead and get started with the first
question so the first question is we have from the lovely <PERSON> so I've been
thinking to choose one of these niches to dive into just not sure how narrow to
recruitment tech recruitment this one I have the best gut feeling for well
that's the best one actually still find too hard still fine so hard to find
50,000 emails or technical recruit only 3k decision makers full stack tech
recruitment not easy to find a specialized CTO headhunters I think not
a lot of repeatable stuff too much relationship based another niche I'm
interested in B2B SaaS companies well a B2B SaaS companies are always going to
be a good niche just because there are many companies that are every day there
is new startups every day and then well it should it's a double-edged sword to
be honest but but I feel like they'd be cash-strapped not sure also it's
probably not even a niche itself well yeah it's a it's a task after as a
service it could be any softer really in any industry so I'm gonna answer your
first question my friend so like I said I've been taking some notes some notes
like 10 minutes earlier so tech recruitment is your best bet just
because I've made like 15k in one campaign back in probably September
just in tech recruitment just because you got to understand that finding good
talent is a like a constant struggle for companies especially big companies that
have money they always are looking out to hire better people especially in IT
and like basically software developers all of these job roles they pair really
well and companies are willing to spend a lot of money and a lot of capital they
allocate like a lot of capital for these hires so I would definitely recommend
you go for that they also understand cold outreach just because they do it
themselves they they if you notice like a lot of recruitment they're constantly
on LinkedIn so what you can do is just can build that build like a cold email
system for them and it's pretty easy to get them the results actually just
because they would have like candidates so just let me give you some insights so
in terms of recruitment they typically have a lot of candidates already ready
so what you do just look from companies that are already looking for those job
titles like it's pretty straightforward guys so we just use something like I'm
gonna go to epiphy and then let me share with my screen with you guys so I can
show you the exact scraper you could you be using so
awesome so you would go to epiphy basically and then let's say I want to
get those results for these people I would just go to basically go to console
and then I'm gonna look for I'm gonna go to a store and I'm gonna look for either
LinkedIn job scraper and it's either going to be from this guy so this guy
right here and then this guy this guy's actually a really good scraper right
here it's so known this first quarter guy he has higher rates but you can see
like the cost per acquisition that's just if you get one lead like just one
lead for them it's basically like gold just because they spend a lot of time a
lot of money to find and allocate to find like people companies that are
already hiring for these jobs just because they they're constantly like
relying on job boards they're constantly on LinkedIn and if you can just automate
then their entire outreach and that's pretty cool so what you do you can use
so he has if you don't want to provide cookies you can use the public job
scraper so the cookies is basically just this edit this cookie extension that I
showed you guys so basically just like an extension that you download in your
browser and you basically just extracts the cookie and the cookie is just your
session cookie basically like an ID that have a crawler that just logs in into
LinkedIn right and then just retrieves the data as in you so you would paste
in here your session cookie and then you'd post like you post like here the
job search URL so let me actually do it like in front of you so you can just see
it let me stop presenting and then I'm gonna present my entire screen awesome
so let's just go to then I'm gonna show you how you can do it so you'd look for
LinkedIn jobs and then let's say you go right here and then you would go and
look for for example you would look for let's say they're looking for a software
engineer you just go right here and you see like a lot of companies look at
these companies there's so many like 14k results 14k results what you do is just
go back and you copy this URL and then go back to your scraper and you paste
that in here and then once you have that you would go basically to the extension
that I showed you guys and it's going to be edit this cookie and then you copy
and paste this right here so cookies call a copy to clipboard and you just
paste that in here right the second thing you would do so it's really
depends so if you if you want to scrape more details to just it could be like
benefits hiring team company info like if you do this you can personalize the
hell out of it you could have like a better outreach but yeah if you if you
want to it's going to be a little bit slower and then you can even scrape the
skills requirements this the company details and for the total number of
records basically you just go ahead and pick how many records you want to use I
wouldn't recommend you go over seven to eight hundred like per hour just because
you're gonna get rate limited make sure you always use for the proxy residential
always use residential don't use the data center because you're just gonna
get rate limited like easily and then for the wrong options always make sure
it's no timeouts okay always no timeout because sometimes scraper will just
stop and you don't want to do that you want to always make sure it's an
infinite run that finishes and you would start it would look and you would click
on start and you would just scrape those data so that's pretty much it how do we
do this yeah and another thing you could be used and it's good you could be using
indeed like I said guys like Epiphy is just a goldmine of scrapers like it's
just amazing like there's so many people and they're all competing at price so
this is the best thing about it any customer is the one that's getting the
best scrapers another thing you need to go to link indeed and you find this one
right here like a lot of people are actually using it and there's this guy
right here and there's other ones like paper results but you know you can go
ahead and just find the script yourself so yeah like I said they understand
called outreach so you can build out a system and they have the budget so tech
companies pay well for a common like I said so like I said there's a few hacks
use recruitment metrics care about so these are like what I say when you guys
are sorry if anyone is targeting basically our recruitment like it's
pretty big guys like you don't have to worry about people like you know step on
your foot or something just go ahead and target that a lot of people actually
are targeting a recruitment and there's many people right here that are doing it
so you would use metrics they care about because you know since I know a lot of
our command they have the big care about time to fill offer acceptance rates all
of these are you know metrics they care about you'd also use words and keywords
like candidates like always guys always use present in candidate the clearest
offer you could go in just present in the candidates in front of dream hires
like it really gets you in front of the door like in the door easily with
recruitment it's kind of like saying to a company I'm gonna if you guys watch the
last video I've talked about how you have a having a clear offer is really
gonna land you meetings for you and your clients and the way you do it is you if
if like if a lead answer like answers your email for example they're like hey
what is a qualified lead you'd say something like a pretty qualified lead
is a decision-maker that is interested in your service and willing to chat
more over a call that's the clearest offer you could do and it's it sounds so
good for the clients just because they're like okay like he's gonna
connect me with someone and that probably is going to be a decision-maker
which it will and that person is gonna hop on a call and my sales team is
basically just gonna do like it's gonna pitch them their service so in reality
you're just like reducing the sales cycle for them like extremely
exponentially and you really justify the let's say like three thousand or
like two thousand dollars they're they're paying you so I hope that this
makes sense for you guys yeah so let's go back second question Lucas I'm the
first one I thought it was nine PSC I hope you are here man if not that's okay
you're gonna have the EOD in terms of the next question so Sven any thought how
to deal with GDPR specifically regarding scraping and storing emails etc from
Norway live in the in the live there and I have been thinking about niching down
to the Norwegian markets man not sure why maybe just because I think I'm a
better seller in the region it can be a bit easier to break the ice not to do
language challenges I've worked with English as the main language for 10 to
15 years also thought you would be easier than the US in terms of
company structure yes honest I would just I would love to just do the US but
for practical reasons I guess something can you know it would be better I
remember reading about GDPR somewhere I think it was topic somewhere many would
make you might have been seen it but I think I should be careful about this or
should I ignore at all just missed the call today I hope I can make it to the
next one so let me tell you something man like like a lot of people will tell
you that GDPR is sending email in reality nobody gives a shit like unless
like unless you like spamming those people and like actually like sending
them spam emails pretty much good to go like and I would recommend you actually
target them like just because you know the culture like we said you speak the
language so it's easier for you to close and it's easier to handle money since
you're in Europe right in the business stuff the bad points are it's a very
small market you will limit yourself so I would I would just go ahead and target
companies everywhere because just companies everywhere I need tech
recruiters and like you said you have it really good English skills you're you
know and you asked for like 10 to 15 years so you were just from like our
interaction I think you're pretty smart guys so you're gonna do well so my
honest advice would be like start with tech recruiters anywhere in the
anywhere in you like don't limit yourself to just basically no way like
don't do it because you're gonna it's like there's really two cases it's
either you're gonna crush it there I wish I know you will and then you're
gonna go to the you're gonna go abroad anyway so why not just do it from like
the get-go like why go for the you know for the little pieces just go for the
guide the whole entire fucking pie excuse my French so yeah like I always
say it's like always go for the big pie don't don't go for the crumbs all right
so another question by our lovely friend hey saw just watch it back now had a
client meeting during during climate and I just wanted to say thanks for the
recording awesome angry stuff good we have a list of every system that we
should master for ourselves as an agency yes man um I'm actually was
thinking about like adding like another course for you guys just because I want
to as to so there's really two ways you can do it and if there's two ways you
have done it so it's either you sell those systems completely hands-off kind
of like those grosses that I've built and you what you do is you build those
systems and you promise like a an X amount of leads let's say for example
let's go back to our whimsical right here and I'm gonna process chart this
for you guys so what you do and I've experienced this before and I've had
tons of success with it and recently I've been approached by a Legion agency
and this is exactly what I've done so what you do is you build the system the
system right then instead of promising so instead of promising let's say 10
leads in 30 days you save yourself the household and what you do is basically
you offer them the system build let me remove this one so you offer them system
build on make right which you can template out like pretty easy using the
systems that I've outlined and all the the blueprints that we have in the
community you could offer them system build and instead of 10 leads you'd
offer them something like five leads right five leads a month or let's say in
30 days you'll get five leads pre-qualified leads and what you do is
you sprinkle a little bit of a salt on this one and you try to frame it in a
way that they're getting in the make.com build and they're getting five book
meetings and what you do is you essentially add a little bit more value
by recording an SOP like walk through a view of you of you explaining the system
and then what you do is you add like another thing which a text SOP kind of
like this one so let me guys let me walk you guys through it so it's SOP this one
just like it is so you would record like like an entire SOP for them and you
would have like a text document you could use just GPT for this actually
like I've used like a 60% just at GPT and then you would put the tools they
need and then loom video like I explained to them like how to do it and
then steps to build the system basically all of these and then I always like you
know try to make it as aesthetically pleasing as possible and then I record
them a walkthrough and then it's kind of like they see like multiple things if
you guys understand like they see a lot of things they see text SOP the video
and you're still delivering five book meetings so that should like free you
with the house the hassle of like let's say it's a pretty tough niche and let's
say you don't want to get them ten book meetings you're not sure you're actually
gonna get them and you don't want to refund them so you can use this type of
flow right and these are the sales systems that you could sell and
basically you can build those flows in like I don't know like 30 minutes guys
so it depends on basically their offer and everything but if you have the
system that we have here like these are the best systems on the internet like
there's many hacks that we have in these displays just like the finding the CEO
names just that we have and all of the resources that we found that we have in
the community so yeah so I would just do this and then in terms of basically
like like mastering the systems I don't think you need to master anything I mean
I'm not really a master anything I just know how to deliver the lowest hangings
fruits and all of you guys like you guys are more than like make more than like
30k or 40k just as sales systems agency automation engineers or like agency
owners you don't need to be like a goddamn business Warren Buffett crazy
Benjamin Franklin nerd you just need to like find the highest leverage that we
have which is the templates and basically just drag-and-drop models and
use the framework and paradigm that I taught you guys which is basically
whenever there is a system that's working you just scale it you never touch
the system because you're only trying you're not trying to optimize anything
you're just trying to basically find a proof of concept that works and you just add
volume to it that's all what scale is and like essentially in principle so I
would use like apify like I said use multiple scrapers inside however you
want and then you just use like 80 20 make like I explained to you guys in the
classroom how to manipulate API's and for the ones that you're gonna use is I
called it the aim system basically the Apollo males autos oh and has had GP in
GPT-4 system and then the LPA which is linked in Phantom Buster and email
finder and then you would retrieve the scrape that I scraped the data using a
HTTP request and then basically you would just feed that data and you're
like true the latest case studies and you just put that in a template and then
the usual like sales is a master's flows like companies higher person roles
it's pretty much like 90% there unless unless like the projects completely
ad hoc which you can basically just find leads kind of like a flow that I've
built for someone community for the name they wanted to retrieve data I believe
from a website what you can do is basically just find us like make a nice
here across to that website and then just scrape that data as HTML and then
their user like AI which I think it's the best way like just use AI and feed
that a she and that that text data so it's going to be HTML and you're gonna
be using something like let's say I don't want you guys to see this is an AI
responder that I've built for the community so you guys can use it in your
campaign so it's gonna be up soon so I'm gonna look for HTML text so you'd have
like in a HTTP request right so you'd make a request for example and then what
I'm gonna do is I'm gonna put this right here and then you'd make a request and
then you'd have like a text parser which is an HTML to text right and then let's
say I'm I'm gonna use my website right
and then what I'm gonna do is I'm just gonna feed in the data right here as a
right and and you would basically add Claude with create a prompt and then you
would add this and then basically what you do is you're like always use
basically some nets this one right here and for max token which is 3060 look at
a message the role user contents it's going to be text always and then you'd
say something like using the provided scraped data and you would map the text
I would say something like retrieve the latest case studies the latest case
study or let's do something like retrieve the latest most relevant study
and then what I'm gonna do is I will case study and then it would say
something like outputs the results only and one one to two lines max use a
casual language
with a Spartan tone and no really language so basically I'm just training
this model to give me to talk to me like a damn human not just like a robot so a
Spartan tone make sure you use this and no frilly language because you notice
sometimes they're like hey this company did this you know it's so cringe so
you're like hey no frills and you'd always say something like output the
results only with no additional text so you'd always say that just because
sometimes a I would be like okay here's this great data and I don't want that so
you would do something like this and then let's go back and just run this so
essentially you were making requests it's pretty easy and then if you go back
so this is like a case that I have my website $145,000 in your revenue add in
three months for clients securing seven major partnership is in 250k revenue as
you guys can see this like a like an example and what you would do is you
would just copy and paste this one right and then for example you would have
another prompt right here and then you would say something like fill out the
template and you would have something like an like an email right here like
hey first name and then you would say something like congrats on and then you
would paste in this right here and then you just start your pitch right this is
how basically I start the AI personalization which I always recommend
you guys do so you like a pre-draft like an email and then you would just fill
that email with variables which I think it's way better right so you just
retrieve data from multiple sources and just output that there so yeah that's
pretty much it and that's how I would actually approach this and then like I
said volume whenever you have an outreach lag I'd increase volume
whenever there's no response you need to delay gratification guys you have to
understand that there's something called initial dependence on initial
conditions of the initial dependence what that means this could drift so like
let's say you start a campaign there's like a lot of bit of time right until
you get the results like you can't expect the results come in like that day
the responses are not gonna get there sometimes it goes in steady and it's
just go and you just can't even handle the replies and then like it it's called
right sometimes people take time to answer their emails and then in there's
no like there's nothing wrong with your offer or you just have to give it some
time it's called the outreach lag so these are my two cents just go back I'm
gonna answer these questions at the end so great first session hopefully many
more to come thank you Zeeshan I'm sure with everyone have a million questions
but I keep it short to give everyone a chance so just ask me the million
questions don't worry man when generally leads should we can we narrow down to
companies who earn X amount of dollars per year example yes looking companies
making 30k MRR yeah you can do this but you got to understand that this
information is not 100% accurate you know all of these platforms like Apollo or
all of these which is a general ballpark like unless you you actually see the
company mentioning that in a LinkedIn post or something these are just general
information you just assume that but yeah I would definitely go for that I
would I wouldn't go for a specific number like 30k MRR I would just go for
like 0 to 100k 100k to 500k and then 500 to million million 10 million
general broad is way better right just because you cannot predict how much how
much how much a company is actually making so yeah once the campaign is
running how long do we wait before measuring it against KPI so that's a
good question you never stop the campaign until it stops basically this
is why it's good to use the high intensity training high intensity training
the high intensity sending that I used basically you you send emails in like
two weeks so you can finish up 5,000 leads in like two like two weeks which
is why it's so damn effective and how I basically always managed to like
interfere with the markets as soon as possible so I can I'm like get the data
give me the damn data instead of me trying to like think about in shower
like hmm is this gonna work I just go ahead and start sending and then you're
the market is gonna apply to yes like the the best mentor and the best the
best responses are you gonna get are from the markets so make your markets
your friend don't see it as enemy so unless you're getting like a lot of
results which you will so I wouldn't I wouldn't stop the campaign I don't know
like I would just wait until we finish the 5,000 leads and then if you get
within a KPI like I said just to put 2% of merits you're pretty good so I'll
just add leads and then you get from there because now it's just a scaling
volume but I so peace should we stay within our business so I'm not really
sure what you mean like what SOPs like you would have for me like I had an
SOPs of like pre-drafted messages that I'd have in my like notes so I'd be
like let's say I'm not on my laptop they say I'm outside or something like I'm
whatever and there's an email reply like like the next module that I'm gonna
update it's probably going to be tomorrow and then it's going to be the
day 10 which is going to be basically how to handle the replies and how to
basically let's say the quickest way to close a deal let me say let me tell you
something guys the quickest way to land a meeting is just to reply quickly trust
me just reply quickly within two to three minutes and then this flow that
I'm gonna that I built for you guys both cell systems community AI responder
basically what we're doing is we're watching the event whenever there's an
interested lead we're gonna search the lead and then I'm gonna scrape their
web web page and then what we're doing is we're retrieving their latest
achievements and then we have a pretty good email but I've personally used to
and this is the one so we have a template that I wrote for you guys this
is a hundred percent handwritten so thanks for getting back to me we're
gonna put like first name just using this for testing purposes I have an
idea to target dream ICP so dream ICP is a variable of their eye basically the
dream ICP right when they're talking about dream ICP pain points now there
this is their dream idea clients and their their dream idea client pain
points right so I'm gonna explain to you why we're doing this so we have
another thing which is top engagement cues who were feeding this into AI
engagement cues of their ideal clients and you're saying something like hey I
have the tax systems to be the specific we could help them with the help now
when you script in the data of their website you basically gonna get their
value proposition with whatever the hell they're doing so you could leverage
like hey we could help them using your service feels like the perfect time and
it's even crazier when you run all this automatically now as you guys can see
now they replied so now I'm pushing for the call at first I'm like hey would
you like more information they're like yes this is what I mean by more
information in your nurses into a call and I'm like hey is this worth a quick
chat this week always make sure it's is this worth your time is this worth
chatting this week it's kind of just to be in like a little bit of cognizant of
their their time essentially and then this is the pre-drafted email and then
I have an example so there's an example guys so thanks for getting back to me
first name and congrats on the new acquisition I have an idea to start
it's municipal municipal finance their director is right that right when
they're talking about cost recovery challenges top engagement cues could
include social posts about fee study struggles complaints about outdated
cost models discussing new development impact fees I have the tech systems to
monitor relevant keywords and topics is the perfect reason to reach out which
mean that we can help them with fee calculations and post recovery and
analysis I don't give a shit what this means but this is what essentially this
company is doing and whatever their offer I can essentially just use this
and the real juice is you're going to use this in your sales call so when in
our basic in our classroom once we have the sales call script and I'll manage
to get like a lead on a call so I can record that you essentially use this
right here so in the sales call whenever you're pitching and you're like hey
thanks thank you thank you for getting on call today and they're like yeah
what do you want to talk about or something you're like yeah I have an
idea to target these people so basically you're already like crushing
the entire competition and most people are like hmm what is your current lead
generation process you're coming in like pretty damn strong you're like hey I
have an idea to target your ideal customer here's how we're gonna do it
here's how many meetings you're gonna get and basically you you can close you
really here you have higher rates of closing right and essentially now that
you know how you have an idea what their ICP you kind of calm your nerves
whenever you go in your sales calls right so you just keep that in mind like
even if you're a pro like trust me the best salesman gets a little bit you know
weird and intimidated whenever you get it on a sales call obviously it dies
down when you start getting on sales calls like every single day just like I
used to do but yeah this is how we usually works so let me know if you
guys are going great so I can just because I'm picking up the amendment
yeah so I'm gonna answer all these questions after yeah all of these okay
so let's just go back got a few recruitment niche space based questions
hopefully you can answer which method would you recommend to use for finding
leads within the recruitment niche like I said you'd use the company's hiring
flow using LinkedIn or indeed you could still use Apollo but yeah you can still
use a fault to be honest you can like use Apollo if you're going on higher
volume and like Apollo has like new filters we can look for companies that
are hiring and I think they do have it yeah you can you can use that and that
should pretty much help I would just focus on if you're in Apollo just focus
on the volume and I'd use like an email validation tool such as either mails
that is always your balance because the data should get from Apollo is not a
hundred percent accurate but yeah I've never had problems with Apollo to be
honest I just use mails that I saw and I'm pretty good to go so as you have
previous success in the community types for people who are looking to get into
the niche yeah man um I've worked with a company before they're from the UK or
they're called connect group and I like I got them the results and all it was
back in probably July or something and then I just stopped working with them
just because the founder was pretty annoying it was very demanding and he
was basically like asking for free work and like notifications to his slack you
wanted like new things like I just like completely off board them but yeah man I
had like pretty good success with improvement like I know a lot about
recruitment right now and what I've noticed is they really love to get on
calls you just love them and I love get on calls just because they're a little
bit you know they talk casually they're more they're not like nerdy come like
they're not nerdy niches there they're always talking to people so yeah the
the likelihood of them hopping on a call with you is very much more likely than
other niches so if you if you take in like a founder this that's a complete
nerd typically you need to nurture them to a call and then they would basically
just give in at the end and get on a call with you and you would push them
to your service but yeah man so these are the questions of the threats so not
that much so you know what that means I'm gonna go back to the oh hey Eduardo
I'm really happy to see you man so let's just go back and see the questions
pretty good questions actually so yeah guys just drop any questions now yeah
drop drop in your questions now and we're gonna leave the last ten minutes
of the with a weekly call just because I want you guys to give me suggestions
and kind of like what you guys want to see because I want this this community
to essentially have the value that they want instead of me trying to find ideas
of what you guys might need I have many things in my mind and I want to just
structure it and give you everything that I need that you guys would need so
let me fix this camera right here awesome
so let's just go back and read through this
so what is an AMG and LPS system it's Apollo yes okay great so how many
operations to use on a monthly basis on make.com so it really depends to be
honest I try my I try to keep like all of my pipeline pretty lean I try to not
build so much automation that just completely useless but to be honest they
do it up a little bit of operation just because when you perform the AI
personalization it takes a fair bit of ops what you can do is you can use
genuine secrets if you have genuine secret they're gonna get they're gonna
give you 240,000 operation you guys don't see this wait let me show you so
in terms of joint secret why I recommend this is you go to make they're gonna
give you like 240k ops so look at this make from like 12 months free on the
teams plan and they also gonna give you like a month free on the pro plan so
imagine this like 240k ops like you could be building like the next step
left and you won't be yeah so can you please talk about your automations
regarding sales meeting wonder if you have some fireflies path and something
set up at all to be honest I don't really do this I actually don't I just
go ahead and basically I just go ahead and get on the damn sales call because
I don't think it's worth the time and automate and all of this you just have
to focus on you know the real juice which is actually get on the sales call
and like close in that the only thing I would automate is basically how I get on
the sales call like I would have like a general idea of what they do kind of
like I showed you where I would come in I'm like hey I have an idea to target
this ideal customer that might be a good fit for you this is essentially where I
take it could you go over a sales call framework order of talking points yeah
definitely so I wanted to make this like an entire video for you guys because I
use a little bit of tactics and like I said I don't care about grey hat tactics
because we want to make money here so in terms of sales calls I use a bunch of
like I use a bunch of I don't know like people call it manipulation or something
but humans are extremely predictable and reliable and consistent what I'm
doing is basically I'm just applying psychology to them so there's a ton of
things that you could be doing like mirrors or labeling like you would be on
a call with a prospect and they're like you just let them talk and then you
would essentially mirror what they say so whenever they they're speaking you
just repeat the last part of what they say and people usually when you use this
they're like hmm this guy actually listens and you're not really listening
so these are things you could do there's also your tone of voice I want you to be
whenever you're presenting a resolution be confident whenever you're asking
questions you're nodding your head and make sure you always turn on your camera
and whenever you are handling objections you always be confident you know you're
not really needy of their approval of them of you like you know working with
you so always make sure you you do this you have to put yourself in a right
frame of mind and always make sure you go in and do the sales call kind of like
in a frame that you already closed it I always say this guys I would say live in
the end I love it I like this coat always live in the end so whenever you
go on a sales call I would just go ahead and like I would like in my mind I'm
like I've already got this so it's kind of like the second win and I would
imagine myself closing the deal and it just goes in like naturally on a sales
call and at the end like always make sure to understand that it's all a
numbers game at the end like you know if you get on a sales call and you don't
they don't you don't you know close it there are many sales cause you're gonna
get like it's not like that sales call is gonna like change your damn life always
make sure you go in this state of mind just because you don't want to fuck off
your mind and it's better that way for your sanity and for your confidence too
so don't don't be that don't be that with a dead horse so great shit thanks
so yeah so research the most common pain points and desired outcomes of the
niche so you know tolerable stuff then speak with absolute confidence your
body language in a way of speech will have confidence even if you don't know
much yes it's a more of a mental thing than practical yeah man yeah and word is
like putting on some sauce it's really good it's confidence is years a year on
how to decide yes yeah always like I would always do something like I would
like take a look on their like LinkedIn you would do that like just look at their
website just kind of like have an idea just don't go out on the sales call like
absolutely you know it like knowing nothing about them so yeah a fear of
running out of leads how do I get five care for each leads and unique leads
each month now let me tell you something Paul like you don't you shouldn't like
care about this like if you can't like I say you can't predict like like new
companies are every day you know like you know how many leads are in the
entire world like you shouldn't care about this to be honest unless you're
targeting I don't know like red light therapy in Los Angeles that don't
have a like a red light therapy bed you know then you might essentially run out
of leads you're never gonna run out of these trust me like I've been in like
pretty damn like narrowed down niches and I've never like actually like I was
like holy shit how can I at least because we can always find leads like
there's always buyers at the end of the day always buyers whenever there's the
niche there's buyers just don't overthink it I'll just go ahead and
start spending. Could you propose a proposal draft for us? Yes a hundred
percent man I have a proposal that you guys could use and if you guys read
through the service agreement you will see that I have a pretty pretty good
tactics so even if they ask you for a refund there's some things you could do
such as a setup fee that is actually let me show you guys so let's go back let me
share my screen and then feel free to copy it or remodel it whatever you want
on so if I go to app.pandadoc I believe believe it's this one let's see yeah
this is the one so I was like recording a video for you guys yesterday and it's
called the sales process and you guys will have to see it right here it's in
the classroom if I go back to my classroom and I go back right here you
will see it guys so the sales process of the agent that doesn't mean I just
actually posted this right so you're gonna see my sales process and I built
the entire sales process like a 38 minute video like I show you like the
automation that I use basically everything and then you're gonna see the
Pandadoc proposal that I have so look at this proposal that I have it's kind of
like you know I have my logo right here service agreements scope of work I don't
use any bullshit like your problem your solution I don't use that pretty
straightforward like I think it's way more professional when you frame
yourself as a growth partner not as a glorified freelancer like here's your
problem here's your solution like I don't care about that people don't care
and you're kind of like shooting yourself in a put I think because you're
like hey here's your here's the problem and the solution here's why I'm better
than you and when you are working with clients are paying like five ten K
typically the non-likes to see that they're like hey hey can you help me and
us so we have benefits how we do it benefit how we do it and then I have a
just a timeline right here they can populate and then pricing and I just
call a frame a growth system for company name and then I have consultants and
then clients and then basically guarantee provided all payments shall
be non-refundable and not as a support is just for me just because I built
those those growth systems like those systems as you guys see me build and I
just I'm like hey there's no I built the system so there's no there's no
refund when it comes to that well I mean depends on the offer I'm just running
this offer right now so yeah and then basically just they sign and then I have
my logo right here it's called a growth system and that's that's pretty much it
and I just click on send and I send it so yeah I hope this helps man yeah 240k
ops yes so I don't know I have a question regarding the structure where
we charge yeah 10% of LTV per book meetings goals 10 book meetings some
clients might ask a question this model especially if they feel they are taken
on the risk of the meetings won't convert into actual deals for example if
they only close three clients out at 10 book meetings and we still charge them
for 10% of the LTV and send me can we clarify how to handle the solution
should we only charge it no you never do this you don't really care if they
close or not listen man like your job is to actually deliver the meetings that's
all if they convert or they don't we don't really care like you have to
understand that why do you have the leverage is because you can deliver
meetings and if they don't convert it's their own like they it's their own
fucking deal right yeah it's it's a closing problem like like I would I
would go ahead and like it like if you if you send them the meeting like the
meeting ready lead like you need to close that for them like no like it's
not your job you're you're just sending them the meeting like just doesn't make
any sense so yeah your job is just match pattern them with a client that is with
to hop on a call and is interested in what they have to say so in terms of
them closing or not it's not your it's not your you know your job but yeah man
so we have like 10 minutes left just dropping your questions guys and what do
you want you guys want to see in terms of like because I want to put like so
we're gonna have like a beginner stage where everyone just gets their first
clients and then we're going to talk about scaling and the scaling part is
the actual juice so once you find that niche now we're going to talk about
products a productization how to actually like basically build those
systems that you can send those leads to the clients and depending on each
interested lead let's just say you have like an interested lead and then you
have a lead that needs more info dependent you'd have like a router on
make that sends a message in your slack and your slack you'd have pre-drafted
messages and buttons you would just click on one button and you would send
it the the lead to the to the client you're like hey this lead asks for more
information and they would reply to you on slack and you would just answer them
quickly and there are other other like you know automations you could build how
to send the meeting really lead or I mean depends on what you're doing but
so yeah that's pretty much it I think there's another thing which is going to
be the yeah the AI responder I wouldn't recommend you guys use all of these
basically like like an AI SDR all of that crap don't use that just because
sometimes AI is gonna fuck up and like like let's say like percent of 90% is
gonna AI is gonna mess up so I wouldn't recommend you guys do that the flow
that we use is basically just a pre-drafted one so you're still gonna go
ahead and you're gonna receive that pre-drafted like 90% all the way there
email reply and they're gonna tweak it sometimes AI is gonna mess up with like
a little bit of mistakes you know what you can do is just go ahead and copy
that like tweak it in like 30 seconds and then basically just it back using
instantly you'd have like instantly app on your phone this is what I used to do
basically as a one-man agency like I didn't need to hire anyone guys you
don't need to all the way to 40k don't need to hire anyone trust me I've done
it like I didn't like you would have like like your instantly app you're
gonna have a full walkthrough on the video but I'm just gonna give you like a
little bit of an overview you're gonna have the instantly app you're gonna have
notes app where you're gonna pre-draft like like replies that you will have or
you can use Google Doc or any note app that you want to use in your phone just
because let's say you're not on your phone that you're not on your laptop
let's say you're outside or something and you want to reply to that lead
because I can assure you guys like there's many big deals that I've
managed to close just because I was like I had everything in my phone I received
reply using instantly app I got in I received the message in my slack I
tweaked it a little bit and I replied pretty easily and there's this cool
feature I believe isn't it's an instantly and you can essentially just book the
call through instantly I believe I don't know if they've done it yet but
I'm keeping up with the founders and Twitter and I'm gonna let you guys know
yeah so that's pretty much it and also there's another thing you so you would have that you
would reply to that lead like instantly and then once you reply to them they're
like yeah I'm interested in having a call you essentially just go ahead and
book the meeting with them and then they eventually enter your pipeline and
that's pretty much the way you secure a lead and in terms of clients management
you would have like I said you would have a bunch of automations in place
that sends like you'd set up a webhook instantly and then you would have like
an AI that categorizes the reply like I would always always always just
categorize into play into like responses basically lead more wants more info and
If you go in and you start like trying to find better like other responses AI is
gonna mess up and you don't want to do that so what you want to do is you would
have like Lee wants more info and leads interested and you would add that to your
CRM just because you want to track those leads for the clients because some
clients will mess you up and you're like they're gonna be like hey well you
delivered the lead and is it six or seven and you have to track everything
guys like like I said what doesn't get tracked doesn't get measured so you
would you would have a like a basically like a webhook that sends the leads
for CRM let's say I'm using click up just because I like click up I'd use
click up and I'd have like a list and click up basically that auto populates
automatically whenever lead like has a question like leads more info with a
drop-down field which would be like lead has more info and I would track them
with the same with the campaign ID and the campaign name first name last name
email and company name and then I would receive like a message in my slack
right and then I what I do is I click on one button like it's either so AI is
gonna categorize is gonna be like hey this leads needs more info and I'll just
click on wants more info if I if I know the answer I'll just go ahead and apply
if I don't I'm gonna send it to the clients using our slack channel which is
why I recommend you guys use slack just because it's easier you onboard the
clients and then to your slack channels pretty straightforward you don't have
to deal with emails all that shit so you would add into your slack channel is
client lab playground or a client client so I always name it client playground or
something like this where they come in and then you define your availability
you're like hey I'm gonna give you progress updates before we go in during
the week and my availability is you know from this time to this time and that's
how you like your pure professional and you can eventually like just do this
your own yeah and another thing as you what you do is you would dependent on
the offer so you would have a bunch of information about the clients and you
would have like this is what I used to do this is like peak neurotic crazy
working all day what I used to do is I used to keep like a folder so I'd have
like an automation but I already showed you guys in the onboarding so whenever
you onboarding your clients actually like create a folder for them in a
Google Drive and then what you do is whenever you want to like write the copy
do everything just go to that Google Drive it's gonna be the reference so
just because when you get to that point there's many clients and your clients
and you're gonna get cluttered and you're open you're on your own and it's
just so many data here and there so always scrape the data put that in the
folder message in all of everything offer everything put this in that Google
Drive with their offer messaging AI everything there and then what you do
basically is you add like a Google Doc like a file there with their entire
offer and with the value proposition what they do and what you can do is
whenever you don't want to deal with sending the clients like a question you
don't want to and they don't know the answer of you would have like a like a
just a mini SOP of what they do and you just know what type of questions like
that lead would ask and you would just reply on their behalf and you're
nurtured them to a call and then essentially what you do you just once
you do that like a couple of times it's just easier at that point like you're
just gonna you just have to get the reps in and then yeah you just book the call
you would have like basically the information of each client and another
thing once you get to that point you start warming up the emails you just go
ahead and if you guys look through my website I don't warm up the email my
clients well I and now I don't now I stopped cold outreach completely just
because I cannot handle more meetings like I just can't what I what I used to
do is I basically just have like just get pre warmed up emails and I would
just build out a campaign like two days and the first week I'll start sending so
the first thing is I get the client the first wins the first week and like the
first week I'll just send them the the the leads just because I want to don't
want to deal with the I don't want to have like in my mind like I need to
deliver for that client if I don't want to fund them I don't want to deal with
that I don't want to fund them I want to keep the money so what I'm doing is I
like I would buy pre warmed up emails using instantly or you can use like a
couple platforms such as paper side but I believe is pretty expensive so I
wouldn't recommend that at first so there's this guy out right here is Nick
Abraham they're they're pretty good lead agency agency you know they're pretty
good like I'm friends with this guy you could use there's other ones you could
look for pre warmed up there's a lot of platforms so it's either
instantly or you can use let's see pre warmed up emails email account there's a
lot of platforms yeah I know this this one right here that's in don't use my
use like a limelight on all of these so I think someone community can just point
us in the right direction so I would only use I used only the you know the
instantly ones and some hyper tide so yeah I'll just get those and then the
only thing now you have to do is basically you don't have to deal with
any D mark any a bullshit what you have to do is you just go ahead and start
with their offer and you scrape the leads and you get them the first wins so
yeah man
okay so how come what clarifies a lead to be a hand we're ready basically a
lead that wants to chat over a call and they booked a meeting right the best
thing is they you you send them the calendar link after you nurture them
you're like hey here's the calendar link or you schedule them alone like you go
ahead and schedule their name you're like hey I we're gonna have a call I'm
gonna send you in it like an like an invitation on your Google Calendar just
accept it just to reduce the fraction on your own there and just because the
client is gonna have to go to their to the Calendly or the cal.com and pick
them pick that time and then it just removes all that you're like hey I'm
gonna schedule you is that okay and then they're gonna be like yeah go ahead and
like what and you always say something like hey what time like is it good like
Monday or like Thursday what time what time like works best for you always
look for the best time for them not for you or for the clients just because it's
way better that way so how common is there are retainers do you start with
each month with zero dollars how much work is there for example 2k monthly
retainer so retainers so first you want to just get them booked meetings and
then you would essentially just pitch really hard and upsell or their retainer
what we have to do is you need to get them the leads at first you need to like
deliver first month and then that's it like that's it like they're sold because
what means if they stop working with you they're not gonna get leads so this is
why you just justify your value there because you guys know like that when
value is created money is gonna be made so always make sure you provide value
please upload this click obviously I'm gonna upload this this part is something
I'm not interested in how would you nurture the leads for your clients seems
so abstract again not showing their niche well let me explain to this to you
my friend so how do you nurture the leads for your clients well you nurture
them typically just how you nurture your leads for your own clients for your own
company right so you essentially just like I said like just use this this this
formula right here that I'm gonna give you and there's just don't beat that
like you ask them for more information if they want more information if they're
like yes then ask them for a call this is basically what like nurturing means
until they until until you will sense it like I cannot just tell you like how do
you nurture like when you speak to someone you can just tell they're ready
to go on a call so when they trust you that's when you will nurture them like
into a meeting right you're asking them more information you're like yes I am
interested in more information what you do is you send them information about
your idea your clients you're like hey this is what we do and here's an offer
should be interested like in a call this week is this worth your time if they're
interested they're gonna be like yes I'm gonna hop on a call if they don't then
you just got your answer so I mean I guess I'm afraid to ask about niche
specific things that I don't know no I don't think so it happens rarely to be
honest if you don't know then like I said you just send the question to the
clients and you're like hey there's a lead that's interested like I said you
just send them the email or like you send them you send that like a paint them
on slack and you're like hey what's the appropriate answer for this question and
they're gonna give you the answer my friend so yeah man uh this call is gonna
be uploaded to the community so I hope you have you guys found value in this
call right here and yeah even if they do it's fine it's a numbers game yes
yeah that's that's exactly what I would do so yeah man thank you guys for coming
today and I'm gonna post this at the end of the day and obviously I'm gonna post
the thread and you guys just post your questions for next week and I'll see you
guys in the next video and peace guys
you
