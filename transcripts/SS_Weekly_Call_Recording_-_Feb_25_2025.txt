This meeting is being recorded.
Is there an axle? Okay, perfect. Now we have a way better
quality. Since I'm such a Google Meet guy. <PERSON>m is like,
believe it or not, guys, every prospect that I've talked to in
my entire career, I've always tried to get them on a Google
Meet. Always. I'm always saying, hey, I'll send you over a
meeting URL using Google Meets. I like Google Meets, man. So
easy. Zoom is like,
and I'm using OBS
to record. So I believe the quality is going to be even
higher. Once I put this recording into euphonic.
Hey, <PERSON>. Hey, <PERSON>. You guys can hear me?
Let me mute y'all.
Okay, perfect. Let me mute you. Okay, perfect. Let's just wait
for everyone.
So I didn't upload, just because it's going to be, you know, logistically
unfeasible for everyone. So I have to basically admit to
everyone.
May I suck?
So let's just wait for everyone to come in. Hey, <PERSON>. What's up?
This is
we're using <PERSON><PERSON> and I'm using OBS to record my screen at the
same time. So the quality of my voice is going to be way better.
And then once I put that to euphonic, it's going to even be
way better. So hopefully this is going to be even better. It's
kind of like a streaming way, you know, it's kind of like the
streamers.
Yeah, it looks way better. Yeah. Hey, Sam. Okay, so we have like,
we have a very, a lot of things. It's the Samsung key two. It's
this one right here. I wish I had the, the package I can show
you, but I can, I can show you, I can show you, I can show you
the package. I can show you, but I can, I can just link you the,
the link on Amazon. It's not that expensive. It's like, I bought
it with probably like $70. Yeah. And I just got this tablet so I
can, you know, explain everything better. So it's way
better. I have the pen
and I have a computer here. I wish I could show you. I'd like a
monitor here. I have my laptop in front of me and I have the
Yeah, much better than a whiteboard. Yeah, I was thinking
I would put like a whiteboard in my background, but here in my
office, I can just do this. But let's admit people that come
in.
I'm sure you have the complaint key.
I used to geek out when it comes to this. Yeah, I was, if you
guys been into forums, so back then, the early two towns, there
was forums, instead of like communities and stuff. And
forums are like much higher quality. So you meet with people
from the entire world. And then I was a kid was maybe like 12
years old. And with me and with people there. Like I was 12
hours like in front of the laptop.
Yeah. Okay, guys. So hopefully, how many people are here?
So I think we're just going to get started. And I'm going to
just manually admit people. Okay. So I'm just going to share
my screen now. And let me know if everything is perfect on you
guys. And I'm going to share now
entire screen, and then share.
Okay, so you guys, everything's good.
Okay, looks good. All right, perfect. Let me just put the
chat here. Okay. Awesome, guys. So today's lecture, we're going
to talk about setting up your sales system for success, we
don't talk about six pillars, right? So six pillars to have a
successful our trend. So we're gonna talk about outreach lag,
not talk about vagueness when you write your copy. I'm also
going to, you know, dive deeper into, you know, copyright in,
I'm going to give you a formula that works really, really well.
And then we're going to talk about, you know, how to have
like a clear value proposition, how to copy, you know, copies
that are in the community, how to basically add your little
spin to them. We're going to talk about quality and quantity
when sending emails, you know, outreach. And then we're going
to talk about how to actually scale how to determine whether
your system is scalable or not. And then we're going to talk
about again, we're going to talk about follow up, etc, etc. And
then at the end of the day, at the end of the call, we're going
to talk about, you know, you guys questions in the chat. So
45 minutes, I'll talk about this. And then the last 15
minutes, I'm going to just answer you guys question in the
chat so we can be interactive. And obviously, I'm going to keep
asking you guys questions while I'm explaining. So it's more,
you know, fun and interactive. Does that sound good? Send me
like a like emoji or something. Okay. Perfect. Let's go. All
right. Awesome. Okay, so we're going to talk about the outreach
lag. So I wish lag is pretty interesting, guys. So you send
an image. So let's say today, you start a campaign, and you
send an email to someone today, there's this period, right?
That's called latency, which is the elapse of time between them
reading the email and actually doing anything about it. Right?
We just admit everyone here. So there's a period that's called
latency, right? So let's say you send an email today, some people
might reply today, some people might reply tomorrow, next week,
two weeks from here. So there's this, you know, period that's
called latency, right? So first of all, they got to, you know,
receive the email, they're gonna open the email, they're gonna
read it. So a lot of people don't know how to manage their
time. So they might put in their to do list, and never do it,
etc. So this gets a lot of people because I remember when I first
got started, I would basically start my campaign, and I would
get a little bit of anxiety when I don't get replies within a
few hours, right? Which is totally, you know, normal. Do
you guys get this feeling when you start a campaign? Yeah, you
get this, you know, clean feeling inside of you. It's kind
of like, it's kind of anxiety that gets inside. Yeah. Yeah,
for sure. Yeah. So that's normal. Just so you know, guys,
even like, like, at this level where I'm saying is so much
volume, when I still get this, right? Yeah, I always think my
list. Yeah, I'm gonna, I'm gonna explain to you is that the
work. But this gets a lot of people. And I remember when I
first got started, this also got me but I learned it the hard
way. So you have to understand, guys, that there's this thing
called latency. So the person that you email today, right? My
book in seven days, or 24 hours later, you just don't know. So
if you can be consistent, then the work today is likely going
to be the client that you sign two weeks, or even a week, or
even today, a lot of people in the community, they book a call
during the exact same day, right? So this the exact same
day, that they started the campaign, and some other lead
that you might book them, you know, a few days later. That's
so normal, right? Because the sales cycle is pretty long,
right? There's a lot of variables that have to come in
before you actually, you know, sign a client. So if you're
feeling this is just normal, it's just called a latency or
it's just an outreach lag, right? You just have to wait a
little bit of time until people actually open the email, read
it, etc, etc. Okay, so this is what's called, you know, outreach
lag. The next thing we're going to talk about is to go from an
email to a dollar, right? There's a lot of little stages
that have to happen. Okay, so just understand, you know, that
the sales cycle varies can be seven days or a month or even
one day sometimes, right? So if you just wait, and don't change
the variables, please do not change the variables, meaning
don't change your copy, don't change your offer, don't change
the subject line, just wait for a little bit, right? Until you
know, those emails actually are sent, and people open their
email, and you know for sure that your offer resonates and
just lands, then you can change. Okay, so this is called second
order effect. So the efforts that you do today, right?
There's this thing called second order effect, where the results
of what you do today, right, are going to be probably in like a
timeframe of one to two weeks, right? So if you send an email
today, you might not get a response in a few hours, like the
same day, but you might forget that you send that email. But
then you might sign that client or that lead a week later,
right? Does that make sense?
So the key here is consistency, guys, okay? Always just be
consistent. Don't change your variables at all. Just wait that
real lead that you contact, right? Each lead has a different
sales cycle. Because once you understand, now the doubt is
going to go away. Because the main, like the most important
thing is a doubt. Because this isn't is going to work, you're
going to get replies. But you know, your emotions are going to
basically get triggered and you're going to go ahead and
change something because I used to do it. So I'm just making
sure you guys never ever change anything until you finish your
campaign, right? Because the most important thing is actually
like your like your abilities to control your emotions and your
feelings. When you start your campaign, not the actual like,
like scraping leads, etc. That's the easy part. You know, just
stick with it. Understand that there's a lot of you know,
there's a little stages that have to happen for someone can
just open your email and then reply to you and basically book
call. Okay, the story normal. Okay. Now we're going to talk
about basically something called vagueness. Okay. So there's two
reasons why people book calls and it's gonna help you guys a
lot. It's gonna help you a lot, even if you're when you are
delivering for your clients, right? So there are two reasons
why people book calls. It's incentive and curiosity. Okay.
Incentive comes from the offer that you know, the value of
going to provide the developer position, the clarified offer.
This is the outcome that people want, right? And then there's
something called curiosity, right? So there's incentive and
there's curiosity. Curiosity is why I always tell you guys, make
sure you just ask them from more image called vagueness. So by
keeping things vague, you simulate their curiosity, and
people will want to learn more, then fill the space with their
mind, which is why I say, would you be interested in more
information instead of directly pitching for a call, right?
Because this can create loads of scenarios, right? In their mind,
and humans would like to, you know, love to chase. This is why
we're saying, Hey, would you be would you be interested in more
information instead of saying, Hey, let's just go, let's just
hop on a call, right? So when I tell you guys, as say, pay on
results, or like pay on performance, I wouldn't say what
it means, I'll just leave completely out of chance, right?
So in your offer, when you have like an offer, let's say you
are, you have your personalization, you have your
clear value proposition, right? And at the end, you'd say, Hey,
it's all pay on performance or pay on results. Well, if they
ask you, like, what do you mean by, you know, pay on
performance? You would say something like, Hey, don't worry,
pricing is never an issue for our clients. See you on Friday, I
see you Friday at 12pm. How does that sound? Right? Never say
that you never talk about the price at all. And you just get
them on a call, it's gonna help you a lot with your clients.
Because in order for you to have a successful performance, you
not worry about whether the client is gonna, you know, close
them or not. Your job as the growth partner is just get them
the meetings. So now you're safe. How you get them the
meeting doesn't matter. Does that make sense? Okay. So it's
gonna help you when when you're like, you know, trying to
fulfill and there's like, maybe because sometimes you might work
with the clients and they have a very shitty offer. You can use
some leeways to get them on a call. Now you're safe because
you you've delivered the book meeting. They can't say shit.
You know, I've had so many instances where I work with
clients, and they have really shitty offers. Like, I can do
anything like I could just have the best, you know, dream email,
everything, but their offer just sucks. So what you can do is
just find a way to position yourself. Right? Yeah. Yes,
wrong. You help them have a reasonable offer. But at the same
time, sometimes you might get some clients that they say, well,
we can't, you know, you cannot have this risk reversal on this
offer. Because like a good offer has three things, a good
timeframe, a risk reversal, and basically something that is
outcome focused. Sometimes you might work with clients that
they don't have a clarified offer. Right? You'd ask them,
well, in order for us to have a good offer and offer that lands
at your customer, we need to have some sort of, you know, risk
reversal for them. They can't just say yes, and expect us to
work with them. Right? Some clients might have a shitty
offer. And a lot of people in the community, actually, they
just DM me and they're like, how can I, you know, my client
doesn't want this. Like when I try to help them build an offer,
they say, well, we can't say this because we can't work on
paper performance. Right? Not all the clients, not all the
clients that you're going to onboard are going to have product
market fits. Right? So your job is to get them meetings. You
know, the ability of them, you know, closing is not, you know,
it's not in your power. So just get them the meeting. And that's
it. If they have a shitty offer, you completely remove that
stress from delivering. So you've delivered a meeting, they
have nothing to say is their job to close them. Does that make
sense, Roger? I've never had this backfire because, you know,
in the proposal, you sent them, Hey, we're going to fly both
meetings, you know, with people that are interested. And then
that's it. If they close or they don't close, it's on, you know,
it's not your job to close the meetings for them, right? The
entire point of why our business model is very profitable,
because you just get them the meetings. Is their job to close?
I mean, no shit. So you have to understand that people only book
calls if they think you can get them something. But they need to
understand it just so they keep themselves sane and sleep that
night. So if you have a client that is so hard for them to get,
you know, book meetings, you just, you know, find a way to get
that prospect on a call, no matter what, right? So make sure
you apply vagueness in your client's offer to get them a
book meeting, right? Just a little hint of their offer, and
then keep it vague and get them on a call. It's going to help
you a lot with your filthiness. It's going to remove all that
stress from you. I know you do this all the time. So keep it
vague. People are more likely to book, right? And people are more
likely to change knowledge, and don't have open loops. Okay? I
think Axel is going to understand this. Right? Because
actually, sometimes you're going to work with clients that don't
have product market fit, right? Your market is just shit, you
know, it's not your it's not your problem. You know, at the
end of the day, you're just going to get them a book
meeting. What they do with that leads, it's totally up for them.
So same thing, if someone asks for the price, that's the
ultimate leverage to get them on a call. Don't just say, here's
the price, etc, etc. Because any price that you're going to talk
about, you know, in text, when you're talking to them via
email, it's going to be like, it's going to be high, right? So
prospects will always say, hey, how much is that? Or like, how
much are you guys charging? And then you just say, hey, don't
worry, price is never an issue for our clients. I'll see you at
4pm on Friday. How does that sound? Always pitch for a call,
never talk about the pricing, right? Which is why we have
those follow ups in the classroom. Okay? Now we're going
to talk about copy. Okay? So I see a lot of people here that
sometimes they copy and paste things, which is totally cool. I
just want to, you know, inform you guys that you shouldn't be
copying scripts word for word, but just add your little spins.
Okay? So if you see someone in the comments or like in a thread
that is posting like a copy or something, or like in the offer
odds, make sure to add, you know, your little, your own
little color, right? Just your own little color. And let me
just remove this. Not talking big variations, because there is
a reason you want to copy is because it works, but just add
your little spice. Okay? Make sure you never copy scripts in
the, you know, word for word, just have your, you know, your
own little color to it. And it's gonna work because as soon as
someone like just everyone sends the same copy, like a word for
word, it's not going to work. So just make sure you add little
hand words and it's going to work perfectly. So here's the
ultimate copy that I found it works after sending thousands
of thousands of thousands of emails. Okay. Here's the one. So
we'd have a clear value prop, right? A clear offer. Obviously
you're going to have your personalization on top. So
personalization, a clear value prop is a clear offer. And
these two things is literally just the risk reversal, the
outcome, and the timeframe, right? So the clear value
prop plus the clear offer is just these three things. So make
sure you have your risk reversal, your outcome, and the
timeframe. And some sort of case study. Not necessary, can be
vague, will mention anything, even if not related. Okay. And
the last thing is going to be a low effort CTA, right? So this
is like the ultimate, if I had to like choose, like choose one
thing, and get a client as fast as possible, I just have my one
liner personalization, right? A clear value prop, a clear offer
that has these three things, which is the risk reversal, the
outcome, the timeframe, and some sort of case study doesn't
doesn't have to be crazy. Just anything that you worked on, you
could say even like I built a system that does this, and that's
X, Y, and Z. It doesn't have to be like, you know, super
specific. And then you just have a lower effort CTA. But the
things that I would recommend you guys focus on is these three
things, right? timeframe, risk reversal, and the outcome, right?
These three things are the most important things. Okay. So you
literally you literally like reach out to someone you say,
Hey, Peter, congrats on X, I can get you exactly this in a few
weeks. And it's a pay on results. If you don't get it,
then it's basically free. Is this something you'd like to know
more about? That's like the easiest way to get a book
meeting, right? I can get you X in a few weeks, right? So I can
get you five book meetings, and three to four weeks, just 30
days. And it's pay on results. If you don't get it, it's
basically free. Is this something you'd like to know
more about? So we have the personalization here, we have
the clear value plus the clear offer, right? Make sure you
never talk like a politician, have a clear offer, right? And
then have a low effort CTA. Is this something you'd like to
know more about? They say yes, then great. The best the best
way to talk about just hop on a call. That's like that, you
know, the Trojan horse. You just say, Hey, would you like to know
more about this? And they say, Yes, I would like to know more
about this. And you say, Okay, the best way to actually, you
know, give you like a proper code is just to actually hop on
a call. And they say, Okay, sure, let's hop on a call. And
you just put them right away. And you can do this too, for
your clients. You'd say, Hey, we do this, guarantee this. Is
this something you'd like to know more about? Okay, and then
you just forward the meeting to your clients. And that's it. You
just removed one meeting from your from your plates. And now
you're not stressed. Okay, so this works for you and works
for your clients. Does that make sense, guys? Let me know in
the chat. Okay. All right, perfect. Now we're gonna talk
about. Now we're gonna talk about merging quality with
quantity, right? So 5000 leads is ultimate number that I still
use till this day. So 5000 leads is why I chose 5000 leads
because some people swear by volume. I know some lead gen
agency, they say, it's all about volume. Well, it's true. It's
all about volume. But volume plus quality is everything that
you need. Right? Because volume, we know that volume negates
luck. We know that. But what if we merge quality with quantity,
which is our 5k leads, right? That's our sweet spots. She's
why I'm always stressing 5k leads, 5k leads, 5k leads,
because it's the ultimate, you know, spots, right? For lack of
better words, where you can be sure that, you know, you're
still sending a lot of emails. But at the same time, you're
sending high quality emails with a, you know, with a really
high quality personalization, you have everything sorted out
and you just basically are so sure that you are testing with
the right, you know, the right data, right amount of data. Okay.
Now we're gonna talk about the, how to basically apply scale to
your system. So let's say you start your campaign, you got a
few replies. I've talked with a lot of people this week that
got their client, the first clients. And most of them
actually like the established KPI, which is 2% of them even
got some people even got like 5% of fly rates, right? And they
got their first client. So now it's time to scale. So once
you've established a proven concept, now it's time to scale.
Okay. But you should never ever scale if you haven't got at
least 2% reply rates, right? If you get to reply rates, right?
Then it's time to scale. Because why I'm saying I'm telling you
guys 2% reply rates, because it's much easier to get results
from a system that is already working than to try to reiterate
entire system from end to end, from the beginning, right? So we
established KPI with 2% reply rate. Let's say you book 5,000
leads. You've managed to book an ABR. ABR basically is just the
appointed booking rates. Let's say just 1%, right? You got 10
booked meetings from 5,000 leads. Let's say you're shut at
closing, right? Then you just close like, you know, 20%. That's
still 2 clients, right? That's still 2 clients are paying you
$2,000 to $3,000. So you've essentially just all the money
that you invest in your campaign, that's like 10 out of
wire, right? And you're still profitable. So that's like the
lowest, right? That's just 1% ABR, just 1%. If you get more,
right? Then it's even more. But 1%, just 1%. So another one
priority is to find proof or concepts or POC. You shouldn't
be trying to get rich from one campaign. Obviously, it can't
happen, right? It can happen, right? But you should only try
to find a proof concept. Again, what you want to do is go with
an idea of trying and interfering with the market rather
than trying to waste time. Because the only person who is
going to give you the right answer if your niche is working
or not is the market, right? Okay, so I'm just going to quote
a quote that I read from a book. It's called The Intelligent
Investor. I would highly recommend you guys read that
book. And it's about how markets in the short term is a voting
machine. But in the long term is a weighing machine, right? So
if you come in, and you, you know, provide value, right?
upfront, right? You're not trying to basically, you're
delaying your gratification. You have lower expectations. And
you basically stay consistent in your campaign, you do not change
anything, you're gonna, you're gonna achieve initial results.
Because don't do what I used to do when I first got started,
right? I would start a campaign, and I would only scrape like
2000 leads, right? And then I would be like, I would get lazy
and I wouldn't personalize everything. I would just
personalize like maybe 1000. I'm gonna be like, okay, let me
just split the campaign in half. And I'm gonna target this niche.
And I'm gonna have I'm gonna target this niche. And then I
would send emails. And then the next day, I don't get like, I
don't get replies. And I Okay, I go on YouTube. And then I see
someone that's doing a completely different thing, I
change it. And then a few days later, I get replies from the
initial copy that I that I previously wrote. And now I don't
know what's working. What's not right. So never ever do this.
Okay. And it took me a long time, a lot of money wasted, like a
lot of things. This is why I'm telling you guys, never change
your copy. Wait for the outreach lag to basically go away. And
then you'll be gold. Okay. And it's also going to remove the
anxiety, right and gives you way more leverage because now it
becomes scientific. And more about you know, being a
scientific as opposed to blindly and desperately freaking out,
right. Which is this huge guy. Now the good news is that once
you test enough, and you find the POC, then you can go balls
to the walls, right. And scale this puppy all the way up. Once
you've established a 1% ABR, right from your 2% KPI, which is
reply weights, then now the scale, okay, just scale. And
then you can essentially now you've established something
that's working. Instead of reiterating from the beginning,
now just apply volume, apply volume, apply volume. And this is
how you get more cash collected. And then you still have some
clients that come in. So you have your pipeline coming in,
right? You have your pipeline, and then those people that are
already working with them. Now, you can you can now build case,
but at the same time, you have, you know, people that come in
your pipeline. And now you're essentially doing something
called the Lollapalooza effect. When everything is working in
your direction, you have a pipeline that's coming in, you
know, consistent flow of leads, you're still working on your
fulfillment, right, you're delivering. And you're delivering,
you have a pipeline. And at the same time, you're building your
case study that you can use in the next outreach. And everything
is working together, right. And this is how you can essentially
just, you know, build something profitable and long term. And
this is what I want you guys to essentially have at the end,
right? You you have like a like assistant that's working, you
get your first two to three clients, you get those clients,
get them on a call, tell them, hey, how was your experience
working working with me? You record your video, let me just
fix my camera. So it's just after 30 minutes. Okay, so like
I said, you'd have your consistent lead of pipeline. And
now you book those meetings. And now you're delivering at the
same time. And you get those clients, you deliver to them,
right. And then we get them on a call. You say, hey, how was
your experience working with me? And you can ask them if they
can basically record a video as a testimonial for you. And just
add it in your funnel in your website, or you can use it in
your next outreach as a testimonial. And you could say,
hey, let's say you have the next campaign or like you're applying
volume to your existing campaign, you'd say, hey, what
if I can send you a video of a happy client, now your ABR is
going to shoot to the roof, because now we have testimonials,
you have studies, right. So now you can essentially just be
golden. And now you'll be great, right. So before we go in and
talk about follow up, let me know if you guys have any
questions before we continue.
So Sam said, I suck at sales, I can't sell it. It would be
niche and he has to sell this and without hoping on a sales
call, like free trial webinar, make it a disaster. Oh, man,
that's tough. It's quite tough. Because you know, people, people
need to talk to you to trust you. Right.
I've never tried anything like that. I can always hop on a
call, you just have to, you know, you shouldn't. So the
problem that I see, you're saying I suck at sales calls.
Why? Why are you talking about like, why are you talking about
yourself this way? Right? No one sucks at sale. You know, no one
sucks at selling. You just have to. You just have to put in the
reps, man. Right. You can do anything you want. You just have
to put in the reps. Yeah, you can sell to your friends, you
can try that. But the idea of telling yourself that I suck at
sales is why you're probably suck at sales. You should never
talk bad about yourself about this. Always say I'm really
good at sales.
Yeah. Always say that. It's gonna put you in that state of
mind or actually being a closer. Yes, going to do these calls
like you're entering a house party. Yes, the fastest way to
stop sucking.
I know this guy that was a friend of mine, he would basically
get a little bit drunk before a sales call so he can calm the
nerves. Don't try to do this. Exactly. No limiting beliefs.
Yeah. I know that people are like, well, affirmations and all
of that, but they really do work, man. When you tell
yourself that you're really great at something, you become
great at it. Trust me. I used to write affirmation about that
I'm really good at sales. And eventually, I became good at
sales. Obviously, you're gonna have to put in the reps. And
don't expect that your first sales call is gonna be like,
well, Jordan Bell first style, but you know, you just have to
get in the reps, right? What do you do typically? What do you
typically do when on a discovery call and at the end, a client
asks for a proposal. So it depends. So ideally, what I like
to do is always close them on a call. Why would recommend you
guys do always try to close on a call. Contrary to what people
other people say, just send them a proposal. Yeah, you can send
them a proposal, right? But ideally, you want to have your
proposal ready before the sales call, right? When you have your
proposal ready for the sales call, you also put your mind in
a closer framework, because now we already have a proposal. So
your mind is like, okay, you're already prepared. Yeah, because
sometimes you would get ghosted, right? But even if you get
ghosted, you can still all up with them like nine times until
they reply back, right? But ideally, you try to close on a
call, right? Because sometimes they like they would have
bullshit, like the weather, whatever they had. So always try
to close on a call. Sometimes it's not gonna happen. But
ideally, you'd say you'd have like your proposal pricing
already ready before you hop on a call. And you'd say, Hey, if
that sounds good, let me let's just, you know, look over the
proposal and go over it. If you have any objection, we can
just you know, handle them right away. And if it makes sense, we
can just go ahead and get you started, right? I can send you
the contract you sign and then we begin everything else. We
begin the infrastructure, we send it, you know, warming up the
emails, etc. If you have an offer that you can buy, you
know, pre warmed up emails, you can say, hey, we can get
started up, we can get started in like a few days. You know,
we just build the offer in our next kickoff call, which can be
an ideally 24 hours, and we can start getting you wins as soon
as possible this first week. And then once you get them these
dopamine hits, they're like, okay, let's do it. Does that
make sense?
So I noticed you guys notice how I speak now?
I'm trying to create this urgency, right? Like now, now,
now, now.
But again, depends on the clients and the prospect, but I
would always always try to close on a call, right?
If they say, okay, we need to talk about people in house, etc,
etc. Yeah, it depends on your personality. That's true. But
typically, if they like you on a call, you can close them right
away, right?
Okay.
Let me read through the questions that I have here in
the chat.
How many times do you do the affirmations? What does? Okay,
what does that look like in your day to day? I believe that
belief in minds that really manifest. Yes. Like people think
that this, you know, there's an affirmation thing, or is the
wasted time. Trust me, it works. It does work. Because
everything like identity is the root of all mental, right?
And without, you know, go deep into psychology and all of that.
What you're talking about is the root of all mental, right?
So if you tell yourself you are, is whatever you are. Okay. So
if you tell yourself, I'm the greatest closer on earth. Your
mind is going to start believing that. And you're going to
unconsciously start implementing, and your brain is
going to start identifying patterns, right? And it's going
to like, brainwash your brain in a good way. Right? And now we
have this confidence. And you have this perceived confidence
in the sales call. And it really does help. So I can tell you
guys my morning routine back then that was a little bit
different, because I have many things to do. But back then, the
first thing I would do is so I would wake up. I used to have
this thing. You guys are gonna laugh. I have I had this white
board. And I used to write like I put it on my room. And I would
have basically all of those, you know, limits and beliefs, but
in reverse. So I would be scared of sales, I would say I'm the
greatest closer, right? I always have the great copy, right? I
always deliver to my clients. And I would read that, read that,
read that every single day, every single day. And eventually
my brain just started associating with that identity,
right? Because the identity that you have before, you know, you
become a business owner is different than the one you have
after you become a business owner, right? Because people
don't have business problems. They have personal problems.
As you said, the hardest is the follow up to get them on the
call takes so many goes in a lot. Yeah, we don't talk about
follow up like in a few seconds, right? Yeah, I was often was
cool and still struggle. I just get distracted. My question is
when you get people on a call, what do you actually demo? Like
what do you show them visually? Yeah, we have a presentation,
right? So we have in the classroom, just make sure you
watch the sales call presentation with Vishal, right? So I this
is what, okay, the first thing you'd have like a point point or
like a Wednesday call, a little overview of what the system is
going to do, right? What the system does. And then you're going
to the sales call, obviously, you're going to have your
basically your high introduction, etc. And then you'd
have like a little overview of what the system does. And then
you would sprinkle a few questions, right? To sell them
that new identity that they want. So prospects typically
want a new they they don't want the outcome, they want this
identity. So if you're talking to a founder, you know, an
unconscious limiting belief for them is basically they want in
their company to respect them, right? They want more clients,
they want more bottom line. Therefore, people that work in
that company, right, will respect them. Because now they
are getting more customers. Now, and they are a great founder to
continue tapping into that. And a few questions that I sprinkle
when I do, you know, those introductions, etc, etc, say,
what made you hop on a call today? What might reply to my
email, right? And then this is now you basically planting the
seeds for your next questions. And then after that, keep asking
them questions, keep asking them questions, questions,
questions about their business. And now you'd understand how
this how their business works, etc, etc. And then now you pitch
your pricing, right? And ideally, you try to get them on
a call. And what I would recommend is always try to close
them early on on the call. So we can handle objections later on,
right? Yeah, you need to explain to yourself in front of the
mirror when you explain to your friends and family to really get
clean on what we do and how we can help them with our system
and how we deliver it. Yeah. Yeah, that's true. Do you ever
leverage a VSL before a discovery call? I actually never
tried to like having a VSL like a video sales. What I used to do
is I just record personalized looms. I used to record a lot of
right. And then there's Jesse, right? Yeah, Jesse in our
community. Am I muted? He's like one of the best people I've
seen. What's that? Am I am I muted? Or can you hear me? Yeah,
yeah, I can. Oh, sorry. Is this like not allowed? This is my
first jump on the no worries, man. You can speak. Okay, sorry,
guys. Yeah. Yeah. I've been chatting in the group a lot. I
got a lot of personality. You can tell. Yeah, so I just real
quick. All you guys in this community, I truly truly truly
from the bottom of heart. I'm a sensitive guy. I need you to
know that I appreciate all you guys responding and giving
insight. I can see so much love and care. I guess that already
hit the 41 marker with the baby and you get more sensitive as
you get along. But I promise you people who are not quite there
yet, you know, decades before me, life will kind of beat you
up. And it's so vital and important to find a sphere, a
group of family that has the same things aligned. And this is
a really, really great space. So I find myself sometimes
hesitating to ask stupid questions. But the benefit of
getting older is you kind of just stop giving a shit. So I
want you to know, if I can do it, ask dumb questions, like
what the hell do I show people, please ask questions because
someone might be shy to ask that question and you may answer it
for them. So I just wanted to introduce myself. Say hi. I love
this community. You guys are awesome. And yeah, you'll
probably see more dumb questions from me in the general
discussion group.
Listen, man, there's no such thing as a dumb question.
Trust me, there's no such thing as a dumb question. You know, I
even, you know, there's repetitive questions. I mean, if
we keep asking the same questions, like, that's totally
cool, because now it's going to be solidified in our brain,
right? And, you know, this is the entire point of the
community, because, you know, I love helping you guys and I would
never get, you know, I would never be like, weirded out or
angry with too many DMs. I'll make sure to basically reply to
all of you guys, because I do this thing in my routine now is
I batch, right? I have, like, a time tracker and I go into the
community and I come in and I apply to all of the DMs and I
apply to them all. And I try to give everyone the same, you
know, efforts and energy and, like, really think before I
answer everyone. And, you know, that's like, it's like something
that makes me really, really happy, right? Because, you
know, the entire point of the community was never something,
you know, to increase my revenue or something, you know, that
like, I was part of other communities and everyone was
like, yeah, create a community, we can talk about cell systems.
And I was like, you know what, let's just do it. Now we're
compiling like the biggest knowledge base right of cell
systems. Right. So it's like, that's made me emotional.
Okay.
I said, can you hear me? Yeah. Yeah. Yes. Yes. Yes. Hey, hi,
it's my it's my first time to you. I'm doing good, man. So I
just wanted to ask you, you know, you helped me build out
the cell systems for for solar companies. And I really
appreciate that. But I found out just like a few things from
that, you know, with my budget. But because because me basically
I'm my offer is that I'm helping the solar companies by building
them a client acquisition system, running their ads, you
know, be like, for example, we view them voice agents, chat
bots, provide them with data. And we also help them like
running the ads as well, you know. So I found out the system
that the cell systems that you that you created the one for
for ecommerce actually works out for me, right. So I wanted to
ask you that, because I wanted to guess I wanted to get some
leads from Apollo. And I wanted to ask you, can I find solar
companies on Apollo? Solar companies in Apollo? Yeah. Yes,
you can, man. Or you can use Exa. Exa. Yeah, I don't know.
Have you guys been using Exa recently? I don't know if you
saw my my comment yesterday. I told you should you should
record more content with Exa. So yeah, yes. Yeah, yeah. So
essentially, I hopped I hopped on a call with the founder of
Exa, right. And they raised 2 million $20 million. Right. For
their system for their you know, for their software. And
essentially, Exa guys is one of the craziest platforms you can
be using. And let me walk you guys through it. I'm just gonna
go to Exa now. They have something called Yeah, they have
something called websites. Okay. Yeah. And those websites is
they, you know, score the entire discovery, the entire internet,
right. And they find those exact people that you're trying to
look for. And they use AI to populate the entire columns.
Right. Instead of like, so the data is much more accurate. So
for example, I was, you know, just playing with this. So look
at this. So we can find the company, right. Also, you can
find dirt, you know, the decision maker. And all you have
to do basically is just add, you know, like a column. And you can
type in whatever you want, right? Whatever you want. Let
me just remove this from here. So you guys can you want to try
solar? Yeah, you can type in whatever you want. You could
type in solar companies, for example, solar companies in the
US. Right. And then let's just use let's just put 50 results. So
now they basically did everything for us. Yeah. Company
operates in this foreign industry company is based in
yes, it's insane. And you click on this button right here in
this arrow. I think if we get like, like a good deal. I'm
still you know, negotiating with them that would be like that
would be like, amazing. That would be great. Yeah, because I
want to understand how the tokens work through the credits.
So they even give you a reason. I'm just leveraging both. Yeah.
Yeah. And then I want to record a video where I basically match
pattern companies hiring for AI roles, and recruitment companies
already have candidates ready for AI roles. And I can say, Hey, I
can come in, right, and help you guys. Right. But the crazy part
is we can have as a variable company hiring and you'd say in
the copy, hey, we have an internal systems scans to
companies that have those candidates. Matter of fact, this
company is hiring. So I'd have it, you know, any variable in the
copy and just add in our dream email. I think that would be
really powerful. What do you guys think? So you can click on
this enrich, right. And you can add whatever you want. And the
cool part is like this, let me show you. So I can just type in
founder and then find the name of the founder of the company.
That's it. And essentially, you can just click on create, etc,
dude, I like, I guess, I see stuff like this happening. And
this like gets me so excited. And then I'm like, gonna be
distracted all day. I'm like, oh, shit. Now all I'm doing is
focusing on this website all day. And then I'll be like, Oh,
God, I got to pick up my kid from daycare. Like, this is all
I'm gonna want to do.
Like, I mean, you can still use it at the same like, you can
still use it. You know, let's say you couldn't find like, a lot
of leads for your niche, you can still use it. Then I just typed
in really description generates, and I can basically
provide a brief description. It's very expensive. It's
actually pretty expensive. This is why I'm trying to negotiate
a deal. Right? It's actually pretty expensive. Yeah. So you
can also type in something like that. Under name, if not found,
provide hiring manager or hiring roles. Provide the name of the
founder, if not available, the hiring manager or hiring roles
associated with the company, I can just click Create, and
they're going to go ahead and find this for me. Right? Which
is like, it's kind of like clay, but better. And then I can just
basically get results, and then just download my CSV. And it
took me like five minutes. Right? So maybe like, hopefully I
can get a CDL. I think that's gonna help us a lot.
One thing I noticed when we when we have these, we go through
these classroom modules, you know, it's like you have like a
curriculum that you run through in a class. And over time, you
for kind of forget about the old material. And I find myself
like, trying to like catch up on all those modules. But then all
of a sudden, I see like a relevant brand new post that's
been pinned. And it's like, Oh, guys, don't use that use this
now. And I'm like, shit, okay, curveball. Now, I got to figure
out is this the thing I'm going to use now. So maybe I just
wanted to bring this up. Is there a way where we can kind of
notify the club, the community where say, hey, this is how we
used to do it. But this model seems to be a little bit more of
a financial benefit to you all.
Yeah, yeah, you're really right. So if you notice the video of
the personalization, I had to record it, because platforms
constantly change, right. And I want to make sure everyone gets
the most updated, you know, way to scrape leads, etc. So, yeah,
the main idea is I don't like when someone puts a classroom
when they put a video course, and then they just forget about
it. Yeah. I want this to be completely updated, right. So
eventually, if I introduce something in my agency, I want
everyone to use it because now I have a concept that it works.
And it has made me money. So now I can just share it with
everyone. So maybe we can add like, how would you guys want to
structure? Like, just let me know, and I can do whatever you
guys want. Like, whatever thing you want to see, like, just let
me know. And I can just make that happen. Like, how would you
guys want the structure? Because, you know, it's our
community.
Yeah, I think one of the things that just, you know, from
someone who struggles with learning, but when I have these
aha moments, it's quite clear for me, is the classroom video
instructions that you lay out. I think maybe what I could see
being like, helpful for me is if we go into among the dashboard
for the sales system, actually, and we go to classrooms, maybe
you could have a module set up that says like, adjusted,
redacted, or like, change from this to this. And then we when
you click on that module, it'll say old version, and then an
arrow pointing to the revised version, and then your video.
Yeah, this is how we used to do it. But now this is why we're
doing it this way, because it will save us x, y, z. So now,
you know, kind of scratch that blueprint, because you're just
spending way too much money on it. And obviously, I want you
guys to save as much so they can all take a trip to Dubai and
have glasses of champagne.
Yeah, the goal of the community is like, I want this, like,
everyone gets to like, maybe like 10k month, we'd all like a
meetup somewhere. I'd love to do that. Maybe everyone would have
like a like a meetup with everyone somewhere. Imagine 200
people.
Can you send 50 emails per domain? Or you see, you just
went
50 emails per domain. So what I would recommend just send 20
emails per email inbox, right. And then you can you can
increase using the high intensity sending using 50
emails. So the first day 20 emails, so it's going to be 300.
And then the next day, high intensity and then you pause for
24 hours, you can just basically let that email rest a little
bit, increase the warm up with 100%. So you can just
basically trigger DSP that okay, we get in replies, we are sending
those warm up emails and people are applying to us. And then now
this is like a way also in the way with our wish like because
some emails would be sent day one, and the next day is going
to be so many emails. And now we've essentially like, went
through the entire lead list in like just two weeks. But at the
same time, your emails are still in good quality. Does that
make sense?
Yes, it does.
Yeah, yeah, we can meet up in Bali. Maybe we can train.
For sure. Another thing I'd like. Another thing I'd like is to
make more content with this with this platform.
Yeah, excellent. Yeah. Yeah, 100%. Let's talk about the shiny
idea syndrome. And then we can continue. So we had Yeah, you
have to you know, you have to pick your right. So you need to
pick your mountain and become that you know, this is the
person who is protecting that mountain with your sword. It's
like the best way to explain this, right? Because you know,
shiny idea syndrome, like we all have it, right? And the idea
with the still with AI agents, right? Because people now like
some people are like switching, like I see people on YouTube,
etc, etc. And then it's gonna die out, right? Because, you
know, we still need a lot of time to make sure like we have
really good AI agents, in my opinion. But yeah, let me just
go back to our structure around presentation. And let me know if
you guys still can still hear me pretty well. So
there we can. Okay, awesome. Perfect. Okay, let's talk about
follow up guys. So follow up, the majority of your meetings are
going to be from follow ups, guys. Okay, so the formula that
I would recommend you to have the first email, second email,
third email, so just three emails. And then once we have a
tiny bit, right, tiny, just a tiny bit of interest, you can
follow up up to nine times, okay. Until they book a call,
this business is due to every single day. And it happened to
me so many times where I would buy something or sign up just
because they follow it up. I'm going to give you guys a story.
So back then, like a few months ago, I was with my girlfriend
at the gym. And then we went to see the gym. And then
okay, the gyms guys that have kind of like you have the gym,
but at the same time, there's like a cafeteria, there's like a
lounge where you just you know, you can come in train, but at
the same time, you can sit and have coffee, etc, etc. So what
they've done is once you once you just show a little bit of
interest in that gym, what they've done is they asked for
my email and they asked for my phone number. And I remember
the amount of follow ups that I have received from just just in
a few days, they followed up with me like 10 times. And I
eventually just signed up with them. And when I observed I
said, Okay, how am I so you know, how did I did not like
implement the same way of thinking in my own agency?
Because at the end of the day, we are following up because we
want their money, right? Because they're the ones that have you
know, their wallets. So we want their money, we need to follow
up with them. And this case is like a multimillion dollar
company. It's like a big gym. So they don't need me. They don't
need me like to pay them. They already have money. But at the
same time, they see me as a customer. They know that I'm
going to pull my wallet and I'm going to give them money. So
they keep following up. So follow up does never never never
think about follow up as it makes you desperate. Never. Sam
said, Do you follow up every every day? I always follow up
every 48 hours. So I'd send an email, right? So I follow up
with them. And then no reply. This is assuming they showed a
tiny bit of interest. I follow up next 48 hours, another 48
hours until they book a call. Right? Or they just tell me,
well, we're not interested, etc. And look, I still add them
into a list where I can just say, hey, does it make sense to
basically follow up a few months later? And you can just you
know, if you want to really squeeze the juice, you can add
them to your CRM and sell and have like, maybe you could have
like an automation that triggers every three months, or like
every 30 days that sends them an email checking that should, you
know, checking up on them. And you could index so many people
like so many leads that I have. And then I'll put them three
days later, and I've managed to close them. Right? Just because
they had like, maybe something that's happening or in their
business, or maybe they didn't have the budget around that
time. So do you have examples of follow ups? Like what would you
send per email on LinkedIn? Do you send voice or video? Man
videos? I don't think so. Because I've received these, like
daily on my LinkedIn, I never opened them. I assume people
won't be, you know, won't open those video calls. But what I
would recommend is you can send them a loom, right? Loom is low
effort. Loom, or you can just send them, you could just follow
up with them in LinkedIn if they don't reply. Right? So you'd
maybe you'd comment to fill their latest posts. Oh, yeah, my
video stopped recording. Okay. Okay, let me just, because you
know, the camera just after 30 minutes, it has just has to
stop. I use the Canon M50. So hopefully, you guys can see me
now. Okay. Well, all right, perfect. So yeah, I sometimes
they even follow up with them in LinkedIn, right? And sometimes
I even comment to their latest posts, right? And I add value.
But what I would recommend is in your follow ups, make sure to
share some industry insight, etc. Or like, like something
that's not so pitchy, especially like follow up two to three, but
make sure to follow up with them like even 10 times until they
book a call. And then they would really appreciate it because
out like I'm getting so many replies from you know, those,
you know, thumbnail designers, etc. And I had a guy follow up
me like 12 times. And I eventually just hopped on a call
because like, you know what, I was like, you know, just let's
just hear what he has. And now I'm working with him. So he
closed me. So it's all about the follow. Okay. Because you have
most of your meetings and follow ups will come from follow up four
to six. And your first emails, sometimes people will book data
like a first email or third email, right. But again, it
depends. So we have a question. I have a question here from
Vishal. And he asked about basically having a system that
runs automatically and basically automatically scrapes newly
posted jobs. Now, there is a way you can do this. There's there's
two ways, right? I'm gonna walk you guys through this. So yeah,
would you guys be interested in seeing this? Maybe like a system
that, you know, runs every few hours, and then checks if there's
any job postings, maybe feeds that into an email finder, and
then find the email then as you release instantly. Maybe that
could work. But to answer your question, man, just because you
want to you're gonna watch this video after. Yeah, to your
router. Thanks for coming, man. Oh, it's 5pm. Okay, so we have
we essentially went after one more, but that's okay. Okay, let
me just answer the question of Michelle. So there's this
platform, it's called right here, it's called their stack,
right. And their stack is essentially they give you an API,
and that you can use their API to basically query jobs that are
constantly being updated in their platform. Because what they
do basically, is they feed in, right. So they go to, they have
a like a, they basically have like a higher plan, right, and
they're able to connect LinkedIn API, along with indeed, info
jobs, basically 18 other providers, and then they feed in
into their own API. And they can essentially just send you newly
posted jobs. So once you sign up to them, they're going to give
you an API key. And then all you have to do is basically make a
request. And let me show you. So we'd have like basically an API
key, right. And the request is going to be API their stack at
jobs. And then you would have like an authorization there. And
then we have to do is basically taping type in the job title,
for example, marketing manager, for example, marketing manager,
and just on save, right. And then you would have basically like an
interval, like just like basically like two hours, like
every two hours at regular intervals. And then you will
basically run the system. And it's just going to check. So
maybe you would add this to a Google Sheets. And then you would
have another flow that checks or like automatically just search
the rows and checks if there's duplicates. If there are
duplicates, nothing's gonna happen. If there are no
duplicates. Now you can, you know, decide what like what you
want to do with it later, maybe find the decision maker, etc. So
if I click on run ones, you're gonna have the data here, which
is going to be data, job title, etc. URL company, like a bunch
of data that you can have. Countries, cities, country codes,
etc, etc, matching keywords, company, you know, every single
thing. Okay, so this is like the pricey way to do it. Again,
there is like a bootstrapped way to do it. But it's definitely
not scalable. Okay, so this is the MVP that I've built for you.
Actually, the idea is pretty simple. I'm just scraping in
deep, right? So I'm scraping in deep, so you'd have your own
parameters, right? You'd have basically like a job filters.
And then you click on save. And then you just parse the text,
it's gonna be HTML. And then you'd feed this into AI. So you'd
feed this into AI, and AI is gonna basically look at the, you
know, that text, and it's gonna restructure it, and it's gonna
have something like this. Right? So you can have all of this.
Again, same principle. Yes, this is mainly for a good niche.
You can, you know, you can build this and you can sell it to
them hands off. You'd say, hey, I've built a system, right? And
you can bundle that in your offer, right? You'd say I built
a system that scans job postings, right? For companies
hiring for you, you guys as candidates as you guys already
have in-house. And yeah, I can, we can check every, you know, a
few hours, and it could be the first people who actually can
connect to these people and just reach them out. Yes. You can
sell it hands off. And you can basically, like, you could pitch
like 1500, and you'd offer some quick retainer, like 200 bucks
a month. Pretty good. You know, like, you'd have like, like the
initial setup, and then you'd, you know, you'd have like, you'd
handle, you know, there's rate limit issues monthly with some
sort of retainer, basically, like 200 to 300 bucks. Yeah, I
get to see it. One thing that I want to walk you guys through
before we finish is this one. So this is the one that I'm
actually like building out is that we're scraping Company A,
which is going to be basically Recruitment Agency Specialized
in AI roles, has candidates ready for placements. And then
we connect them with companies that has current job openings
for AI. And we'd have a copy that has, hey, we have AI driven
internal systems setup that track candidates worldwide,
specifically for AI roles. If you're looking to fill AI
conditions, I noticed that company name hiring, and we can
have like a little personalization, like it's a
company that does X, Y, and Z, is currently hiring for similar
roles, specifically for job title. And then we can basically
have all of the data from Excel. I think this can be the future
of our, you know, sales systems. If we get the right deal. Well,
we have one last question from Kevin, which is going to be
applicable for clients in the IT space. If you're scraping, if
you're scraping looking for companies looking for in house
IT person, they could, they could a target, they could be a
target for an IT company to reach out to wouldn't be
applicable for clients in the IT space. I'm not sure what you
mean, man. Can you just
reframe it? You're scraping. You're scraping looking for
companies looking.
Yeah, so yeah, the idea here is you basically say, Hey, I can
find those companies are hiring for you guys as candidates,
right? And you is you is X, I will use any platform like this
right here, their stack, you can either their sales, you know,
services, and make.com along with some monthly maintenance
for it. Or you can just say, I can just, you know, get you guys
meetings with those people. This is what you're referring to.
Yeah, I mean, just to reframe the question, then I love to
willing, you know, help you out. I'm just I just don't
understand what you mean with if you're scraping looking for
companies looking for an in house IT person, they could
target it. There could be a target for an IT company reach
out to. Yeah, sure. Sure, man. Go ahead. Yeah.
All right, great. Hey, Kevin. So I was basically referring to
you're looking at this job posting boards and whatnot. So
recruitment companies or companies looking for that, that
space. And my focus is on the IT companies looking so we can
healthcare sector or particularly with a lot of us and
whatnot, but would not be applicable to that space as
well. So let's say I scrape companies are looking for an IT
person in house. However, my IT company is actually trying to
find leads for will not also fit them as well, because they can
then request a meeting with those same companies to say,
Hey, we can offer this service instead of hiring on somebody.
Okay, oh, yeah, yeah, I get what you mean. Yeah, I get what you
mean now. Well, don't you think it could still be applicable if
you can just get them that person in house?
Yeah, yeah, I mean, yeah, definitely. I mean, if I'm
trying to sell systems for an actual IT company that pulls
getting a person directly. I think it's just a different
approach.
Yeah, it's definitely a different approach. Yeah. I mean,
depends on how you position it, right? Yeah. Yeah. So Raj said,
can we have the last question? Can we have extra scraping
LinkedIn profile? If it does, does it use cookies? I actually
haven't tried this. Maybe we can try this. Yeah. I'm not really
sure.
That'll be the last thing we do.
Of, uh, yeah, of companies for sales, just for example. Oh,
yeah, we can. I think we can. Yeah. Anyways, Kevin, I always
have fun talking to you guys, right? Because now we all are in
the same, you know, room.
So I'm talking to my family. So we have, yeah, you can
definitely find company, uh, profile companies.
Yeah, there you go. Yeah, you can.
So there you go, Raj. You can actually.
Like, uh, the access is just limitless.
Yeah. Don't use lead magic. I've tried it. It's not that good.
All right, guys. So thanks so much for coming today. I had a
lot of fun talking to you guys. I always have fun talking to
you guys. Um, let me know if you guys have any questions. Just
you know, make a post and send me a DM. I'll help you guys. And
then, uh, yeah, hope you had the value in this weekly call.
All right. Great. Cheers, guys. Big hug for you guys. And I'll
see you guys in the next call.
See you, man.
