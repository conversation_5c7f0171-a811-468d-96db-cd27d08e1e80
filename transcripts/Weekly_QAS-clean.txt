Okay, let's just wait for everyone to come in so we can get started.
We have an epiphyll lecture where I will walk you guys through basically some of the hacks
that I use.
That's going to help you whenever you want to scrape leads for yourself or for a client
because sometimes you might encounter a client and it may be difficult for you to find their
ideal customer, where to get leads, how to pick the right scraper just because there's
a million and one in epiphy.
Most people don't actually know how to use epiphy, they just go ahead and search LinkedIn
sales navigator or something.
But in reality epiphy has some very hidden scrapers that you can find in ways that you
might not be knowing.
So what I'm going to do in this lecture is I'm going to explain to you guys how to find
those scrapers and essentially how to use this keyword search system to basically find
ideal customers over your clients pretty much easily.
Let's admit James.
Okay.
<PERSON> can't hear the voice.
So you guys can hear me well.
I don't care.
You guys can hear me, right?
All good.
Okay.
Okay.
So it looks like you have to you have some something with your settings, I believe.
Okay.
Okay, perfect.
So let's just go ahead and get started with this.
Okay.
Let me just share with my screen.
And looks like everything is going well.
Can you guys see the screen?
Just put a emoji in the chat so I can see it.
Perfect.
Okay.
Awesome.
Okay.
So let's get started.
So when you scrape leads, like I said, or trying to build a lead list, right, sometimes you
might find it a little bit difficult to find your ideal customer or let's say you have
a client that is very hard for you to get their ideal customer.
Now I have a solution for you.
So first of all, I want you to guys to think and have this mindset, right?
So always think from the end, right?
So when you're building a lead list, the end result is always going to be the email, right?
So the end simply going to be the email of the person whom you want to reach out to that
has the ability to pull the trigger and pay you money.
Okay.
So once you start thinking from the end and thinking how can I find the email address
of my ideal customer, you can start to reverse engineer it back, you know, right?
So once you understand this, you will realize that actually building that lead list of what
whatever niche you're trying to target is going to be extremely easy, right?
When you understand this, okay?
So the step one, let's say you have a very tough niche and you don't know how to actually
get those lead lists.
Let's say you have like a like doctors, let's say you're working with doctors and you don't
know how to scrape a list of doctors.
Now you can scrape Apollo or LinkedIn, but there are other platforms that actually offer
you lists and actually leads that are doctors, right?
Specifically for that exact niche.
So the step one is always look for scrapers that give you the website slash domain first,
right?
So the website URL is the foundation of your list building process.
So when you actually are trying to build a lead list, try to look for scrapers that actually
give you the in the output website domain, right?
Just because it's easier for you to basically find the end, which is the email address,
because if you have the website domain, you can use enrichment tools such as an email
finder or hunter, right?
And you can find the company's email using using these platforms, because with an email,
you can reverse engineer the first name by either using a split formula or getting the
first name using AI.
So for example, let's say you're targeting, let's say you have a client that has doctors,
okay?
So what you're going to do is you're going to go to Epiphy and you're going to try to
look for those scrapers that I'm going to explain to you how to find them.
And then you're going to see if the output of that scraper is going to have the website
domain.
So once you have the website domain done, right, that's literally done, you can essentially
now have like a powerful outreach.
And you might be wondering, well, now I have the website domain, like how can I find the
first name last name?
Well, you can find it using their domain, like I said, right?
So you can just feed in the website domain to an email enrichment platform, and you can
basically just retrieve the first name of that results.
So sometimes you're going to have, for example, an email that has, let's say, my email, right?
Like for example, just like this, myoprocess.com.
Now if I fed in myoprocess.com, right, to an email enrichment platform, I can essentially
retrieve that email and I can just take in the first part of the email, I can just feed
it into AI using prompts that are updated in the classroom, I just updated this a few
days ago.
And those prompts are going to basically look through the email and they're going to be
like, okay, what is the first name here?
And you can just retrieve it.
And then the next method is by using a split formula.
So whenever I am building a lead list and I can find the email address, it's very easy
for me to find the first name and last name, right?
And now since I have the first name and last name, I can essentially do what?
Scrape the website data, right?
Now I can essentially just scrape the website data and then get the case studies.
And now I just essentially just found the solution, okay?
So the mindset that I want you guys to always be in is how can I build a lead list?
First step one is look for epiphy scrapers that give you the output such as domain.
So once you have the domain, you can essentially just find the email address and can reverse
engineer from that and find the first name and last name.
So I hope this makes sense.
Do you guys understand in the chat?
Just let me know.
Send me an emoji here so I can see if you guys following along.
Yeah.
Okay, perfect.
If you guys have any questions, just leave them in the chat because it's best to have
it interactive so we can basically answer your questions in real time.
Okay.
So now that you understand it, you can essentially build a lead list just from a website domain
if you find the right scraper.
Now how do we actually find the right scraper depending on the niche that we're targeting?
So we're going to be using epiphy.
So epiphy, you guys already know epiphy is like the best scraper to ever exist just because
there's like thousands of high quality scrapers that you can use, right?
And you also have direct access to the developer who actually built the scraper.
So most of them compete for us since there are thousands, right?
And one developer that every day builds a better versions to compete and bring in people
like you and me to use them.
So people usually when they scrape data, right?
And like most agencies that I talk to, they can't find exact scrapers.
So what they do is basically they look for a scraper in epiphy such as LinkedIn scraper
or Google Maps scraper.
Now this is like it actually works, right?
But it is very slow, right?
And efficient.
So the way that I would encourage you guys use, it's called the sales systems master
way, aka searching using industry specific or unconventional words.
So you might be wondering what are these unconventional words?
So you'll be able to scrape data and pull scrapers designed for exact needs that you
wouldn't find otherwise.
So for example, let's say you want to, you have a healthcare client, you can just type
of trying to find the platforms that scrape those doctors, you can just go in and to epiphy.
And instead just type in doctor, okay?
If I go to epiphy, I can just type in doctor, right?
And now I have access to some of the platforms that I actually don't know, right?
But they list leads that I'm trying to target.
So for example, this Clover health scrapers.
Now I don't know what this Clover health is, but it extract data from Clover health network.
Search doctors, specialists and medical practices by name.
So now I know that this platform exists and I can just scrape it, right?
So there are million and one platforms where we don't know where we can find leads, right?
It doesn't have just to be like Apollo or LinkedIn, right?
Sometimes you might have like a client that has a very specific niches, so you can just
go ahead and look for those keywords such as doctors, broker, some unconventional words,
right?
And you can just basically just use AI to give you all those keywords, right?
So you could say, hey, based on my X niche, retrieve all the keywords that might be found
in an epiphy scraper.
And basically it's going to give you multiple keywords.
It can just go ahead and search in the store and you're going to find those scrapers and
it's going to surprise you that only three or four people are actually using them because
there are, you know, they're basically gatekeeping them, okay?
So if I again look for broker, I can find scrapers pulling real estate agents, financial
advisors or insurance brokers.
So let's say your clients is selling health products or SaaS tools, you can just type
in health, right?
So instead of looking for looking into those leads into LinkedIn or Apollo, you can just
type in, for example, health.
And I can essentially again find a bunch of scrapers right here.
WebMD, scrape details, doctor profiles.
So a bunch of like platforms that I can use and scrape data from, okay?
Which might not be known to the general public, okay?
Again, if I want to pinpoint companies with fresh budgets for new tools and services,
okay, for example, let's say you're working with a client that has a SaaS tool, okay?
And then let's, this is very common, by the way, you might, you might be targeting startups
or SaaS and then now you're onboarding a client.
Now they have a SaaS.
How can you sell that SaaS?
You can find companies with fresh budgets just if you type in VC funded, okay?
Now this is just a keyword, right?
If I go to Epiphy, right?
And I can just search in.
Let me just remove this tab from here.
If I just type in VC funded, okay?
VC funded or VC, there you go.
So web scraper for extensive collection of 2,500 VC firms, okay?
So I can essentially just look for that, right?
And then I can find like a scraper that's exactly scraped websites that list startups, right?
Again, another thing, it would be government contracts, right?
You can just type in government contracts in Epiphy.
And let's say you're targeting companies bidding on government projects or consultants or B2B services, okay?
So you can essentially just find scrapers that extract government procurement data, okay?
So you get the point.
Instead of just looking at an exact platform, just type in like a keyword
and you're going to find scrapers that are going to basically help you achieve your goal faster, okay?
So then the next method is using AI to find hidden websites that have the data you're trying to scrape.
So this is what I mean by targeting by pain and not persona, okay?
So for example, I never knew what DICE is.
So DICE supposedly is like a website where you can essentially scrape
and find recruitment agencies and companies that are actually posting those jobs, okay?
So it turns out DICE is a platform where companies post job boards.
So this is exactly what you should be doing.
So you basically feed in like data, right, which is going to be websites that match your ideal customer to AI
and say, hey, where can I find lead lists, right, that match pattern this exact website?
And if you guys check the classroom, we have the keyword search system, right?
So when you onboard a client, right, you have a question in the onboarding questionnaire, right?
So the question is, what would be an ideal customer if you have it, just give us the website URL of that ideal client?
What you can do is use the keyword search system and essentially just find all the keywords that are constantly being mentioned in that website
and essentially just feed into AI and you can essentially find scrapers that can scrape all this data that match pattern their ideal customer, okay?
So this hack, I use it all the time, okay?
You can essentially also use it to basically build your offer, build your copy based on the keywords that are constantly being used in that ideal customer.
And you can essentially just observe and you're going to see that these keywords are constantly being mentioned.
And now you have basically a lot of information so you can do your market research, okay?
So again, you can just type in H1B visa sponsors.
You can find scrapers tracking companies that hire international workers.
For example, let's say your client runs an e-commerce agency, okay, and that helps sellers use inventory better
and have a better pricing. You can scrape SQ.
If you just type in SQ and Epiphy, you can find basically extract data on shaving, which, yeah, this is the one, bestbuy.com.
Scrape SQ products from bestbuy.com.
So essentially you can find scrapers that do the exact same thing that you want just by just feeding in keywords, okay?
Does that make sense, guys?
Send me an emoji here. Yeah, the gorilla.
Okay, so again, another thing which I want to help you guys with is always read the documentation, right?
So whenever you're looking for the scrapers, always make sure, you know, before we run it,
read through the documentation to understand the tool's limits, condition, and best practices.
Because scrapers come with specific rules around rate limits, data structures, and what kind of data are we pulling.
So does it include names, company names, emails? Make sure to dial this in, okay?
Always look through the documentation and make sure the end result is going to be the website URL
because it's going to help you all the time, okay?
In terms of how to not get blocked is always use residential or special proxies, okay?
So if you go to any scraper in Apify, right, for example, I'm going to go to my actor here.
I was scraping crunch base.
So always make sure it's in either residential or special, okay?
My recommendation is always use residential at first.
When you get rate limited, now we can pull the special proxy, okay?
Now you have like additional 10 to 15 scrapes in like a week,
which is going to help you a lot when you are building a lead list, okay?
So you guys have any questions? Just let me know in the chat.
Yeah, so Jason said before using a scraper, how do you check that it gives the website URL?
So you have to read through the documentation, okay?
So if I go to, for example, right here, if I go to crunch base,
if you go to information, you can see the output, right?
So company data fields, right?
You're going to have identifier and you're going to see that they include websites.
Let's read through this.
Yeah, websites, websites, websites.
I believe they have here a website.
Last funding at website URL, maybe?
Yeah, website.
Yeah, here is it.
So websites.
So each scraper is going to give you exactly like what kind of data you're looking for, okay?
So, okay.
Now, okay, for the LinkedIn scraper, when using it,
do you risk a ban on your LinkedIn account?
If so, how do you avoid this?
Yeah, so what I do, so if you want to scrape in volume,
you're going to have to get burner LinkedIn accounts, okay?
Just because you're only allowed to scrape 2,500 leads per day, per account, okay?
So if you scrape more than 2,500, you're going to eat like a little ban.
They're going to give you like a 24-hour ban.
It's not something, it's nothing serious,
but you can just get basically LinkedIn accounts for like $1, okay?
I know a couple of platforms.
I believe someone in the community just DMed me about it and I sent them like a link.
Maybe if you want to scrape in volume, I can just send you the link.
It's totally reliable and it's the one that I use.
So these LinkedIn accounts is basically, they're warmed up
and they have like two years, basically.
These accounts have been created like two years ago
and you can just scrape 2,500, 2,500, 2,500.
Yes, okay, sure, 100%.
Sam said, how do I know when I'm blocked?
I don't have any access to the current Apollo scraper from Curious Coder.
Just says that I can't load it and I should contact Apophys.
So I had this exact issue three months ago.
So basically, I was scraping Curious Coder, actually, and I got the same issue.
So what you have to do is basically change your browser and use VPN.
Are you using VPN?
No, currently, I'm not using any VPN.
Yeah, you should use VPN.
Like any VPN, it could be just a free VPN, basically.
It could be just Proton or something.
Just a free one and change your browser, okay?
Change your browser from Google Chrome to Brave or something
and then it's going to be fixed, right?
You probably scraped a lot.
Yeah, I did, I did.
And instantly, it was like I can't access it anymore.
But thanks a lot.
I will try it.
Just change the IP and there's a couple browsers you can be using,
AdsGuard, you can use Brave.
Should you use a VPN when using these other accounts?
Not really.
Since you changed the accounts, there's no reason for you to use a VPN
because these accounts are different, right?
They have a different cookie, different session cookie.
So you can just go ahead and scrape them.
Yeah, I'm going to send you guys the websites.
It's like $2 per account, I believe.
Yeah, which is extremely reliable.
Okay, amazing.
Okay, let's just tackle the questions that we have in the thread for multi-accounts.
Okay, that's a great, I've never heard about this.
Let me just, oh, this is like a browser.
Oh yeah, for multi-accounts.
Yeah, I've never used this actually.
I always just use AdsGuard or use Brave.
Between Brave and Google Chrome.
Yeah, okay.
So we have a couple questions here in the thread that we're going to answer
and then we're going to go ahead and answer all of you guys' questions in the chat.
Okay, awesome.
So, Hi Saad.
I would love, what I would love is like a day in the life of what you do basically.
How often do you run campaigns?
It's just called email, like workflow.
You follow typically to generate your impressive income figures
and what would we do as newcomers?
Okay, I'd like to generate, I'd like to have an outline plan
on what to do day by day in early days.
After I've set up, you've shown in the 12 days agency.
Yeah, that's a really good question then.
So currently, my workflow is a bit different to when I first got started.
My workflow now is if it requires a lot of my brain power,
I just delegate it so I can focus on systems.
So my day usually starts with the highest leverage task.
So it's different each day, but now I'm looking to scale more
and tap into the 120K a month.
Consistently, there's purely off my process.
Last month, we've had like 100K, but you're going to have to always think about it.
Like, let's say like when I first got started, like let's say this month, I made 3K.
I always just sit and think how can I consistently make 3K each month, right?
So when you hit a milestone, it's time for you to just say,
okay, how can I consistently reach this income every single month?
What are the things that I've done in this month that yield into that results?
And what are the things that I shouldn't be doing, right?
That didn't basically give me any results that I want, okay?
And then you're just going to have to reverse engineer.
So currently, like to answer your question, man,
my current season now is reading a lot about hiring and management,
just because I've been working with only VAs the majority of the time.
And what I would recommend you guys do, especially when you first get started, okay?
Just work because you're going to retain most of your money
and it's going to be like 90% margins, okay?
You can basically delegate scraping leads, et cetera, inbox management to a couple VAs.
But once you get into that 10K a month milestone.
So now I almost delegate most of my fulfillment to agencies
that I believe they do great work.
Sometimes they mess up.
So I have to basically come in and just fix their mess.
So the goal right now is to basically remove that.
It's called the exceptional founder problem,
where the competence of the business owner
swallow up the majority of the tasks, right?
So I become the bottleneck.
So I shouldn't be the face of the company.
I should probably hire more,
which is basically like a limit and belief that I have.
Just because when you run your business and it's working,
it's very hard for you to delegate
and that you kind of like scared that someone is just going to mess up, right?
So I'm trying to get on more calls with multiple people,
hiring more sales reps that are better than me in closing deals.
So I can eventually just scale, okay?
Because right now I'm going through an entire rebranding of my process.
So essentially just like scaling and just because let me just explain to you guys this.
So when you first, right?
When you first get started from basically from zero dollars
all the way to let's say 50K a month,
right?
Your entire, yes, your entire bottleneck.
So let's say this is your pipeline
and this is your pipeline, okay?
Sorry for my poor drawing skills,
but you have a pipeline, okay?
So you, the biggest,
the biggest issue you're going to have is basically
a pipeline issue.
So pipeline.
So you need more leads coming in into your pipeline, right?
And then closing them.
That's the entire thing you should be focused on.
It's just outreach until you hit 50K a month, okay?
Once you get past that point, then now we're talking about ops, okay?
Now we're talking about how can I template my fulfillment, right?
So how can I do this?
How can I hire sales reps that do my job better than me?
How can I hire inbox management VAs, right?
How can I streamline my entire processes so I can just focus on systems, right?
Because there's something called founder time, right?
But you shouldn't worry about this now
because your entire issue is going to be pipeline until you hit 50K.
Once you hit that 50K mark, right?
Now we can start basically qualifying hard,
not getting into those sales goals that maybe that lead is not warm, right?
But at first, you want to talk to everyone, right?
You want to talk to everyone.
You want to make sure you get as much cash,
as much cash coming into the business so you can reinvest it
and essentially have that problem, which is going to be ops,
templates, fulfillment, automation, et cetera, et cetera, okay?
So my early days, right?
Here's my early days.
The main bottleneck is pipeline.
Your success will depend on daily outreach one
and consistently sending emails and follow-ups to get meetings.
And my evenings would involve fulfillment.
So whenever I wake up, so I wake up, the first thing I do
is basically handling my outreach, whether it's targeting new niches,
following up with interested leads in my instantly campaign,
basically do all of this because this is the high ROI task that you have, right?
Which is interfering with the market and constantly booking meetings
because meetings equals money, okay?
And then when you finish your outreach, then you can focus on fulfillment, right?
I always say get the clients first, then worry about fulfillment later,
which is because you want to have cash coming in into the business
because cash is the oxygen of a business, okay?
So then in evenings, then I would focus on delivering results
so I can build a stronger position and get referrals.
So you'll spend 80% of your time, right, on outreach
and 20% of your fulfillment in the beginning, right?
So once you have like a pipeline, then the balance shifts, okay?
So this is a typical day that I had.
First three months, it was building campaigns, sending more emails,
following up with mom leads, replying to positive leads, ASAP,
and tracking every single lead in my...
I used ClickUp.
You guys might be using Airtable or something or Monday, okay?
And then evenings, I would basically handle sales goals, fulfillment of projects.
Once you had 10K, okay?
Once you hit the 10K, now you can hire VAs to handle lead scrapings
and admin work and inbox management that really doesn't help you.
It's just admin work, okay?
So this is going to free you up to double down on outreach,
meaning scaling campaigns even further, adding more volume,
and focusing on high-value clients, right?
So now you can introduce the retainers to them, maybe partnerships, right?
So let's say you're working with a client for over a month now
when you're delivering the book meetings,
you can start to introduce like higher deals, higher partnerships,
like six to eight months partnerships and retainers.
And then now you can systemize fulfillment by templating everything end to end.
So growth doesn't slow you down, okay?
So I hope this makes sense, guys.
Let me know if you guys have any questions regarding this question
because it's an interesting question.
Yeah.
I have a little question.
What is the purpose of having setup fees?
It doesn't cost us anything to start the process for the clients.
I know, right?
That's funny.
Well, it does require to basically buy them the domains, right?
Unless you have already domains, right?
Unless you already have pre-warmed up domains, then honestly,
you can scrape Apollo.
If you really want to cut down on costs and actually just get that meeting
with like zero costs, you can.
But the only thing that you should be doing
is having your domains ready warmed up
and then you can essentially just have multiple accounts on Epiphy
and scrape 5K leads.
So yeah, the cost is still low.
I mean, think about it.
If you run ads, let's say you run $100.
The ROI is not going to be $100.
But in terms of cold emails, it's very cheap, extremely cheap.
Inboxes are like $6.
Thanks, Jason.
Thanks, Zach.
Yeah, so what I would recommend you guys do all the time,
all the time is focus on outreach
and then your systems are going to change as you go.
Trust me, I change systems weekly, right?
So what I would recommend you guys do is something that I have now
is I have like a weekly journal in Google Doc
where I track my entire week, right?
And I see where I was messing up, essentially.
And basically, I want you guys to block one hour of your morning
every Sunday, for example, and just think.
Just think and see, okay, what am I currently doing right?
What am I currently doing wrong?
How can I increase my income?
Right?
And it's going to help you guys a lot.
You're going to basically find those little bugs that you're doing
and you're going to start just removing them.
Okay, this doesn't yield any results.
I'm trying to remove it.
This got me positive responses.
Let me just, you know, double down on it.
So make sure you're always tracking, always tracking, always tracking.
Okay, so this really helps.
So what I like to do is just dump in all my ideas in a Google Doc
and I just observe as in like a different person, right?
Like what is this guy trying to say?
And I just come in and just tackle all of this, right?
So it doesn't, so my, you know, my decision-making abilities
and all my decisions are not emotional, okay?
So yeah, that's pretty much it when it comes to from zero to 10K, right?
You're going to have to focus on outreach most of the time,
getting more clients, more clients, more clients, more clients,
because having more clients is always better
than having just a few clients, but you're handling full settlements, okay?
Does that make sense?
You guys can speak in the chat.
Yeah, if you stop outreach, lag catches up very quickly.
Yes, because now we have a problem.
So there's something called momentum.
When you basically start your campaign, you start getting momentum.
There's this guy in our community called Nishal.
He's getting booked meetings every single hour, but he's put on the work.
He had one campaign, he didn't get any responses from it,
and he essentially just refined his campaign again,
and he's getting like a shit ton of booked meetings.
So this is like, I believe he's going to post like a win today or tomorrow,
pretty sure, just because he's been DMing me
and I was helping him like closely with his campaigns.
So yeah, guys, like you never want to,
you never ever, ever make a mistake by stopping your outreach
just because you want to fulfill.
Now I get it, you're going to get stressed about fulfillment,
but you're going to have to put yourself first all the time, you know,
because what got you that client is your pipeline, right?
So the funnel, right?
So the funnel has to always be constantly being fed new clients, right?
Because you can offset after all, right?
So all the time, right?
All the damn time, new clients, new clients, new clients,
then we're about fulfillment later, okay?
Okay.
So this question, I think it's from Sam.
Hi, sir.
Do you have any strategies on how to get the highest possible
deliverability rate when verifying scraped emails?
Yes, my friend.
When I'm scraping up polos, sometimes it only gives me 25%
deliverable emails, thanks a lot.
Yeah, no worries, man.
Yeah, you can take it a step further.
You can use a double verification process.
You can use like zero bounce alongside with mails.so.
So in your flow, you would have just me,
this is me just like building a system in my head.
You would have your just normal system that runs through mails.so,
and then you would have the filter.
If email undeliverable, you just send it to zero bounce API, right?
Use and make.
They have like a module there.
And then basically, if it's undeliverable again,
then you know that email is shitty.
If it's deliverable, now you can be sure that, okay,
you're not leaving the juice on the table, okay?
So you can just use a double verification method.
But just keep in mind that even with a solid verification process,
around 50% of the emails might still end up being undeliverable
just because, you know, script emails aren't from people.
Some people might have changed jobs, right?
So sometimes the data is not going to be 100% accurate.
So this is why we're using the verification, okay?
But you can still have a double verification filter, okay?
And then let me just show you how you can do it, actually.
So I'm just going to go to make,
and let me just draft an MVP for you.
So for example, so if I go to scenarios,
and if I go to method one, Blueprint, right?
I believe this is the one.
Yeah, okay.
So you can have like a mails data SO, right?
And then you can have, for example, another filter here.
So it's going to be a router that you can add,
and then you can add your filter here, okay?
So now if email, the deliverable, that's going to continue, right?
Then you're going to have another flow,
and then you're going to have another flow if email,
so results equal undeliverable.
You can essentially just add in zero bounds, right?
So zero bounds, and it's going to be validated in email, okay?
So you can have another layer, right?
That's going to check if the email is invalid,
and just going to go run through another flow, right?
And then you can continue your second flow here, right?
It can just copy and paste here.
And then if results equals deliverable again from zero bounds,
then you can continue, right?
If it's not, then it's going to stop here.
Now you know that the email is shitty,
and the system is going to continue running the next bundle.
So hope this makes sense for you, man.
So this is kind of like visual so you can have an idea.
Okay, next question we have.
Okay, so system build request.
Nurture with lead magnus and free value until 24 hours before meeting.
Yeah, that's a really good idea, man.
So what I would recommend you do
is whatever you find valuable in the community, right?
Or let's say whatever you learned so far from the community,
you can go ahead, and I want you guys to win.
You know, there's no gatekeeping.
You can take things that you learned
or something that you find valuable that's going to help your client,
and you can essentially just build an SOP
based on what you can find in the community,
and build like a basically like an SOP of like emails,
or like let's say every 48 hours,
you just send them like a free value, right?
And you have my blessing, you can use resources here,
and basically just add you a little color to it,
and build lead magnus for them to basically keep them on the loop
and give them free value until they essentially hop on a call,
and then you're going to stay in their mind a lot if you do so, right?
So I've implemented this before where I would just have like in my clickup list,
what I would do is I have that book meeting with that client,
and then every three days,
like there's a trigger that sends them like an email,
saying then like just like explain them more my offer
and how our system work, et cetera, et cetera.
So when they come on a call,
they're kind of like, you know, a little bit warmed up, right?
But some elites didn't read my emails,
but majority of them, they're like,
okay, I appreciate you sending these emails.
Now I like kind of understand your offer,
understand how your systems works, et cetera.
And they're more likely to be closed on a call, right?
So feel free to use resources from, you know,
what makes sense for you or things that you learned, right?
Maybe how to use AI, et cetera, et cetera.
It can essentially build your entire SOP, right?
And it's just going to be a rise and repeat, okay?
So you would have like an SOP,
and you would have like an email in make, right?
That's just checks in every 48 hours
and just sends that exact email
to that exact person in that task, right?
So then click up, there's a task.
And that task is going to retrieve first name, last name,
email, et cetera, which is already booked in your CRM.
And you can essentially just send that to them
every 48 hours until like the meeting occurs.
So I hope this makes sense.
Let me know if you guys have any questions
regarding that in the chat.
Okay, we have a question here.
If you stick to one niche,
I think everything can be automated.
Yes, that's true.
Agency outreach on autopilot every day,
delivering the service like SaaS machine.
The only thing standing between us and word domination
is closing those new clients,
unless of course we can teach AI to close humans.
Yeah, if you can find one niche
that you can consistently get results for, think about it.
Like it's going to snowball for a lot of things.
You're going to get referrals,
you're going to get access to their network, et cetera, et cetera.
So now you can essentially just rise and repeat.
And you might be wondering, well,
how can I not like just send them the same lead list?
Well, because it's different, right?
Each niche that you're going to be working with,
let's say you're just working with one niche,
they're going to have like different ICPs, right?
It could be an ICP and an ICP.
You wouldn't know, right?
So this idea that how can I fulfill two clients
at the same niche, like each niche,
each client that you're going to work with
is going to have a different offer.
And that different offer is going to resonate
with a different ICP, right?
Different ID or customer profile, right?
So when I was working with IT Health,
some of them had AI agents
that they wanted to basically sell to healthcare firms.
Some of them had AI copywriting, right?
So a lot of these companies,
even though they operate in the same niche,
they have different offers
and they have different services, okay?
It's kind of like recruitment.
There's not just recruitment,
there's IT recruitment,
there's marketing recruitment,
a lot, there's AI,
there's recruitment companies
that just hire AI developers
or AI roles, right?
Okay, would it be more effective
to target recruitment firms across all Europe
or should we focus on Germany
or another specific country?
Excluding the US,
we observed a lot of negative responses.
Which approach is likely to deliver better results?
Currently, I'm getting out of office responses
in my campaigns.
What should I change or do?
I'm talking in the US recruitment firms.
So I would focus on recruitment firms in Europe, right?
You mentioned Germany.
Yes, Germany, they have a need in recruitment, actually.
I had a client that they were basically based on Germany
and it was very easy for me to fulfill
just because the recruitment agency, right?
They had already roles ready
and companies were hiring for those roles
around that time heavily, okay?
It might be worth looking at your lead lists,
where you got your lead lists essentially
because if it's just out of offices
and most people here are targeting IT
and US recruitment firms there,
they're getting responses.
So maybe you're getting outdated data, I don't know.
Another thing you could do
is you can just download this lead list
from your out of office, right?
And retarget them when they are more likely to be available.
I did this back then in summer 2024
and I got a bunch of deals out of it.
So essentially what we would do is,
so my system is like,
it just watches for every instantly response,
just like I show you guys, right?
And it just adds that to my CRM
and AI just looks through the responses, right?
If it's interested, not interested or out of office.
And now I have a bunch of leads that are out of offices
and I can just download them out of ClickUp
and basically just add them to another campaign,
which is out of offices
and I can essentially just retarget them
maybe like a month later.
Let's say you don't wanna like leave that juice
and you can get a bunch of deals out of them
because there's still leads, right?
There's still leads.
What is the sales agency end game for you?
So I used to run in my own process.
Of course I am running my own process.
If I don't run my own process, it's gonna collapse.
Yeah, like I said, I'm focused now more on hiring.
I actually just got on a call with a bunch of people
that I'm looking to hire as sales reps
and none of them actually stand out.
So it's kind of like different for me since I'm like...
I don't like getting on sales calls a lot with people,
which was like a limiting belief that I had
when I first got started.
But yeah, I just have to do it.
Raj, one of my campaigns got paused by instantly
showing the message paused due to too many recent bounces.
Yes, leadless alert, leadless alert.
So it looks like the leadless is not good.
Yes, looks like the leadless is not good.
Did you run through the leadless through mails.so?
Was the best way to tackle this?
I was thinking about deleting all the leads.
Yes, yes, instead of deleting them,
just download them using instantly, right?
Just download them and then verify them using mails.so.
Yeah, in terms of the emails that have already been sent,
you can't do anything about it
because they're already sent, right?
So just download the remaining ones, pause the campaign
and the reply rate is gonna be 100%
and make sure you warm up,
make sure you leave the warm up for like one to two days, right?
Because bouncing rates just means that the leadless
that you just scraped was not deliverable.
Those leads were not deliverable.
Yeah, yeah, were there any?
Yeah, what's up, man?
Yeah, so I did run it through MillionVerifier.
As with mails.so, what was happening was
even when there were some valid emails
like saad at myoprocess.com, right?
And I also ran it through my own email, raj at clickpresso.com.
I ran it basically a few valid emails,
but it was showing that no, those are invalid.
So I just stopped using mails.so for a while
and I was like, you know what?
Let me just try MillionVerifier
as it was something that I used earlier.
So you used MillionVerifier instead of mails.so?
Yeah.
Yeah, MillionVerifier.
So yeah, this is why like a bunch of people are asking me
why is mails.so only output in 30% of the emails?
Well, because they always exclude catch all.
Probably MillionVerifier also includes catch all emails, right?
So that might be the issue.
So the actionable steps.
So don't get stressed, you know, don't get anxiety.
All you have to do is just download the leadless,
just run it through mails.so and re-upload it instantly
and continue your outreach.
All right. Thanks, Ar.
Yeah, no worries.
Were there any more tips and tricks from Apollo
that you would like to share?
So in terms of Apollo, yes, I recorded a video
for the community I posted.
Probably it was Monday.
Did you check that video out?
Basically, it was a hottest crape Apollo et cetera.
Yes.
So one of the few hacks is that I explained that video
is that Apollo is kind of like a keyboard search system, okay?
So instead of just looking for industries,
you can just feed in keywords
and you can essentially find those leads.
For example, if you just type in founder,
now you saw the video, okay, yeah.
Okay, so it looks like we don't have any more questions here.
Let me just read through the entire chat here.
Great, I've done it the wrong way,
trying to build a reputation.
Yes, yeah, build a reputation first, right?
And still believe on delivering, yeah.
Okay, so I'm going to go ahead and do a little bit of a
demo, okay.
So it looks like we have all those questions already here.
We have time just flies with you guys.
We only have like three minutes left, I believe.
Hi Seth, can you hear me?
Yeah, so if you guys have any questions,
just let me know in the chat
and I'm just going to go over and answer them.
Am I muted? Can you hear me guys?
Okay, I was looking to Instally today
and it looks like they automatically paused
the out of office replies.
Yes, yeah, and resurrect when the internal AI
has found them to be back.
This is a new feature, yeah,
which I really like about Instally.
Back then, they was basically just pause
and they'll just mention it's out of office.
So yeah, I use this nowadays, yeah, too.
But the best way is to actually just add them
to like another lead list, right?
And now we have a more relevant personalization.
You could say, hey, I reached out to you back,
but you were out of office.
Hopefully had a great vacation, et cetera, et cetera.
And you can essentially just re-hit them back.
So yeah, I think that's pretty much it for today's session.
Hopefully you guys had, I was wondering
if you get clients from your social media activity.
Yeah, I do get a bunch of inbound leads currently
just because since I'm, you know,
kind of like built this reputation now
about sales systems, right?
So, you know, definitely I get some inbound leads
but I'm trying not to focus on that heavily
just because I'm a firm believer of doing what already works.
So essentially like I did not need inbound leads
to come to me, like I've built like everything
from the ground up.
So I'm like, I'm afraid I'm gonna just focus on inbound
and like just not give a lot of attention to my outreach,
which is basically something called second order consequences
is that the things that you do today
are gonna be, the results you're gonna achieve
are gonna be in like a year, right?
Or like six months just because it takes time, right?
And you actually don't know what are the things
that you've been doing in the past that actually worked.
So this is why I always recommend you guys track
and things that already worked, just keep doing them.
Just keep doing them.
Don't try to change.
But I do get sometimes like I do get inbound leads, right?
But sometimes it's like a second order consequences.
Yeah, why I don't sleep at night.
Yeah, the second order consequences.
Yeah, it's you always have to think long-term.
This is a mental model.
So for example, if you like one action,
if you do one action today, let's say you send
like you start a campaign today,
you might get a booked meeting two months later
from that campaign, right?
You might get a client from that campaign
like two months later and you don't know, right?
So it's like each action that you do has a reaction,
but their reaction has another reaction
and another reaction is just snowballs, right?
So yeah, hopefully this answers your questions, Victor.
Hopefully I said your name right.
If I said, if I butchered it, I'm sorry.
Yeah, can you hear me guys?
Because I think my, oh, that's great.
I think I told my mic got broken.
Anyway.
Oh, we can't hear you well actually.
Yeah, your voice is very low.
You're just typing in chat.
Okay.
So this should be our last question of the day.
My mic is showing me and always, man.
Yeah, you can just type in your question.
I'll answer it.
Okay, is it better now?
Yeah, we can't really hear you, man.
Your voice is like very low.
Okay, let's see.
Trying to not skip any questions
just to make sure everyone's questions have been answered.
So that's pretty much it, I think.
Okay, so we have the last question from you, Victor.
Okay.
Ah, Victor.
Okay, I was waiting for your question, man.
Everyone's waiting for your question, damn.
Okay, guys.
So hopefully you found value in this lecture today.
And yes, yes.
Thanks, Axel.
Thanks, Jason, for coming in.
Thanks, Victor.
Thanks, Raj, Jason.
Who else came in?
Yes, Sam, Zach, Sam, again, Nicole.
Thanks for coming.
I'm going to send you over the websites
where you can get the LinkedIn accounts along with Axel, too.
And I'm going to follow up with you, Axel,
when it comes to your fulfillment.
You can still fulfill, man.
Yeah, we're going to have to find a way somehow.
Okay, thanks, sir.
Thanks, everyone, for coming.
And great to join in person from once.
Yes.
Launching more campaigns today.
William & Gates luck.
Yes, that's a homozy coat.
Okay, Aaron.
Cheers.
Cheers, guys.
Ciao.
