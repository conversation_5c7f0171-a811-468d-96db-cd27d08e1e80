Appreciate everyone's time coming in. Our programmer is here. Hey <PERSON>, hey <PERSON>, hey <PERSON>.
If you guys don't know, or in case you guys missed it, <PERSON> is actually building an internal email finder.
That not only just finds the emails, but also validates them for us.
And it's going to be just for the community. It's going to be never shared, just for the community, obviously.
And the cool part is everyone in the community is going to have 25k of email validation and finding tokens.
So it's like, if you compare that to an email finder, it's like $300.
And in the next couple of days, I'm going to be sharing the code from <PERSON><PERSON>.
So I've managed to push a little bit harder.
Yeah. Yeah, man.
So we have now Exa. Exa is going to be a little bit more than 14 days, obviously.
I had to push harder. It's kind of like going into negotiations with the founders for like three calls.
And I had to push harder.
So we're going to have <PERSON><PERSON>, which is basically like the entire lead generation for us.
And then we're going to have like an email finder tool just for us.
So basically, like acquiring customers is going to be like $0.
Like the only thing they have to do is just get instantly and get domains, which you can get from Namecheap just at like $3.
And the only thing that you're going to get is just basically the emails.
And that's pretty much it.
So that's like even lower costs for everyone so they can keep more of their margins.
And that's how we make money, right?
But yeah, let me just go ahead and share my screen with you guys.
And then let's go ahead and tackle these questions.
So let me know if you guys can see the screen and everything is good.
So I can proceed.
Okay.
Okay.
Perfect.
So you guys can see me.
You see the screen.
Voice.
Great.
Okay, perfect.
Let's just go ahead and get started with this.
Let's go.
Anyways, so the first question we have.
So these are all...
Okay, <PERSON> said, I've got all my 10k lead list data exported from <PERSON> and email double verified in a Google Sheets.
What's the best way to add that data into a cold email personalization flow in Make?
Yes.
And what's the best way to process 10k leads?
How would you split this to avoid errors?
Yeah, so the best way to add that data into a cold email personalization flow in Make is basically it's going to be super, super straight forward.
And easy.
Okay, it's just going to be like three modules that I'm going to show you now.
Okay, so you have already exported the lead list from Apollo and you've email and you've double verified the email.
So step one is going to go to Make.
Right.
And I'm going to show you exactly what to do.
So just create a new scenario.
And then all you have to do is prepare your lead list.
For example, I have like a lead list here, which is IT support.
No, that's a series.
Okay.
So this is a series B company CEOs that I scraped.
Okay.
So all you have to do is go to Make and you're going to use search rows.
Okay.
So search rows, obviously.
So she's going to retrieve that data.
And I believe it's in this account right here.
Yeah.
Okay.
So just copy spreadsheet ID, paste that in here and limit.
Just put one sheet name.
It's going to be the name of the sheet limit.
Just put one and just retrieve the data just normally.
Okay.
So now you have already the lead list.
Okay.
So it's already here.
Have the name have the valid email.
So you're pretty much good to go.
The only thing I have to do is just add the entropic or whatever.
Right.
And then just go to basically seven figure prompts or the my process playbook that we
have in the community where we have basically all the prompts.
For example, you'd use just for example, let's just pick one.
Okay.
Okay.
Let's just use this one right here.
Okay.
So just copy it and go back.
Put your tokens.
Okay.
Add your prompt.
Right.
And then just add the data that you're going to retrieve.
Right.
So it's either you're going to add already the data that's already inside or you can
just add EXA and perform a search.
Right.
And all you have to do is just add the website number of results.
Just one.
Okay.
Include content and response and make sure it's company.
Okay.
So category is going to be company.
Click save.
And now you're going to have to map the text here.
Okay.
So you're going to add the text.
And the last step is going to be just go into Google Sheets and it's going to be update
a row.
Okay.
So now the only thing I have to do just add the personalization.
Okay.
And spreadsheet ID is going to be the name which is I believe it's series.
Is this is account.
I think we have to go from all.
And just copy the spreadsheet ID.
So it's going to be spreadsheet slash D and before edits.
Okay.
Always.
Okay.
Great.
So she name is going to be shit one.
Just hard code it.
And the row number is going to be the row number that you have here.
Okay.
And now since you have everything here.
You're going to just let me see.
Let me just do a test here.
Okay.
And I believe.
Let's try it.
Which is going to run once you perform any search.
And then you generate personalization.
And we're going to have a little error here.
Just because we need to get access to every single thing after.
Okay.
So we have.
The first liner.
Okay.
And let's just put.
The text response here and then there's a test.
Okay.
I think we're going to have an error here.
Pretty sure.
Yeah.
I knew it.
So okay.
So shit one.
Let me see.
Oh yeah.
It's deliverable.
It's not shit one.
There you go.
So now now we got access to it.
So you're going to add a little.
Row here which is going to be just personalization.
Okay.
And then now it's going to work.
Okay.
And before I actually do anything make sure to refresh everything.
So once you refresh now I have.
Personalization and now we just have to map everything.
Okay.
And click on save.
And then run once.
And then you're going to see it here.
Okay.
So great.
So now we're going to add the personalization.
And the only thing you have to do is click on file.
And just download your CS we just uploaded directly to instantly.
Okay.
So this is what I would do.
Yeah.
So because you want to update the row.
Otherwise you can just delete it up there.
And just add it to your instantly account directly.
Okay.
So this is like what I would recommend.
So your next question you had here.
Was the best way to process 10k lease.
How would you split this to avoid errors.
Yeah man I would split this based off.
I would run.
So here's what I do.
Okay.
So for me like I don't use the search rows.
I just directly just pull from get data set items.
From a fight.
Right.
And then I just use offsets.
Okay.
Because I don't want to deal with.
If I have like an error here.
I need to like create a new sheet.
Copy the ones that were personalized.
And then I have to like filter out and do all of that manual.
So what I do is just offset.
Okay.
So I would recommend.
So instead of just pulling.
But in your case you have the the search row.
So we're going to we're going to have to do it regardless.
So yeah man.
So I would just split.
I would have like a Google sheet that has like.
Maybe like a thousand maximum.
Okay.
And then I would just run the entire flow.
And it's going to it's going to basically personalize everything for you.
And so one thousand and just chunking and chunk that out.
Okay.
Just chunk one thousand one thousand one thousand one thousand.
Because if you go one over one thousand going to get rate limited with.
Either X or.
Entropic or yeah.
So just use the offsets.
You know.
So offset is going to skip the first one.
So what I would do is like eight hundred.
And I just send two like eight hundred.
And then I offset the next eight hundred.
Right.
So if you guys don't know.
Let me just show you guys like so you guys can see it.
Instead because I know you guys a lot more visual.
Kind of like me.
You need to see things so you can understand.
So let me delete this one here.
Add this here.
And then now.
All you have to do is for example I have limits one thousand.
Right.
So I processed one thousand.
Now I'm going to office one one thousand.
Okay.
Now it's going to skip the first thousand.
And it's going to give me the second one thousand.
This is how like the logic works.
Okay.
So I hope this helps man.
Make sure to follow up with me if you have any more questions.
Okay.
So got the answer in chat GPT I'm going to add a status.
So we had the way it works is.
Hey J.
So the way it works is you have to.
Just put in a question in the thread.
And then I'll just go ahead and just.
Prepare them so I can just give you the best quality possible.
And the first forty five minutes we just go over the thread.
The questions and the thread for example here.
And the last fifteen minutes kind of like a more interactive.
So it's kind of like more fun so you guys can ask me questions.
And we can just interact with each other.
Kind of like more natural more fluent.
But since you're here first time you're here.
Just treat it like a thread.
Just ask me what I like whatever questions you have.
And I'll just answer them to the best of my capabilities.
Okay.
So no worries man.
So got this answer in chat GPT.
I'm going to add a status called message not created.
Then use the search rows.
Yeah that's a really good smart way actually.
Yeah.
So it's like chat GPT smarter than I am.
Yeah message you would add like a column here.
Which is going to be.
Message not added.
It's kind of like when you add deliverable.
Undeliverable for your emails.
Yeah that's smart.
Yeah.
This is good.
Yeah.
Okay so the next question from Max.
Fellow Jungin.
Oh wow man.
Of course man yeah.
I'm all about the Jungin.
Psychology right.
Ah man.
You're my best friend now.
Okay so how long is the typical onboard in time before our start.
Before we start delivering the results.
Yeah that's a really good question man.
I anticipate minimum 21 days to buy and warm up new inboxes.
When it comes to.
Sounds like a long time.
Curious about how you'd make clients feel good during that process.
Okay man so I have like a couple hacks that you can use.
That I still use till this day okay.
So here's what I would do.
Okay.
So step one.
Obviously during the onboarding you set clear expectations.
So what you would say.
You'd say hey.
The initial offer creation and campaign setup takes between 14 to 21 days.
Just to ensure like we have the best deliverability.
And we can actually like reach out to the people.
Because this is like a part of the process right.
So make sure you always discuss this.
And the onboarding okay.
So it just implies that you are intelligent.
And you are not just a normal service provider.
You're a growth partner okay.
So you would just set clear expectations from the beginning.
And then here's what I do.
So you would set up a shared project board.
So for example I use ClickUp okay.
And then you would what you do.
You just send them like a view only access.
That has like include milestones, upcoming tasks and due dates.
And here's what I would do.
So for example this is John Doe.
Like an example client.
You would have their email.
Their company name.
And this is like the CRM that I'm going to be sharing with you guys.
Probably someone in the Friday or something like that okay.
Because it's almost done.
It has all the automations that you're going to need.
So you can get up and run as quickly as possible.
So you would have their email.
You would have their company name.
Yeah no worries right.
And then you have their quotes.
Because you want to track how much they paid you okay.
And then you see this 10 here.
This is the number of booked meetings that you want to get them.
It's going to be basically the deliverable yeah.
And then you would have like step one.
Purchase domains X number.
Step two.
Purchase email accounts.
Set up DCAM etc.
Scrape leads.
Validate and enrich.
Brand research.
Copywriting.
Generate AI personalization ads instantly.
So every day they would come in and they see you.
They see like progress updates.
So they know like okay this guy is actually working you know.
So it's also going to remove that buyer's remorse.
Because when someone pays you they expect immediate value.
So I would do this.
Another thing what I would do.
Is yes.
Another thing I would do is you send them regular updates.
So what I do okay.
And here's what I would recommend you do.
So their clients don't actually like annoy you.
Because some clients can be annoying.
And they would expect you to like be their you know their best friend.
And like talk to them all day.
But that's not what we do here okay.
So what you do is just send them regular updates.
To inform clients that the process is moving forward.
So my recommendation would be sending them like progress updates like twice a week.
So let's say like Monday.
And then you send them like you know Thursday or something okay.
And then you would define your availability.
For example for me it's Monday to Friday.
From 10am to 3pm.
So they can ask like ask me questions etc.
And then I just batched all my clients like in that time frame.
And it's essentially after I batch community DMs.
And like I reply to everyone.
And the next thing would be like replying to clients.
So you guys are the first people to actually respond to.
Which is.
And here's my template that I use okay.
So I say hey first name for example.
I hope all is well is in the end.
And wanted to check in that we've completed.
For example specific deliverable.
Example lead generation campaign.
Email set up for company name.
It's been great working on this.
And I'm excited about the impact it's going to have for you.
So as you guys can see here the tone is you know it's professional right.
While still you know making sure they don't you know step on your toes.
Because some clients can be annoying okay.
Let me know if you have any questions anything else on your mind.
Typically when you front load.
We've completed X Y and Z.
They're not going to give you any questions okay.
Looking forward to knocking this out of the park for you.
Once and another thing okay.
So once you scrape leadless send it in a Google sheet.
In an aesthetic format to showcase deliverable.
So what I would do.
So step one okay.
Let me just walk you through it from the beginning.
They pay you today.
I want you to get the domains the same day okay.
So get the domains.
Get the emails.
Link them up to instantly so you begin warm up.
It's also going to basically remove all that hassle.
If you're worrying about fulfilling them later.
You're going to be like I need to link this.
I need to do this.
Like I understand.
So once you onboard them okay.
This mental model is going to help you guys.
If it's going to take you like 20 minutes.
To purchase the domains do it now.
Okay.
Any task that's going to take you 20 minutes just do it okay.
So make sure you get them the domains.
The emails etc.
And finish that like same day.
So it's even like.
Like when they pay they expect after 14 days.
So it's like literally 14 days to 20.
14 to 21 days okay.
And then once you scrape the leadless in the next following day.
Just send it to them.
You can either use Slack.
For me I add them to my Slack okay.
And then I just add them in like a channel.
Like my process ex company name okay.
And then basically I would just send them progress update in the Slack channel.
You don't have to use Slack.
You can use email.
Some clients prefer email okay.
And then you would send them the leadless once it's ready.
Okay make sure it's beautifully formatted.
Because like the end result is the leadless.
People are like clients are going to see.
They're going to be like okay.
This is a you know this agency knows.
Like what they've been doing this for a while okay.
So this is my recommendation.
Alternatively if you'd like to speed up the process.
You can just buy pre warmed up emails okay.
So this means you would like to skip the 14 to 21 day warm up period.
And start sending emails right away.
I never had any issues with that okay.
And then once you just buy them.
The only thing that you have to do.
You can just get up and running in like two days okay.
So next day scrape the leads.
Prepare copy and get up and running as quick as possible.
Because now we're interfering with their market as quickly as possible.
So you know what works and what doesn't.
So you can basically refine as quickly as possible.
And get them results quickly.
And the cool part is what I do.
Is I say hey it's going to take 21 days.
And I just buy them the pre warmed up emails.
And then I just send them like the meetings.
And they're like holy shit this guy like.
Like how like it was 21 days.
But like I'm like okay we can over the lever.
So this is kind of like a hack.
So this is kind of like a hack you can use.
So like just it's like a psychological hack.
That you can use too.
So this is like my two cents man.
Obviously if you prefer to just purchase them domains.
Just do it the same day.
And just give them progress updates.
And also give them like a click up dashboard.
You don't have to use click up.
I like click up it's just super easy.
Extremely cheap it's like $12.
Per month.
And they also offer webhooks.
So you can basically go to town with automation.
You can have every single thing.
You can even add like a like a once per week.
You would send them like an email.
Right.
And also you would send them like you would share with them the.
For example the instantly analytics.
Okay.
So that's also what I do.
Okay.
But obviously you can do this once you have like a couple clients.
Okay.
Because you're going to have to refine your process as you go.
So that's my recommendation for you man.
Click up versus Notion.
What is the best?
Well.
Man that's.
Click up is obviously for project management.
For writing etc.
Documentation I would use Notion.
But I'm still like a Google Doc guy man.
I'm still a Google Doc.
Still pretty simple.
Google Doc.
Google Sheets.
And.
Click up.
Make.com.
Pretty simple.
Just focus on things that make you money.
Because there's this thing called platform.
It's called.
When you start relying on so many platforms.
You don't actually do the work.
Okay.
Just pick something and stick to it.
Pick your mountain and stick to it.
Okay.
Okay.
So.
We have another question here.
I'm struggling with my accounting niche to find enough leads on Apollo to fulfill 5000 validated and enriched leads.
I first scraped a list of 2500 leads.
Using the accounting keyword and size 1 to 100.
And then.
I did 2500 leads with the bookkeeping keyword.
But a lot of the leads are returning as already in my Slink campaign.
Method 1 Blueprint.
Okay.
My target location is in the USA.
What is the best way to find enough data.
To get 5000 leads.
I'm pretty sure you can get more than that.
I think I just need to filter out.
And refine your search.
So if you are in a chat.
Just send over your.
You know that the Apollo URL.
And I can help broaden that up.
And get you more leads.
So there's always ways.
So if you are in a chat.
Just let me know if you're not.
Then I'll just reply to you and say.
Send over.
The search.
Apollo.
Indian.
And obviously I'm going to add my.
If they need their needs in accounting.
I have like.
Some leads.
Okay send them.
Let me just tag you.
Sam right.
Okay.
But I just want to share this link with you guys.
I have like 4 or 6 million leads.
Okay.
So this is the lead list?
Yep.
If you want to use it.
There is several.
Categories.
Accounting.
Shopping.
Oh wow.
So these leads.
So these.
Lists have.
Like have never been touched yet.
Right.
For sure.
I couldn't.
Send emails.
To all of these persons.
Would you even get them.
To all of these people.
To all of these people.
To all of these people.
To all of these people.
To all of these people.
To all of these people.
To all of these people.
Would you even get them then?
Yes.
I scrabble a lot on Instagram and Google map down there.
Oh wow man.
Guys this guy.
Is just always.
So I can't.
Okay.
I have a few millions of.
Yeah man.
I'll just give you access to.
Like unlimited extra searches.
searches the next day like Simon's gonna say it so you can click for example in
unique category account oh so we have like 15 how did you get this man
30 thousand leads guys like I'm not saying anything just refresh take time
sometimes because normally I work with API is not like just refresh the page
okay let me just refresh this I mean you're the one who's okay so here's
so yeah so what would recommend just validate them maybe they they could
validate yeah yeah they should yeah yeah for sure for sure okay so can you guys
they have you have like a million let me just send you a million lists a million
leads yeah it's so many in here it's for a million businesses let me just send
you a million leads casually man for example you can go in local businesses
or people that's that's amazing man like I got nothing to say alright so thanks
that was like that's crazy man like so guys just go ahead and use this okay so
next question we have how do you stay organized yeah this is like the final
boss of everything I'll just leave the community and just leave okay guys so
let's just let's get back let's get serious okay how do you stay organized
already feel so unorganized the way just run in a few campaigns any tips yeah
man it's looks like it's time for you to start a project management tool so I
would now it's time to start using either a click up Monday or any
documentation tool it could like it could be even Google Sheets okay so the
cool part is I'm gonna drop the CRM soon so it's gonna help you a lot man
right you need to have like a place like a unpopular opinion like I would
encourage you guys have a project management even before you have clients
okay because I want because you're gonna get clients regardless right it's pretty
easy to get the clients right so I want you to be ready beforehand okay so
preparation preparation is key okay so I want you to have a place where you add
your leads so you have your pipeline where your leads come in and then
another folder where you basically organize all your clients okay because it
gets overwhelmed easy okay like you'd get one client and then you'd get you
know overwhelmed with your own campaign and also the clients campaign and now
like it's you can't really focus on the high ROI tasks okay and if you guys know
I'm all about minimizing friction right so if something automated it needs to be
automated something that's not worth your time just don't do it right so
here's my recommendation then I got one call from sending out emails to 1.3 K
leads I don't know if that's good metric but something feels just not right about
this I feel like this niche is not just for me are these feelings and tasks are
something to consider man okay so it's interesting man but let me just give you
like heads up okay so now you've launched your campaign okay
you launched your campaign you got one booked meeting okay so right now you're
here okay so this is the your growth you're here so there's there's a lot of
growth that comes in here and it's only gonna get better okay it's only gonna
get better since you started your campaign you got one booked meeting okay
now instead of just like trying a different niche like why would you do
that like why would you do that like you already got a booked meeting why don't
you just fix what's already working okay so instead of like trying to figure it
out from like from the beginning trying to do the entire niche exercise from
the beginning trying to draft a new copy etc why don't you just fix what's
already working right
so like it's only gonna get better now since you got a booked meeting there's a
lot of things to fix it could be the copy could be adding more leads okay it
could be the offer we could tweak the offer can tweak the subject lines and
think about it it takes you only like 20 minutes per week to just do some inbox
management right like when you set up a campaign you don't really have to do
much work okay it's the only thing you have to do is you don't have to like or
find a copy edit it maybe like try to tweak it a little bit more better like
a B like you just a B splits etc or add more leads okay so why would you like
why would you stop the campaign and like go to another niche okay if you have
already something that's already working okay again if you don't feel like
recruitment is for you obviously listen to your guts right so if you think that
recruitment is not something you would want to work with even though it's a
very really really good niche and I've made most of my money targeting
recruitment okay and it's pretty easy to deliver for them okay well obviously
depends on the market and their job postings but it's like it's really so
relatively easy because their offer is pretty straightforward okay you're just
gonna have to represent their candidates in front of ideal clients which are
companies actively hiring and since now we have X out we have indeed job
scrapers we have LinkedIn job scrapers we have a ton of tools just it's pretty
it's like it's relatively easy to you know deliver so again if you really
don't want it for sure you can just switch the niche okay and obviously
we're all here to help when when you start you when you like want to like go
for another niche obviously just make a post of like offer audit copy and I'll
personally come in and just make sure to tweak that okay so final notes keep like
what I would do like here's what I would do no fluff keep the basically keep the
campaign running okay and then you can start another campaign just because I
know like I know where the behavior comes from I understand you you want to
like try different things and I'm not just I'm not gonna say okay don't do it
you can still you know you can still do it okay you can still start a new
campaign but just keep this one already running okay so I hope this helps man
okay okay so how does it work to fulfill any client user in our make.com
and instantly account yeah that's a really good question man so since you
mentioned how does it fulfill to how does it work to fill the client using
our make.com I'm assuming you're just running an offer where you like we were
you host and set up everything on your end okay which what I would recommend
okay just because you keep 90% of the money okay so what I would do okay is
you would have your instantly account okay so you would have your instantly
account right and then you what you do is just send your clients a dashboard of
your instantly where they can look over the analytics okay and the reply rates
etc the copies okay so they can just see it okay and then all you have to do is
just white label it using their own company name in terms of the systems
in make.com since since now again you're running an offer where you are hosting
everything on your end right which is great okay keep doing this what I've you
know from like sending millions of emails and working with multiple clients
and multiple industries what I would what I what I saw is that most clients
they like having you know mentioning that you know they like when you say
you're gonna get it's also the system okay like hands-off it's all right sir
up but the at the end do like what they're interested in there are those
meetings okay so focus on the end deliverable which is the actual book
meetings which is why I recommend you guys have everything on your end because
here's what I've done and what would recommend you do okay like I'm always
gonna be transparent I'll tell you exactly what I've done so my first
client when they paid me I got one year of instantly and I hosted all my new
clients there okay I just bought the yearly plan okay and then I collected
the cash and now I don't have to pay for instantly for them right so I just host
everything on my end and typically they give you like 25 K okay and typically
when you have like we first going to start you have like two to three clients
it's already enough right obviously when you have more clients then you can
upgrade seats you can upgrade how many emails you can be send in how many
leads you can import just using instantly okay so I love having
everything on my end so I can have full control over it and the best part is
when you have your instantly account you just reply on behalf of them because
some clients might mess up their replies they don't like think about it they
don't know what we have here they don't know how to nurture clients most of them
like been running ads or they have like referrals or inbound leads so don't they
don't really know how to talk to someone cold so this is why I recommend you
handle everything and they just receive the book meeting okay so it's like your
job to do it so I hope this helps man
okay so the next question we have is hey saw just came back on holiday from your
country Rocko yes man half Moroccan half Italian I'm ready to get our each go in
ASAP got a few questions to iron out first would love your help what's a good
link to have for the email outreach I have a template but it feels like feels
long to me it's currently three paragraphs with a question line at the
end so
I wouldn't recommend using a template but I would recommend using a formula
okay so the formula is gonna be just like this okay it's gonna be pretty easy
I'm gonna just walk you through it so you'd have your personalized lines so
personalization okay you would have a clear offer plus clear value prop
and then
ideally we'd have a little case study here but it's not necessary you can
still mention past work so we'll just put case study okay
optional and then I'd have here soft CTA and now let's just actually like build
this copy life so personalization we're gonna just kind of have the personalized
line okay so hey X congrats on Y okay and then for the clear value prop you're
gonna use the clear value prop specifically for recruitment okay so you
talked about talking recruitment right yeah my niche is recruitment agencies
okay so what I would recommend obviously because what is the main core like the
main idea of recruitment like what do they do they place candidates okay so you
have to talk to you have to talk their language you'd say hey I'll represent
your candidates in front of companies hiring okay and then this is like
basically like a clear offer I represent your candidate in front of
companies hiring
and then we're gonna add a risk reversal which is gonna be
purely off performance now this purely off performance is pretty vague which I
would recommend you guys do it's does it doesn't imply they're just gonna pay
per lead okay so you can still mention the setup fee on the sales call okay
we're just saying purely off performance which is vague okay I wouldn't recommend
you guys again another unpopular like unpopular opinion never mention or you
get a refund in the copy or say or your money back okay don't say that because
you're triggering the loss of aversion because now they think there's this
psychological aspect when you mention something like refund okay and a sales
page or like in an email you directly imply that they might need a refund at
the end okay so you're like triggering the loss of aversion okay so don't even
mention that okay so I want you to put yourself in that state of mind that you
know in that paradigm and in a way of thinking okay just mention purely off
performance because again you're not a lead gen agency just like everyone okay
you are a growth partner that's gonna help them you know get more clients and
increase your bottom line and lower their costs okay so this is how you
differentiate yourself from other service providers okay so just mention
purely off performance which is pretty vague and that gets them interested to
hop on a call then you can just close them on the call okay so the idea is you
just give a little overview of what you do and then just got them on a call okay
and then you'd have like a little case study here if you don't have a case
study you just mentioned past work or you could say I'll present your candidates
in front of companies hiring purely our performance you could say for example
our cell systems
guarantee three to five placements on a monthly basis it still implies a case
study if you think about it okay we can say our system can generate okay it's
okay it's kind of cool way to just maneuver your way into the copy okay this
is just if you don't have a case study okay you can still make it work if you
have a case study then just mention it and at the end of the day you can still
mention past work it still works okay and then you would have a soft CTA and
the best one that I found it works is can I send over like a batch of leads
okay of your ideal companies are hiring for example or like what kind of
candidates that you have just send over a couple job titles that you have already
in-house I can just go ahead and send you like a lead list of 50 of these
companies actively hiring for the exact job role that you have and this is gonna
skyrocket your response rate and I believe someone like multiple people in
the community actually have done this okay and don't worry about market
saturation when it comes to recruitment because there's a like a million and one
industry inside of recruitment there's like you know companies that only place
AI roles only companies are like that have like manufacturing roles that have
like a bunch of you know marketing okay marketing rules that set up so don't
worry about it I'm still targeting recruitment right I see multiple people
running multiple campaigns at one at a time how many campaigns should we be
running any tips on running these campaigns or should I still follow the
bonus feed and the 12-day yes you should 100% follow everything in the
12-day agency right you should because it's gonna be extra information in your
brain and then once you start running the campaign you're gonna be like okay
Saad mentioned this exactly at this moment and it's gonna basically you're
gonna know how to act okay so make sure you always like watch everything okay you
have to what I recommend running multiple campaigns no but and the reason
why is I don't want you to spread yourself too thin I want you to focus on
one thing and one thing only but if you still want to run multiple campaigns you
can still do it but don't go over two campaigns okay this is what I would
recommend for example you'd have recruitment you'd have another campaign
you just scrape 5k for each campaign for each campaign right I appreciate the
help from from you the community definitely one of the best communities
out there with all the value yeah no worries man okay let's talk sourcing
prospects it seems like every prospecting service out there relies on
LinkedIn as a primary resource apollo scrapes LinkedIn from nearest ink
incrementally yes that's true XR also sources profiles from LinkedIn but with
an LLM twist actually that's not true XR retrieves data purely off Google they
just sort of extract data from Google they don't rely on anything they extract
data from from from Google and they feed it to AI and they restructure it for you
so they don't rely on anything which is the cool part about XR okay might be
worth leveraging XR specifically for company searches since their algorithm
could be less reliant on LinkedIn yes potential downside neither Apollo nor
XR maintain a full LinkedIn data set which means you're working with limited
depth when using these tools so wouldn't link sales navigator and phantom
bus will provide more relevant prospects in data well not necessarily because
Apollo what it does let me give you guys some context about how Apollo extracts
data from LinkedIn so they have a like an extension that retrieves data off of
polo and they send it to a waterfall enrichment right and they don't really
care about the email validation they just find the email right whether it was
deliverable on deliver on to just put it in there and they just give it to you
okay so the cool part about polo is they have multiple filters so they retrieve
data from LinkedIn and they restructure it according to you this is why you can
see that there are multiple filters and Apollo and there's not in LinkedIn okay
if you use LinkedIn sales nav you're not gonna find technologies you're not gonna
find job postings etc so it's kind of like LinkedIn is the source and other
platforms such as Apollo and other ones for example it could be like maybe like
rocket reach etc they pull the data and then restructure it for you and they
include the emails okay so LinkedIn sales navigate in front fancy buster is
still a great way to find leads okay but keep in mind when you scrape LinkedIn
sales nav the only thing you're gonna get is the headline title founder name
and you're gonna get their summary and basically probably you're gonna get also
the employees that's pretty much it when you scrape Apollo you can get more than
they can get the industry etc right and phantom buster is a little bit pricey
you can use a pofi instead okay just use LinkedIn sales navigator and use a
straightforward a pofi scraper which is gonna be advanced LinkedIn scraper okay
oh so how I hope this helps man okay how many potential companies for the clients
of your clients is required if your clients only have five potential
companies to sell to we're going to basically burn through the whole
audience in a month well it really depends on each clients man like I can't
really tell you like how many clients like well like well I can tell you I
can't really tell you like the exact number because I don't know the clients
okay so if your clients only have 5k potential companies to sell to we're
going to basically burn through the whole audience I don't think the own
like you come like a client only has 5k potential companies okay the only the
only time I had this issue is when I worked with a client that was a red
light therapy and they only had to sell to red light therapy clinics and a couple
cities right so if you're reaching out to be to be typically they have more
than 5k okay and even then you can still scrape more than that there's always
multiple multiple ways to scrape data and you've got to understand that
there's not only like 5k companies that end today like every single day there's
new companies that come in in the United States every single day there's 2,000
companies daily that are startups right and not even not even just in the US in
UK it's like even more than that so I wouldn't ever worry about leadless okay
and my recommendation is never like never think about this now until you get
the clients okay right because it's kind of like driving a car at night and trying
to look over your shoulder you're gonna crash okay get the clients first and
then worry about this later right
and again there's always ways to get data right there's always ways there's
crunch-based LinkedIn Apollo clutch like always ways to get to get data okay so I
wouldn't worry too much about it and obviously if you got any issues building
a list just make a post about it and we're all gonna help and 100% gonna get
you the list and Sam is gonna give you like a million lists yeah man so okay so
we have this question here the last question and then we're gonna go back to
the questions in the chats when it comes to EXA I know that there's a difference
between the EXA and the surge is there's a way to be able to pull the same data
that we get from the website through access search I really like the idea of
being able to pull competitive information yes and data and do the
hyper personalization that EXA offers maybe there's a potential way to
searches the main thing is looking for cost-effective ways for hyper
personalized oh yeah yeah you're talking about yeah websites hyper personalize
email such much further than just personalized line and scraping site the
EXA YouTube video really inspired me that's exactly what it's been trying to
achieve at CP CPL okay so instead of using like web sets we can just use
these perform the search and just basically find the data that's what you
mean right
yeah it works man it does work
let me just show you
I think
if you just put and I'm gonna put an entire course on on X ads it's gonna be
just here in community never posted on YouTube so I think if you can put just
my name let's just try my name for example and I think it's personal sites
and let's just run and let's see
okay I think it's gonna be I think it's gonna give us something oh wow they gave
me they gave me my LinkedIn okay I think you can go even further than that
use auto prompts hmm result type yeah you can definitely do that let me just go
to company and then let's just try just put
EXA for example EXA competitor or main competitor name and click on save let's
just run this and by the way you guys I'm still gonna answer all you guys
questions here oh wow we found it there you go
so what what they've done here is they basically performed a search there hmm
um there's definitely some juice to be extracted here
maybe keyword
and then let's just try again
so it's still doesn't give us basically the
okay so they gave us the highlights so the company primarily serves
multinational corporation and retail industry hmm
it's still they just give you basically you know articles et cetera so might
have to just just tell AXA to give us like access to this yeah we need to play
more yes yeah because you won't find any yeah you won't have you won't find any
video course about it because they just literally just launched a video course
about it because they just literally just launched AXA like literally like two
months ago I was from the waitlist seas like when I saw AXA and I was like okay
like I need to get this now so don't worry guys like the like the code is
gonna be for everyone community along with everything else so everyone is
gonna like scrape data so don't worry about it so now we finished all the
questions that we have here let me just go ahead and answer all of you guys
questions on the chat and don't worry I'm gonna even if we go past one hour
I'll make sure all you guys question are answered okay um
okay so let's just go a little bit
scroll down would you give your client access to that answering sling account
just send them the images of the analytics yeah you can send just send
them the images of the analytics will just send them a view only access to
that to the account okay and you can just white label that with their company
name okay this is what I would do
so do we have the instantly account in our and yeah in our name but instantly
like instantly has this whitelist label okay so you can just add their name okay
and you can even add their logo
Jason said this session has been great I'll have to watch it again tomorrow yeah
no worries man hey son I'm planning to use XSA for providing in for free leads
yes great idea as an indeed as on LinkedIn and indeed I'm not able to
differentiate with between post by recruitment and post yeah here's what you
do so go to XSA man you should go to XSA like you you couldn't add anything you
okay it's just about prompts so for example companies
hiring for a roles and then here's the kicker not recruitment so it's like the
lowest like the the shittiest type of prompt engineer and extra just boom not
recruitment agency or third-party recruitment like service and then
company currently has a job opening for a roles and just click on companies and
just go click on search so you can add as much as you want
they said I'm new here and you to XSA how reliable and accurate is XSA's
information man is extremely reliable and the reason why when you retrieve
founder names from XSA 99% of the time any any mill finder is going to find the
email just gets to show you that the data is reliable because if it's not
reliable they're going to try to ping the website and retrieve the email from
the website and they're going to be like oh no found but if it's found that means
the data is accurate okay so it's like that this just cuts through the noise
okay and the cool part is they give you a reason in okay so for example they
they give you a reason in here so here's why this company is at Sarah at Sarah
hey son my campaign has only sent 15 emails today so far so for some reason I
connected instantly and said it's duty ASP matching which delays sending okay
yes that's true but never affected sending until now should I disable ASP
I have nine email inboxes and sex private email hmm okay so so how much
emails you send in like in the total I use an NA 10 I'm finding I'm blowing
through 40k operation on make um I'm not I'm not gonna lie to you man I I don't
really I know how to use NA 10 but I just don't like their platform but if you guys
if you guys really like NA 10 I can start like and just put up a course for
that specifically for cell systems because you guys know me I'm all about
like like things that make money rather than other things that really don't do
anything
okay so you mentioned high intensity send in method for 5000 leads in five
domains 15 emails yes exactly they want 20 25 emails yes could you break down
how many emails are sent per inbox each day for instance on day two with 85
emails over 15 inbox send approximately 57 emails yeah so day one it's going to
be 20 225 emails right across all the emails and then next day 850 emails
right so instantly just takes care of this okay so ideally right so here's
what you should be doing you would have 850 emails per day okay and the warm up
the warm up will be basically 10 emails only okay so 10 emails only they're going
to be warm up along with you're going to have your just your normal send in
pattern okay which is going to be 50 to 60 emails which equals to 850 emails
okay so at the end like instantly just takes care of the distribution so I
wouldn't worry too much about it only just send it to 225 25 emails and the
next day just update that to 100 850 and instantly is basically going to take
care of everything okay it's going to distribute the follow-up etc etc so the
so the cool part about instantly is they include right so the numbers yes yeah
man of course you can send up to 50 to 60 emails per day okay just make sure you
have the best deliverability you set up your SPF decam D mark okay and then the
next day you're going to have to pause okay and this is how we get rid of
outreach lag because those emails will be sent the next day even if it's pause
you've sent 800 you've sent 850 emails you're gonna get replies from the day
before and the day even like the and the day even before that okay so this is
what I would recommend um he thought thanks all as always you mentioned
earlier that campaign management yeah should only take 20 minutes could you
could you show how you do it yeah man so here's what I would recommend so in
terms of campaign management when you have like a campaign that's already
running okay the only thing that you have to do is you look at over the
analytics you look at your the metrics okay okay I've sent 1000 emails okay
I've booked one meeting okay what I would do how can I make this copy even
better okay right so the only thing you have to do is just tweak subject lines
tweak the offer a little bit more okay maybe make sure make sure to even like
clear the offer even more right so this is what I mean by campaign management
you're just going into your campaign and try to optimize everything okay and over
the course of a few weeks you you're gonna land you're gonna you're just
gonna start like basically you're gonna find something that works okay like
there's no reason you won't find something that works okay it's gonna
take you like 20 minutes to like couple edits all right and then just keep keep
sending okay obviously don't make huge changes right because now it's hard to
track just basically a little bit like a little bit of you know campaign
refinements maybe you'd if you notice that there's a little bit of jargon in
your email just remove it yes so I'm curious about your copy maybe you can
share it in the community maybe you can help there's always better ways to
optimize a copy always like even when I write a copy like I go back and read it
like a couple hours after and I'm like okay I can remove this right um okay
Nicole said when offering leads as a lead magnet do you think it's best to
include company names along with each company's name verified email would
provide around 15 to enrich lead be a good enough to showcase value to we can
so we can eventually get them on a call yes 15 to 20 enriched emails still low
but now that you have that you guys gonna have the any mail finder in house
tool they can you can you can go to like maybe 70 to 80 emails right because 15
to 20 is still not a lot okay and also include the company names always include
company names okay the goal is to give them like a really really aesthetically
pleasing list okay so they see the value okay that's like the whatever this is
like the the main juice here okay it's like a it's where you should shine in a
way right okay so for this email campaign we are sending multiple emails
to clients X or is it one just want to understand if instantly and I'll make
scenario are sending out multiple emails to clients X and not just one so for
this email campaign we are sending multiple emails to clients or is it just
one I can tell by your the look on your face you're confused so maybe I could
maybe I can explain here so one of the things that I've kind of been curious
about and I still try to grasp this is for our email campaigns as I noticed
we're gonna send 225 day one then we'll send out 800 Mike my question is
instantly is gonna do its magic is it going to send an email let's just say to
like Joe Smith on that first day Joe Smith is gonna get hit with the email
from me and then it's gonna run its thing is it's just gonna send them one or
is it gonna this campaign gonna hit him like multiple times yeah good question
man so instantly is gonna take care of the follow-up so if the follow-up is in
two days it's gonna include the follow-up in deep exact number of emails daily
does that make sense yeah yeah so it's so it's it's automatic yes it's all
automatic that's in junction with like yes they make I have yeah they they
essentially what they treat the follow-up as in the still daily email
right uh-huh yeah okay gotcha and lastly yeah I also know that like with all
these messages sometimes I want to like see if people are responding to them do
you do any type of like I guess alias or forwarding to your to your accounts
because I typically use Google but when you jumped on this oh you should try
private email with Namecheap I have to go into that website to see who's
responding on that platform and I'm just like oh man I got to go to that site
it just sometimes confuses me so would it be do you suggest maybe like
forwarding that email to one that you have in Google or is that just yeah you
look forward to in the instantly account yes after the after the call I'm gonna
send you a screen chance on how to step by step on I actually do it okay cool
cool okay all right yeah you can all instantly in unibox yeah it's pretty easy
what were the most important things you added in your email copy that
skyrocketed your reply rates thinking about sending a link to a small loom
video explain the system on a whimsy call it's gonna hinder your deliverability
the best thing that can skyrocket a basically a copy is a good offer and a
lot in a low effort CTA a low effort you know so it's easy for them and the best
way is just send them a batch of leads or you ask them hey can I just send you
like a loom explain and more right because like leads want you know they
want you to give them something first right but obviously the offer is gonna
get this is gonna get you in front of the door like obviously the like the
offer is like 80% of the entire thing okay
when starting out what niches should I target there's a good question man
obviously it's gonna have to be a beauty niche what I would recommend go for so
if I were just starting I'd either go for some sort of a niche in the health
care space and I'll also go for a sass type of niche okay so if you go to
crunch base for example let me just show you
so go to advanced search for example I'm gonna just show you a couple niches you
can go for
there's tons okay so you can either go for AI niche okay which is artificial
intelligence you can go for health care so the one that I would recommend is
either be gonna be it's always gonna have to be B2B okay always make sure it's
B2B because B2B like you you're gonna get access to data and decision makers
okay so either go for some sort of health care niche right or a sass
software right or you can go for recruitment okay so these are the three
best ones that I would go for obviously there are multiple niches okay just
make sure it's B2B okay B2B why do you suggest those three sorry mm-hmm why
why the why would you suggest those they're easier what's what yeah man
because it was a great question because their offer is pretty clear right for
recruitment it's pretty easy like what they want is just they want to place
candidates for sass they want to acquire more customers for their platform and
for health care they're booming right now so the health care niches booming like
most of them now are trying to implement AI right and with the rising of AI
companies AI startups every single day you can get data if you get data then you
can send more if you can send more then you can close more yeah I have a question
about sass niche normally the CEO of sass okay how could you impress him with
such kind of system normally already now yes yes the cool part about sass is they
are tech savvy they understand automation etc right so you can you know the tone
is gonna be a little bit more founder to founder and they understand
automation so it's they're not gonna be like they're not gonna be like some sort
of B2C clients or like a brick and mortar or like typical local businesses
so they understand automation etc so they will understand the juice of sales
systems even better but the problem is they will understand it so much they can
reproduce it in two minutes well this is where you have to just give little
overviews okay because any sass owner if he see like the scenario in making will
understand yeah they will understand yes of course yeah but still they they are
hiring you for a reason obviously they there are other the other things that
are you know domain specific specifically for our sales them agency
right okay so we are past one hour okay so these are the last questions I'm
launching a campaign targeting marketing agencies I want to kind of get your
brain dump on different ways of positioning and frame it in the copy to
stand out from the yes from the rest of the markets for example one of the lead
magnets that I considered is of course offering an enriched lease of their ICP
yeah yeah man in terms of marketing agencies so what they do is they provide
awareness okay so always make sure to structure the offer in a way that's
revenue focused okay now let that sink in
okay so when you talked about a B testing during the campaign since we use
dream email template it use a app to fill in the variables and upload the
upload the complete emails dream email variable and instantly thus we cannot
change modify the copy instantly how do how can we conduct how we can yeah oh
yeah that's a good question a B testing to improve our email copy for better
client results like we change the copy on make.com which we keep the campaign
unchanged until it finishes well ideally I would want you to keep the campaign
unchanged but here's what I would recommend you just copy the the initial
dream email right and it's basically you're gonna add another variable for
example which is gonna be so here's here's what I would recommend okay so
you would have dream email it's sent to instantly instantly you can add multiple
variables okay so you can add like job title company name first name etc so we'd
add another variable it's gonna be personalized line and just output right
the results of Claude in that variable okay now in the A B testing just map
personalized line and add the B which is gonna be the next copy okay does that
make sense
yeah no worries man okay yes okay grace perfect let me know if I should disable
ESP matching or not meant to send 850 emails only since yeah yeah man just
disable it and let me know how it goes and if it sends 850 emails then just
keep it disabled yeah
perfect well what would would you list segmentation look like if you were to
target healthcare right now as a demonstration yes here's what I would do
now it's gonna be the last question I'm gonna go to crunch base which is I'm in
crunch base so I'm just gonna go to so here's are the industries that I would
go for guys type in healthcare and I want you to work with mHealth healthcare
healthcare mental health personal health health insurance not health
insurance healthcare home health care health diagnostic EHR telehealth and
that's pretty much it and you are three hundred thousand results okay overview
obviously headquarters we're just gonna we're gonna choose United States and
actually like posting another video tomorrow which is exactly like what
you're talking about which is synchronicity again it go to company
status I want you to work with for profits and the cool part is you can use
in a crunch base basically just give you guys some context it's just crunch base
obvious you can scrape other platforms LinkedIn or Apollo okay so I want you to
so I want you to go to financials and last funding stage now we can basically
retrieve companies that were recently funded ie they are more expect expected
to grow which means they have the budgets and they're looking to scale and
grow more so go to series a B and C and then you have three thousand results
okay just pretty good you can go over to series D and I have you even more go to
seed and have a seven thousand results so these are all companies that you want
to work with okay and this by the way guys just give you guys something
Hippocratic AI was a past client of mine yeah like it's funny how I see it now
here but yeah so the cool parts about crunch base is you also have the
industries website and also the founders so all you have to do copy this
URL go to Apollo not Apollo sorry go to Epiphy go to Epiphy and look for crunch
base
and go to this scraper right here you can scrape up to 4k with the scraper
click on try for free
and just paste in your search URL here leave this empty because you're going to
scrape all the records add your session cookie using the edit this cookie
extension and then let me just show you go to edit this cookie and just extract
your cookie okay go back and just paste that in here always custom proxy
residential run options is going to be no time out and just go to town with it
so I hope this helps man all right guys so I believe
okay last question I promise did you find the Apollo or crunch base data to be
more accurate um they're both great databases I would combine both I would
scrape 3k from Apollo and on another 3k from crunch base and then I would have
yeah no worries man yeah and then I would have basically like the final lead
list right because both like all the platforms are great it's not either or
right we use everything so we can build our lists okay just make sure you track
them okay guys so hopefully you guys had a lot of value in this weekly session
you had fun just like I did and yeah I pretty pretty sure
pretty sure these are all the questions I'm making sure everyone's questions are
always answered so if you guys have any questions just let me know
perfect all right guys so thanks Jesse thanks Zach thanks Dai or like I'm sorry
like if I butchered your name Axel obviously Jay
Sam Yahoo Raj and what else Aaron
Nicole and Colin all of you guys thank you thank you for your time and coming
today and I'll see you guys in the next weekly call obviously if you guys have
any questions make a post or just send me a DM and I'll be able to help you okay
thanks that all right guys I'll see you guys in the next weekly call okay
Cheers
