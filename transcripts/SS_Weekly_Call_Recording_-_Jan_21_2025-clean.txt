I'll fix this and get everyone here.
There you go.
So let's just wait for people to come in.
I'm going to tackle your first questions, <PERSON>, just
because I think they are highly valuable.
And then we're going to walk through the sales call
and basically how to close.
What's up?
What's up, <PERSON>?
Hope you're doing well, man.
How's the campaign going?
All right, pretty good.
Of course, man, just based on the interactions
that we have together.
Oh, <PERSON> just got his first client today.
I'm pretty happy for you, man.
Just make a post about it, man.
Make a post about it.
That's amazing, man.
It always warms my heart to see people actually
getting their first clients.
And it just gets to show that when you put effort,
because I see you putting effort, basically,
into your campaigns.
And I see all of our limited interactions, right?
Pretty smart guy.
And I mean, I'm not even surprised.
So yeah.
Great stuff, man.
Yeah.
What's up, <PERSON>, man?
Hope you're doing well.
I always see you on these calls.
Of course, yeah, recruitment.
Yeah, man, recruitment is absolutely crazy.
It's crazy how many people are actually
targeting recruitment.
And everyone is getting results just
because it's an untapped niche.
And I also am targeting recruitment currently.
Not me.
I'm not going to get them, get myself clients.
But I have a recruitment client that is essentially
looking for these candidates, right?
So feel free to ping me.
I can help you with that.
I just launched a campaign Monday.
So it was yesterday.
Yeah, it was yesterday of the recruitment.
What's happening, <PERSON>?
Welcome, <PERSON>.
Welcome, <PERSON>.
So welcome, everyone.
So now, hello, <PERSON>abien.
Hello, <PERSON>.
What's up, man?
So first of all, we're going to go ahead and tackle
the question of <PERSON>.
And let's go ahead and get started with it.
So let me go in and share my screen with you guys.
And let's just get started with this.
Let's just go ahead and get started with this.
Go crush this.
You got three booked calls with?
OK, I'm going to stop looking at the chat just
because there's so many wins.
OK, I'm going to read through this later.
OK, so first, we have a lovely person, which is Jason.
Hey, Scott.
What's up, brother?
Question for you and a few things I have learned.
So can you quickly go through what you would
say on the discovery call?
I found myself stumbling over my words
and not quite sure how I can visit them.
Yeah, man, that's part of the game.
That's normal.
So I know that this is about reps, and I'll get better.
But I'd like to get an idea of what others are saying
and the process the call goes through.
So yeah, man, so I've been taking some notes here.
And I'm going to read through this.
I'm going to explain to you, essentially,
how I go through my sales calls and how, basically, most people
that I know that are operating an agency or a lead gen agency
go over their calls.
So what I want to explain to you is that, obviously, you're
going to have an idea on what to say on a call,
because I don't want your mind to go completely blank,
because this is a problem that I see most people actually
have, is that they go on a sales call, and they get scared.
And they would have a script, but sometimes our mind
just goes completely blank.
And it's totally understandable.
I've blew many sales calls.
You're not just alone in this.
I've blown so many sales calls.
I've had days where I didn't close ships,
even though I had so many book meetings.
So that's completely normal.
Again, our friend here, he booked 10 book meetings,
and he only closed two.
And two is actually very healthy, guys.
You'd be surprised.
Two deals out of 10 is crazy.
It's pretty good, right?
So another thing is, at the same time,
I don't want you to act like a robot.
So why I don't like the scripts?
Some people just go ahead and copy the script,
and it really doesn't work, because I
want you to be authentic, right?
So your personality and the connection
you build with clients is one of the biggest assets
that you have as a service provider.
So you want to be easy and enjoyable to work with.
So also, you need to be human and not a robot.
But what I would recommend you do
is you always want to get on a call.
You want to be in control and lead in the conversation.
But it is also important that you ask the right questions
and listen, right?
It always has to feel like a consultative approach,
not a closer, right?
So don't feel like you need to do all the talking or pitching
or selling.
So your service is best solved by showing
how it's going to help them achieve
their goal, which is connecting them with their ideal clients
in the book meetings.
And it's so important that you really understand
what these goals are first, because once you
take the consultative approach, now you no longer
have this crazy feeling that you need to perform, right?
I don't want you to go on a sales call
and be like, oh, I need to perform this.
No, you have to come in, chill, and you always
have to come in as you already closed it, right?
So what I've noticed is that most of these sales aspects,
even the business aspects, are just mindset, right?
And mindset is extremely important.
I want you to come in in the sales call, too,
and understand that whatever happens, happens, right?
Don't put so much pressure on yourself
and always come in from the perspective
that you already closed it, right?
So live in that now.
Don't live in the future, right?
Live in the now, right?
So take a consultative approach, live in the now,
and ask a lot of questions.
What I would recommend is a one close call, right?
So the purpose of the call is to help them
make the best decision of their business.
So I want you to understand where, because I'm going to go,
I'm going to explain to you, first of all, the paradigm
and the belief system that you need to have,
and then it's going to all make sense to you, right?
I want you to go, right, and come in
in the purpose of the call, you're going to help them
as a consultant, right?
So the mindset you want to be in is you are in their side,
working with them to put together a solution
that's going to help them reach their goals.
So this is the mindset that I want you to go in.
I don't want you to come in as a,
just a closer that just comes in
and wants to close the deal, right?
Just because it's going to stress you out
and it's not going to help you at all.
The script that I'm going to give you,
well, it's not really a script, just a frame of work, right?
The first questions here are prompts
about your client's business, their goals,
or stopping them from achieving these goals, right?
It's a really good idea to get comfortable
also talking to the client about money,
just because I noticed a lot of people
get scared to talk about money, which is absolutely okay.
You talk about money upfront, guys,
is going to show your clients
that you're all about making them money
and you want them to see good return on investment
and good offer why.
And what I mean by that is take a consultative approach
by asking open-ended questions
to better understand their business, goals and challenges.
For example, here are the questions, guys.
So in terms of us, we're not a automation agency, okay?
We are a sales team agency.
And yes, we automate our entire lead generation,
but the kind of questions that we have
are very, very specific, right?
So our question is that you should be asking, right?
And here are the questions that I always ask
whenever I get on a discovery call,
such as what does your current lead generation process
looks like?
You'd quickly understand that most of these companies
still rely on outdated methods.
Sometimes they spend thousands and thousands
of dollars on ads.
So you would essentially ask them
what is their current lead generation?
And I can tell you from now, nine, 10 out of the times,
they're going to be saying,
it's either going to be Facebook ads,
or some sort of referral scheme, 100%, right?
Another thing, so this question is going to bring you
to the next question, which is,
what kind of budget do you currently allocate
in this client acquisition method, right?
So typically, if you're working with enterprise,
it's going to be 15 to $20,000 to acquire one damn client.
And if it's paperclip ads, it's probably going to be,
if you're working with a company,
like a small size company,
it's going to be like maybe $100 per day on ad spend.
You can essentially pitch that.
So I want you to understand that
when you ask them this first question,
what does your current lead generation process looks like?
You essentially try to reveal
where you can attack in a way, right?
So because we know that in order for them
to get one client, they're going to have to spend
a shit ton of money.
Now, those answers that they're going to give you,
now you can leverage them, right?
Leverage them and then pitch our service,
which is connecting them with their ideal clients
with basically an internal cost of $500 maximum.
The next question that I usually ask,
which is, have you ever worked
with a salesman agency before?
And this is where you have established authority, right?
So most companies, they are bombarded with lead gen agencies
that automate the hell out of those sequences.
And typically, most lead gen agencies, guys,
I've consulted with so many of them now,
a lot of lead gen agencies, you would know they would come
and they would want a system built using mega.com, right?
Because they essentially still use very expensive software
such as clay, right?
If you guys know clay, it's a enrichment flow, right?
And it's basically like a air table
or a clickable board on steroids.
So what they do is just they query an API
of a service provider such as an email finder
or any like email enrichment or workflows
such as built with.
Built with is basically like a technology retriever, right?
They like a system that finds companies
that are working with certain technologies
such as intercom or whatever, right?
And it's just an API.
You can essentially do the same using air table
or like click up, right?
So these companies, they spend so much time, right?
To enrich those workflows
and they typically have templated shitty emails, right?
They don't know how to use AI
and companies are bombarded with these lead gen agencies.
So when you come in and you ask them,
hey, have you ever worked with a sales agency before?
Some of them are gonna say, yeah, we've been burned.
And this is where you come in and you put your service
as how we leverage AI and real use of automation
to automate our entire lead generation
without worrying about a single thing.
So the next thing I want you to ask is,
I want you to ask about their ideal customer profile.
This is extremely important.
Always ask about their ideal customer profile
before the onboarding, right?
Just because it puts you in a spa,
kind of like a doctor.
So a doctor, when you come to a doctor,
before they give you a prescription,
they're gonna have to basically say, hey, what's the data?
The data here is like, what are your health metrics?
What is the KPI?
Like if you come to a doctor
and they just give you a prescription, right?
You're gonna be like, what the hell?
But if they basically take your health matrix
and your blood work and all of that,
you're gonna be like, okay,
this person knows what they're doing.
So you wanna ask these questions
about their ideal customer profile.
If they have a clarified offer,
like what is the current offer
that they are currently basically going for?
If they're running Google ads or Facebook ads,
you're gonna know pretty quickly
that they have shitty offers.
Another thing is, I always say something like the following.
Don't you think it would be logical
to just focus on closing deals instead of trying to find them
and let a growth partner like us just do the work for you?
How much revenue do you think you're leaving on the table
with your lead gen methods?
So all of these questions are pain discovery questions.
So essentially, you wanna put them
in as much pain as possible.
And I know this sounds Machiavellian,
but this is what sales are, guys.
So another thing I always ask them,
how would having ex-qualified leads per month
impact your sales target?
And this is where they get basically happy.
Because now, since you've discovered their pain
and how much they're spending acquiring one client,
now we've given them the solution.
So how about five qualified meetings
would impact your sales target, right?
And then if they did, how much time does your team
currently spend on lead generation, right?
So these are the questions that I always ask.
And then once you ask these questions,
it's gonna take you like 10 minutes to understand this,
right?
I want you to listen.
And the usual sales script that I usually go for
is I wanna give you a recommendation, right?
So before you go into a sales call,
I would always recommend you have a price endpoint.
And the reason why is you wanna anchor your price.
So let's say, for example, you wanna sell a system build
or like a cold email system or a sales system
for let's say $3,000.
I would always pitch something like $4,000
because this is the highest I would go for.
And then I would anchor it down.
So let's say they say, okay, it's a bit too pricey.
Then now I have the lowest price I can go for,
which would be like 2,800, right?
And it's still within my range of cash collected
that would make me comfortable.
And it would also allow me to deliver the results.
So it's kind of like a bargaining method, right?
It's from a book that I read before
and it really works great.
It's called Never Split the Difference,
which means you're just anchoring the price
to a high and the low.
And you would go for the highest points.
So if the prospect agrees to the high price,
now you're crushing it.
And it's not that you're still within that range
that makes you comfortable, right?
So obviously when you get onto a call,
always be high energy and enthusiastic
if you're like super tired, you're not gonna close them.
So meaning small talk, jokes,
introductions to gratitude and thanks them.
I always say something like,
hey, thanks so much for coming into today's call.
I know managing whatever the hell they were managing
is a hassle.
If you are talking to a founder,
you would say something like,
hey, thanks for coming in today.
I know managing a business as a founder,
speaking as a founder myself,
it could be quite the hassle.
So I appreciate carving out some time for this sales call.
And then obviously guys,
you have to understand that it's a sales call
and the understand that you're gonna pitch a service.
So the understanding that there's gonna be
some sort of pitching, right?
Like people are not stupid, especially founders.
They're very, very careful about their time.
So don't try to use any gimmicky sales tactics,
just be authentic and make sure
it is going to be a sales call, right?
Like there is gonna be money transaction, right?
It's how, this is the nature of the game, right?
So you wanna ask them questions
and they understand their business,
like I said, using these questions.
And then you're gonna give a little good presentation
of what you do and how you could help them,
but not your best one, okay?
And then you're gonna try to close it right there.
So the reason why I'm doing this is let's say,
for example, the discovery call is 45 minutes.
You try to close it in minutes 15 or 18 early on,
just because it acts like a pattern disruption.
Most people that get on a sales call, right?
They expect like in the last two minutes,
you're gonna pitch the price, right?
And then it's time for them to like,
to say something like, hey, well, I have another meeting,
the weather, whatever, right?
So what you need to do is basically
give a little presentation and pitch the price right away.
And then the second you ask for it is the actual,
like is the second it actually begins.
Now you're gonna understand that they will try
to ask you the right question, something like,
hey, how can we know that this is 100% full guarantee?
Now it's time to handle the objections such as,
hey, we work on a paper lead basis
or the upfront fee is going to be 100% refundable
if we fail to deliver the five book meetings
or whatever offer you are running.
And then you would basically pull back the curtain
of what they truly are,
just because it's comfortable when you are understanding
their problems and they're talking or yapping.
And now if you blow your entire load in the beginning,
that's the wrong way to do it.
The natural way people are used to it
is that you're listening and it's all nice, like I said.
And the last 10 minutes they say,
oh, I have to go another meeting, the weather.
They know that the close will come two minutes
before the sales call.
So banging it in the first 15 minutes,
kind of like a shock, right?
And now we have 40 minutes left to really get into it.
So I have something here that I named,
when someone is hot, you strike.
So ideally you wanna close them on a call,
which is why I always make sure
I have a pricing in point in my head.
And I always try to close them in a call
and I send them a contract on the call
and I get them basically get up and running
as soon as possible.
And when you close them,
ideally you would have an onboarding call
the same day, right?
And just because people, when they are excited, right?
You wanna front load as much value as possible
and you get them on an onboarding call
and then you basically start getting them
the points as soon as possible, right?
And you would define your availability,
which is what I do is Monday through Friday.
And I have an availability from, let's say 12 a.m.
to 12 p.m., let's say 5 p.m.
And I would give you progress updates
that's just three times a week.
And I'm also, something that I would encourage you guys do
is you would frame your service
as they would get extra shift.
And extra shift by meaning is,
I would say you'll get a click-up board, right?
I wanna use this click-up,
or you can use any project management tool
that you wanna use.
And I say you'll get a click-up board
that auto-populates with new interested booked leads, right?
And then you're gonna also get weekly analytics
over your Instantly campaign, so you're always in the loop.
And you're also gonna get a Slack or phone integration
so you're always ahead of the curve
of new interested leads.
And obviously I'm gonna send you progress updates
about any lead or interested lead
or a lead that wants more information
and that we don't know the information of.
So clients love this,
but just because they wanna feel like they're involved, right?
And then it also helps reduce that buyer's remorse
just because when someone pays you,
they wanna feel like they got value in the first place, right?
So yeah, this is pretty much it.
This is how I always go into the sales goal.
So let me know if you guys have any questions in the chat
and if you guys are following along.
Okay, awesome.
So we have another question here.
Axel, how would you close?
What do you say when trying to close early on the call?
Yeah, so basically when they ask you a question, right?
You typically will have the answers, right?
Because when they say, well, we are only using referrals.
Now it's time for you to ask them a question, right?
So let's say you have a question.
Now it's time for you when you're hearing
all of these answers of these leads.
Now we understand, right?
That when you try to give your presentation,
let's say you say something like,
hey, we've built a system that will automatically scratch
this amount of internal costs in your system
and it's gonna help you basically connect
with pre-qualified decision makers
that are willing to hop on a call with you
and basically have the pain and the problem,
the pain and the budget of whatever service
you are selling, then you would basically
just give like a little bit presentation
and you would say something like, hey,
and the pricing usually goes from, right?
And then is how you transition.
You would say something like, hey,
and the pricing usually goes from
and then you'd go higher, right?
You anchor your price and then you say something
along the lines, for example,
the pricing usually goes from $4,000
all the way to $5,000, right?
And it's typically fully refundable
and you say it's fully refundable
and we also work on a paper lead basis
and you don't say anything after that, right?
You keep silence, right?
You say your price, I always recommend
when you always pick your price, don't talk, right?
Let the awkwardness be there, okay?
I'm just being completely real with you
just because I've been on so many sales calls
and people typically try to fill in the void,
which is not what I would recommend, right?
When you pitch your price,
then you're gonna see the objections.
And when you pitch your price and you let that silence in,
this is where you see the prospect actually reveal themselves
and then they will be asking you the right questions
such as how do we know this is a full money backer guarantee?
How do you work with a paper lead model?
Do you have any case studies or something?
You can essentially just now basically get
into the root of it.
Okay, so now let's go ahead and answer the next question.
So use and spend tax in your sequences.
I saw this is a cold email Reddit group,
use random regards, cheers best.
You could essentially have a few of these in a single email,
greetings, case studies, CTAs,
and it will be a string of different random parts together
and you'll have three variations,
making sure they make sense when put together randomly.
Would this work?
Do you think three different ones are just easier to track?
So yeah, you can use a spend tax.
That could definitely work.
I use it in my greetings such as regards, cheers and best,
but I wouldn't go over three
just because it's gonna be harder to track, right?
The goal here is to basically just put in one to three
so we understand which one works.
So when you add more,
it's very hard to track what works, right?
Because you'd have a couple of variations
that had the same results, right?
And the other ones that have shitty results.
Now we don't know what variation actually worked.
So don't go over three.
That would be my general recommendation, right?
Okay, so I noticed this instantly
and wasn't sure if others had seen it.
So when you push reply, it says typo and as a macro, yes.
Then you basically can create a macro,
also known as a fast reply.
This will help you get replies out much faster.
Have a few here that cover bases
or just copy and add them with the variables.
Yeah, man, I know this from instantly.
I used to use it too.
So yeah, be sure to make a post about it
just because I don't think if many people in the community
know about this macro thing instantly.
It's pretty awesome.
You can just essentially click on macro
and you can push and create a macro.
And basically just a keyword
where you can reply faster to your leads.
So yeah, so these are your questions, Jason.
I'm gonna go up.
I wanted to tackle these first questions first,
but it's just because I think this is the most value
that everyone is gonna get in this call
just because we don't have many questions in this thread.
So Asher, I have a question
about speed to lead issue I'm having.
So we've built this system before.
I hope you had value in that system,
which basically handles the replies
and automatically replies to leads who are interested
and leads who need more information.
Just because the highest juice
and the highest leverage that you have
are interested leads and interested that need more info.
These are the ones that you don't wanna mess out.
Other leads such as like the pricing requests,
you can go ahead and reply to them later on
just because when you have more categories when AI,
when you use an AI to send replies,
sometimes it can mess up, right?
So this is why we're only using two categories
just because we wanna not leave the juice
and the real juice as people are interested
and people will need more info.
Okay, so, hey Saad, following everything you do,
have multiple and slick things running,
getting decent open rates, but 0% reply rates
except for a few that tell me to buzz off.
I'm using the same methods and email sequences.
Here are the two screenshots.
Granted, among the campaigns,
maybe 500 to 1,000 emails have gone,
but one campaign has 82% open rates and no response.
So I would disable the open rates
just because it hurts the deliverability.
So let me read through the copy.
So good afternoon, Daniel.
Hello, Tuesday.
So I can see, first of all, I think this is a typo.
So I would remove this.
I'd make sure there is no mistakes right here
just because there's good afternoon,
there's hello, Tuesday.
So maybe there is a typo here
or like a variable that you clicked.
And then another, hi Daniel.
So hi Daniel, congrats on securing that 1 million seed
funder, awesome answer for deal potential.
This is a great personalization.
I have an idea to target with private equity
and VC first, right?
When they're researching investment opportunities
and conducting due diligence.
So I wouldn't use this copy right here, right?
I would only use this.
I know what you're trying to do here.
You're trying to use the AI personalization,
but I wouldn't use this right now.
I would only use it when they are interested.
So my usual flow is I would ask them for,
so okay, so the first variance would be asking
them for more information, right?
And then the second one would be an aggressive CTA,
which is getting them on a call.
And then once they say, okay, I'm up for a call,
now I send this, I'm gonna be saying something like,
hey, I have an idea to target with the private equity,
VC firms when they're researching investment opportunities
and conducting due diligence.
And let's say something like, hey,
it's even crazier when you can do this automatically.
Would you be willing to hop on a call this week?
When is a good time for you?
Is it Monday or Friday?
So I wouldn't use this as the initial email, right?
It could work in certain industries,
but it seems like it's not.
So I would not use this call.
So feel free to post this in the community.
I'm gonna roast it and maybe probably most likely
record a loan for you.
And then we can dissect this together.
So yeah, I can see 82% open which is crazy
and 0% reply rates,
but I don't know if you're still getting replies
just because the sequence is just 156.
So pretty early to denote the health of the system,
but we're gonna see about this.
Okay, guys, so now what we're gonna do is basically
we're gonna go through the questions in the chat
since the last 30 minutes is always for the chat.
So let's go ahead and see this.
So what we have, Eduardo, what's up,
others came in to say, thanks for the talk the other day.
I just got three booked calls,
another one started in 25 minutes.
Just checking in, three today, let's go.
Insane.
Hey, Jesse, thanks for responding to my last email.
I don't think you understand anything about my business.
When you have free moments, please read my books.
My book solved curing your medical insurance problems
and then let's talk.
How would you reply to this guy?
I'm not reading that book, no way.
Well, I'm not reading that too.
So he said, when you have a free moment, please read.
Yeah, I think this guy is just trying to pitch his book.
Anything about my business?
So I would keep following up with that guy.
After the call, I'm just gonna send you a reply
based on the initial email that you sent
so I can just have like a little more context about it.
Okay, when you drop the price,
how do you justify the sudden drop in price
while still delivering?
So yeah, this is what you say.
So a common way to tackle this
is to say something along the line.
Well, I would usually lower the price for every client,
new client that are onboard, right?
So this is how you would usually tackle this.
You would say, hey, since this is the first time
of us working together,
I want to provide as much value for you upfront
and I'm gonna lower the price for you
just because I do this for every newly onboarded client.
And they usually love this.
Is Never Split the Difference worth the read?
Yes, I love the Never Split the Difference.
I've read it a couple of times.
And it's usually a, it's typically a story, right?
Of a world-class negotiator that negotiates wars, right?
And it's pretty good just because it's a real life
negotiation of love and death situations
and it's gonna help you a lot about sales.
Hey, I just picked my niche.
Hey, Edward, just picked my niche.
Wonder if you have any experience and insights
with tech SaaS that are looking to hire ZR?
Yeah, that's a really good niche, man.
Typically tech SaaS, they always look to hire ZR.
And you're gonna have a lot of job postings for SaaS,
especially startups.
So if you go to Apollo, like I'm just,
I'm just gonna upload a video today
about me targeting companies that were recently funded
and it's IT companies, right?
So when you go to Apollo, there's a new filter.
So let me actually go through Apollo
and I'm gonna walk you through it.
So Apollo actually just,
it's a really good niche, man.
So let me go to Apollo and I'm gonna walk you through it.
So Apollo actually just added this new feature.
So let me remove this from here
and I'm gonna explain to you.
Typically they had like really shitty filters
but in the new update, it's actually gone a lot better.
So if you go to people, there's this right here
which is basically job postings, right?
Companies such as, you could look for job postings
such as sales representative, account executive,
account manager, sales rep.
You can even look for keywords such as sales
and the cool part of Apollo is they look for keywords
that are mentioned by the company, right?
All of their keywords such as sales, sales rep
and you would get like a lot of leads, right?
Just from doing that.
You could also look for companies that were recently funded,
so when a company is recently funded,
meaning they are investing in growth,
that means they are actively spending a lot of money
to acquire customers.
You can come in and it's both relevant
and also timely for them, right?
You can come in as a salesman agency
and you would say something like,
hey, please check out my company.
So there's a copy that I just wrote, which is this one.
Let me walk you guys through this.
And I'm just gonna post this in the community.
I believe it's already in the classroom.
It's uploaded in the classroom.
So it's called, let me go to share it with me.
I think it's in reasons.
So this is the one.
So usually you'd say something along the line,
hey, congrats on the recent raise.
Happy to see that company name looks a bit different
from all the generic gimmicky company category.
So company category is a variable.
You would add something, in your case,
you would have something like, congrats on the recent raise.
Happy to see that company name looks a bit different
from all the generic SaaS companies out there.
And then you would say something like, hey,
notice you're hiring.
Poor.
And you would have a variable called job title.
And then I would remove this.
And I would say, since in your case,
they're already hiring in the, I would say something like,
job title looks like outbound.
It's something you're exploring for early
and high ROI traction.
Just because when a SaaS company is hiring an SDR,
they want high ROI traction and traction to their service.
And here's my pitch.
As you guys can see, the tone that I'm using
is extremely natural.
If so, you should check out my process, my company.
We built a system that lets one smart growth person
send personalized outbound at the scale of 50 SDRs.
Recently, we managed to add,
we would add some revenue here, prevention,
in just 30 days with zero ad spend.
I'd love to show you how it works.
Would it be worth 15 minutes of your time this week?
So as you guys can see, I'm kind of like merging the way
I say, would you be interested in more information?
So I'm saying, hey, I'd love to show you how it works.
Would it be worth 15 minutes of your time this week?
So I think this is what I mean by a good copy.
I don't think anyone in any community
actually speaks this way.
Just because this is, like, you don't need personalization.
You would never need like another variable here.
Just because it is already extremely personalized.
Just because they had a recent raise, one.
And two, they're hiring for a job title.
So you don't need to add anything.
And that's what I mean by constrained variables.
So what I would do is I would just add in a variable
which is job title using some form of Indeed Fast Scraper
or some LinkedIn Job Scraper, right?
And then, or what you can do is you can just use Apollo,
which is extremely cheap.
You don't need to use even like some Indeed Job Scraper.
So you would look for companies here.
You would go in this filter.
You would look for job posting such as an SDR or BDR, right?
And once you do that, you would add that
as a basically a variable.
And then when you scrape Apollo guides,
you would have something like a industry.
And it's an industry right here.
Typically, it's retrieved from right here,
which is going to be computer games, internet
or information or IT, right?
If you don't have the industry,
you can just go ahead and a little bit broad
and say SaaS or IT.
And then you can just go ahead and pitch your service, right?
And then this is soft CTA along with,
well, I wouldn't say it's a soft CTA,
it's a lukewarm one because I wanna explain to them
how it works and it would be worth 15 minutes
of your time this week.
So feel free to copy this.
I'm gonna add this to the thread of the weekly call.
So there you go, guys.
Okay, so could you talk in greater detail
about what points you turn over the lead to the clients?
I'm going through a campaign right now.
Is it best to take over 100% and use their calendar link
and book the meeting?
Is there any point you can give them access
to the Inslee campaign to book the lead themselves?
So I've used both approaches.
Well, it really depends on how you wanna deal with it.
If you really don't wanna deal with the booked,
you can just go ahead and do it.
If you don't wanna deal with the booked calendar,
you can just forward the campaign to them.
It really depends.
So if I need some context in the initial call,
what did you promise?
Did you promise the booked meetings?
Did you promise the system itself, right?
So just give me more context
and I'm gonna give you this straight up
no bullshit advice.
Hey Sam, she said to just try to the free plan of Apollo,
you just get the free credit.
Sadly, the current fee amount is 100.
Do you have any tips to get it free?
Yes, what you need to do, man,
is you need to get the free, right?
And click on start a free trial, right?
Click on start free trial
and they're gonna give you 14 days.
And I think 14 days is pretty much all you need
to get up and running with as many leads as you want.
So click on free trial
and it's going to be the pro plan, right?
And they're gonna give you 14 days
if you don't wanna really pay for Apollo,
which I wouldn't recommend you do anyway.
Jonathan, hey Sam, are you using a Gmail account?
Yes, you also have to use a business email.
So in the video that I'm gonna post today guys,
I've made a mistake
and I've actually created a Gmail account.
I also got just 100 results.
So what I've done is I had to go in
and log in with my business email account.
And then I managed to scrape more than 2000.
But I have installed, yes, yes, it really does matter, man.
You need like a business account,
like a domain account, right?
On the call, do you run a scenario
and show the potential is that a bit much, do you recall?
I usually do it, man.
So what I do is basically I prepare the system, right?
And it's usually a drag and drop system with Google Drive
or sometimes I show them the fast indie scraper.
And when prospects see that you can basically look
for companies, especially when you're targeting
a recruitment guide.
Recruitment niche, they're not as tech savvy.
Trust me when I say this.
Like I've worked with so many recruitment firms
and they're typically more,
they're not like IT founders.
They're not as tech savvy.
They're just because they're always on calls with people.
They like that personal connection.
So they are more social.
So when you show something like this in make
and the beauty about make is that you can see
literally in real time what happens.
Like it's an actual module that they see.
So I would just walk them through this,
maybe like a minute or two, how the system works.
And then they will be wowed by this, right?
So I promised the book meeting.
So if you promise the book meetings,
I would encourage you not to leave the juice in the table
and just book them, right?
Just book them using their calendar, right?
You have the highest leverage.
Because they don't have to deal with it, right?
But if you really deal with it,
it's gonna hinder a little bit of your retainer ability
to retain them.
Just because sometimes clients wouldn't know
how to reply to leads or nurture them, right?
So sometimes when I tried this model,
like I would have a client logs,
a client that logs in their instantly account.
This is back then before when I used to like sign up
and instantly and give them the account.
They would typically blow so many deals, right?
And sometimes like they would not know how to talk
to these prospects.
So what I've noticed is since I know how to handle
the entire thing, so let me just do this book call, right?
And the only deliverable is going to be the book meetings.
They don't have to worry about it
and they don't have to care how I got them
the book meeting, right?
I could use my tactics, such as getting them on a call
and basically trying to nurture them using my tactics,
which is, hey, it could be like a different messaging, right?
Like it's the juice that you already know.
And the reason why I've included so many written replies
in the classroom, just so you can get inspired
and you can have any objection from any lead,
just because I've seen it all,
that I have so many requests, like pricing requests,
need more information, all of these can be easily.
This is really good stuff.
I appreciate that, thank you very much.
Just another question, what do you have found
to be a good offer, the type of niche?
Yeah, typically some sort of the best offer
would be basically having them not worry about the next lead.
The only thing that they would have to do basically
is to fill out a form, such as the onboarding form
that I have, and then in terms of how would you frame this,
is you can say something along the line,
I'm gonna connect you with pre-qualified decision makers,
right, have the pain, right, and the budgets
of the service you are selling, right?
And then you can also have them not worry about
the next lead, right, and all you have to do, right,
is basically fill out this form,
which is the onboarding form,
which is gonna talk about all the case studies,
all of whatever offer they are currently running,
and it's always going to be fully refundable.
So the upfront fee would be something along the line,
hey, I'm gonna get you 10 booked meetings, right,
for, 10 booked meetings, and what you would do, right,
and this is something that I noticed
that people in the community have done,
and it's actually interesting that most people
in the community actually figure that themselves.
So what you do is you say something along the line,
it's going to be an upfront fee, right,
for this amount of money, right,
for five booked meetings, right?
So once you get the five booked meetings,
then the remaining five booked meetings
will be on a paper lead model, right?
So this is like a pricing model, right,
that I've implemented in the past,
and I've done this with IT support companies.
So what you do is you say something like,
hey, we require an upfront fee,
which is, let's say, $2,000, right,
and then I'm gonna deliver you five booked meetings,
and if I fail to deliver five booked meetings
from the 10 booked meetings, right,
then I'm gonna deliver you,
then I'm gonna basically refund you entire money.
Now, it's so easy to get them
the five booked meetings for the $2,000,
and now what you can do is you transition
into a paper lead model, right,
and this is how you essentially basically
get that cash collected,
because what I want you guys to really focus on
is get the cash first,
just because any business that really, really crushes it
is they always collect the cash upfront,
just because you don't want your own profits,
you want to use it from the money that they gave you.
You want to have always cash collected,
so because I want you guys to scale, right?
I want you guys to speed run your success easily.
So once you get the first clients,
you use that cash, right,
to give them those meetings
and basically float as much value,
and then you upsell them hard on a retainer,
and you keep getting clients and cash, right,
and keep growing your bottom line, right,
because this is the easiest way to scale.
The credit amount is 100.
Yeah, I'm not really sure about the Apollo account,
because I scraped like a 2,500 earlier,
so maybe you're not using a Apify account
that is also a domain-related.
What do you do after the 14 days?
You just get a second trial account talking about Apollo?
Well, yeah, you can get another account,
or you can just pay for it.
In my case, I just pay for it
because I don't want to create new accounts,
but essentially, until I got to 25K,
I was still using trial accounts, right?
What's the timeframe you give clients,
given that you have to warm up the email accounts
for 14 to 21 days?
So what you do, basically,
there's two methods to tackle this, basically.
So either in your warm-up phase,
you would say something like,
typically, in order for us to build you an automated system,
we need to take into consideration the warm-up phase,
and basically, the guarantee will start
after launching the campaign,
which is typically 14 to 21 days after the warm-up, right?
You would say that at the discovery call,
and you would also frame it in a way that,
hey, it takes this amount of time
while we're warming up the email
to build this entire system for you,
which is completely ad hoc,
it completely hands off for you.
So when you say that, you essentially,
you're essentially just framing yourself
as you're giving them more value, right?
So the goal here,
because you guys, you will,
the only thing you guys have to do
is take in a template from the Blueprints,
just make a couple tweaks, right?
And then you can essentially frame it in the offer,
they're gonna get the entire system built.
And what you do is just record a quick Loom video
or a quick Loom video SOP,
and just walk through the entire system,
and this is gonna give you a huge advantage, right?
And you would say something like,
hey, this video is for anyone in their team,
and then you would essentially just give them the system
as a Blueprints in a Google Doc along with a video,
and also the five book meetings.
So you front load in so much value,
and they feel like they got so many things,
when in reality, you just tweak the entire system,
because they're not paying you guys
for the system or the book meetings,
they're paying you for the time and energy
that you put to build this entire system yourself, right?
Just keep that in mind.
And another thing is, if you're not using
the 14 to 21 days warmup,
you can just get them the pre-warmed up emails, right?
And just go ahead and deliver the results,
and it's gonna take you four to five days
to build out the entire campaign,
and typically a week to get the results for them,
like you're gonna get some positive replies
in the first week.
And then you can just go ahead and nurse it in a call,
and then in like a week,
you just delivered the one to two book meetings,
which is already crazy,
and now you don't have that stress
of the other book meetings, right?
So the goal here is to like,
when you buy pre-warmed up emails,
the goal here is to front load as much value,
and when you front load as much value,
you typically get them those book meetings,
and you interfere with the market quickly,
now we understand that our offer is working,
now what you do is just scale, right?
You add more volume,
now it's time to add more volume,
now you have a valid data offer,
you just add more leads,
and you get the more, more, and more book meetings.
Jason said, I think what got my client over the line
when she said that his two expenses,
I told her it's just 50% deposit,
by the 1030 is up,
you will have had sales,
and you will have made more money,
paid your name at 50%, that convinced her, yeah.
Well, man, you're better closer than me,
and you're asking me about the sales call.
I'm kidding, they were calling,
I should get 400% in the future,
I wouldn't worry about it,
if you got 50%, that's awesome,
when you have more skin in the game,
you will have more confidence
to pitch the 100% in the future,
so just give that a little bit of time,
so you can have the conviction to ask that, right?
Axel, with the pre-warmed up emails in Inslee,
there are five mailboxes, should you use them all?
Thought three mailboxes was the max to use.
So, yeah, typically these five mailboxes,
I'm pretty sure they have US IDs,
so honestly, I don't go over three email inboxes,
I've implemented five mailboxes before,
and I didn't have much deliverability issues,
just because I think the main reason why
is because the offer that I used to run
and the presentation that I had was already good,
so people were not marking you as spam.
So you have to understand, guys,
that the only reason for deliverability to go down
is people are marking you as spam.
When you have good personalization, good offer,
people are not gonna mark you as spam, right?
So I would use five email inboxes,
just because the pre-warmed up emails from Inslee
are pretty high quality,
and they warmed up using high quality US IDs,
so I wouldn't worry too much about it,
and I'd just get up and run it.
Awesome, guys, so we have 10 minutes left.
If you guys have any questions,
please feel free to post them in the chat right here.
There you go, so Edward.
So I've been talking to a world lead
who is selling high-ticket SaaS services
for personal trainers, around $7,000.
Yes, personal trainers,
which automates their training services,
but their leads have the big influencers
with large media presence.
Their current leads are from ads resellers,
but mainly referrals, yeah.
Just wonder what would be the best way
to scrape leads for this type of company.
I was thinking possibly an Instagram scraper, yes.
So what I would do is basically an Instagram scraper,
and so they are a high-ticket SaaS service
for personal trainers, around $7,000,
which automates their entire training services.
So tell me what would be their ideal client
based on the initial conversation,
so I can just give you more of a personalized advice.
So they are selling SaaS,
kind of like a software for personal trainers, right?
So I would still look for personal trainers in LinkedIn.
Typically those personal trainers at LinkedIn,
they're gonna have a sales funnel, right?
So those coaches, kind of like coaches and consultants,
they would have a sales funnel and a website.
You can still reach them out
using a LinkedIn sales navigator,
and you'd fill that in into any email finder, right?
So if you go to LinkedIn sales navigator
and you look for a personal trainer,
you're gonna find some people actually there, right?
You can still get a few leads there.
And then what you can do is you can also
basically scrape Instagram and look for influencers, right?
And then basically feed that data to any email finder.
And another hack you can do is
you would look for an ideal customer,
what would be their ideal customer,
and you would build out a campaign
based on what they are following.
So you can basically scrape all of their,
the people that they are following,
and particularly those personal trainers,
just based on my experience,
they would follow people in their space.
So now you have a bunch of people
that are in the same space, you can both target, right?
Axel just got a reply from my campaign right now.
It says, please advise price, how do I respond?
So, yes, so you wouldn't say the price.
So what I would do basically is just,
I would just go ahead to the, go to the classroom.
So what I would do is basically,
here is a copy for you that I would refine right now.
I'm just gonna go to school,
and then what I'm gonna do is
I'm gonna go to the Blueprint library.
And then this is what I would say.
Client management,
uh, where is the campaign?
Okay, so handling campaign example replies.
So this is the reply I would go for.
So what's the cost?
Which one should we go for?
Yeah, this one.
So I would say something along the lines.
So let me go to the chat,
and let me refine this for you, Axel.
So what I'm gonna do is,
I would say something along the line,
hey, thanks for getting back to me, first name.
We prefer how it works,
we prefer to explain how it works on a call
so you can see the value first before explaining the price.
I'm sure you would, no, I would remove this
just because this person is highly,
I think you're gonna get them on a call, 100%.
So we prefer to explain how it works on a call
so you can see the value first before explaining the price.
We work on a per lead model,
so there is no risk on the end.
I'd love to set up a time to talk,
I'd say something like,
when is a,
I'd say something like,
I'd love to set up a time to talk,
and then I'd suggest a time something like,
does X or Y,
or I would say something like,
are you available?
When is a,
I'd say something like,
I'd love to set up a time to talk,
are you available
on X or Y?
Which time works better for you?
Thanks for the time.
So I would say something along this line,
and I'm pretty sure since they said,
hey, what's the process and the pricing,
I would reply using this copy,
and I would just follow it up until they book a call.
Pretty sure you're gonna crush it, man.
So we have the last question
because we have five minutes left in the call.
I wish we could have two hour calls.
I'm thinking about something, guys.
Maybe we can do like a,
maybe like twice a week call,
where we come in and we do basically a role play
of sales calls, right?
And we would have to go in a community,
and we would basically just have
like a hypothetical sales call,
and then we would have someone who is a great closer, right?
And I would also come in
and basically audit everyone's sales calls,
and we basically just have a role play.
And so people in the community
like I understand how those sales calls,
just because kind of like a way to train yourself
how to handle sales calls,
just because it's always going to be reps.
So let me know what you guys think about this idea.
Okay, so the last question.
How do you not scrape the same leads twice multiple times?
For example, if I use the same URL twice,
how do I get the script to not scrape the first thousand
leads for example,
that I already scraped in the first time?
Yeah, man.
So what you have to do is basically
instead of might be a technical question,
but I was wondering if there is a new way around it
instead of using a new URL.
So what I do basically is just in your URL all the time.
And then I make sure I never include the same,
same filters that I included, right?
And typically when you do this,
you would add everything to instantly
and instantly we'll just filter out
all the duplicates for you, right?
So I wouldn't worry too much about it.
You could also,
you could also change the platform that you scrape from.
What you can do is use Apollo
and use a combination of some,
some form of LinkedIn,
and you're going to be pretty much good to go.
Trust me.
Yeah.
So thanks so much for coming today, guys.
You guys had great questions.
Please make sure to leave all of your questions
in the thread.
I'm going to make a post about it.
And obviously the recording is going to be updated
in the classroom.
So thanks so much.
Thanks, Sam.
Thanks, Jesse.
Thanks, Axel.
Thanks, Aaron.
Thanks, Edward.
Thanks for everyone who came in today.
I love you guys and keep crushing it.
I'll see you guys in the next week
and have a great rest of the day.
Cheers.
