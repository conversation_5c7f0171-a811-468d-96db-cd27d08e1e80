Let me just admit all of you guys is note takers.
What's up everyone? Hey <PERSON>, hey <PERSON>, can you guys hear me? Hey <PERSON><PERSON>.
Okay perfect. By the way the quality of the recording is going to be outstanding since
we're using OBS and then we're plugging this bad boy after that to euphonic so you guys will
hear me like an AI. So tremendous. You know <PERSON> like I started to say tremendous now.
Tremendous lead quality.
Yeah so let's just wait for everybody to come in and then we can get started. I have a really
good news for you guys actually. What's up <PERSON>? What a tremendous day.
Yeah I did hear what you said before that message.
What's up everyone? Hope you all are having a great week and you had a great weekend.
And let's just admit everyone and then we can just get started.
Yeah all these note takes. Yeah everyone is taking notes. I mean I don't blame you guys.
So we have our first question from our friend <PERSON><PERSON><PERSON>. Looking forward to it mate.
Wanted to understand your approach from medical and AI better. Yes I saw your last video where
you mentioned that you basically leverage Apollo right. So two simultaneous industry filters. Yeah
so what I do is I basically just choose the healthcare industry and then I just add IT right
or AI. Also saw you leveraged ExaAI for this but I was unsure about data accuracy there.
How was your experience with them? To be fair man <PERSON><PERSON> is amazing. The only downside is just extremely
expensive but I have really good news with you guys for you guys. So tomorrow I have like a meeting
with funny enough the person that actually that I booked the call with is also his name is <PERSON><PERSON><PERSON>.
So that's like synchronicity right. So I think this is their I don't know their sales manager
or something and I have a meeting with them and then I basically was talking to them for like the
past few weeks trying my best like literally begging them hey give me give me a damn coupon
like at least like 80% for the community so they can get started with that with Exa. So I've been
playing with it like last month and they gave me like 100 credits. So the data accuracy man is just
amazing like you can find founders you can find literally anything and AI just fills in the blanks
right. So I think this is going to be the next step. So hopefully tomorrow just goes well.
It's kind of like I'm going to war for you guys but hopefully tomorrow is going to be good and
then I'll see if I can lend a deal for us so everyone can just go ahead and use it.
So yeah so in terms of your question how would you identify decision makers from hospitals in
Germany? My experience was that these people rarely have a LinkedIn profile.
Yeah maybe a Google Maps scraper and go over each city manually. Yeah man like I would use
Google Maps but just think about it I think there's a there's like some confusion here
just because when I when I talk about medical and AI more hospitals and organizations like this
like healthcare organizations are more looking to implement AI nowadays right. So what you can do
is you can just you know connect the two. So you look for companies that specifically
make AI more digestible and specifically tailored for these hospitals right and you can just
connect the two. So you know I would just scrape hospitals and like cities in Germany
even though like I'm pretty sure you can still scrape hospitals off Apollo because I was I just
tried this five minutes before the call and as you as you can see here we have Germany
and then I can just filter by hospital and healthcare and I'm pretty sure these are all
hospitals and I have like 42k results you can even like go to industry keywords and then
if I type in for example hospital right I can let me just admit people here
if I type in hospital and then let's say I don't know why this is not let's go to net new
hospital and let me go back to industry keywords. Did you mean to share your screenshot? Yes I'm
going to share it right now yeah. Can you guys see the screen? Not yet.
There you go. What about now? Yep. It's perfect you guys can see me everything is going well.
Yeah okay perfect. What's up John? A wizard. So yeah like I was saying you can you can still
scrape or can still scrape hospitals off Apollo additionally you can use Google Maps another thing
is there might be platforms specifically in German right that you can scrape let's say
like organizations that are in Germany right that might have hospitals additionally what you can do
is you can look for directories right so currently I have a client that they scrape basically
schools and universities for those decision makers and it's pretty tough to find those leads
so what you can do is you can just look for directories right just in Google there might
be like platforms that they list right decision makers of those schools and you can just use make
to scrape that you can scrape that using an HTTP request and then just parse the results and then
feed it to AI and then basically AI is just gonna structure the data right so that's my thought
process when doing this keep in mind that the data is going to be limited because you just target in
Germany but if I were you I'll target the entire Europe right so hope this helps man if you have
any more questions just send me a DM and then I can just go ahead and chime in
Isaac said I need a 25k MO CRM in my life yeah the CRM I'm actually building for you guys is
insane we have like a 48 hour reminders where we're gonna use basically either perplexity
or XII we're gonna retrieve basically industry insights that we can just email that to our
prospect that we're gonna hop on a call with them and then we have a 24 hour reminder basically
they're gonna they're gonna hop on a call like regardless so the CRM is going to be centralized
so you can just track you leads and track the leads of your clients
I've decided to go after b2b SaaS authorized service companies any tips for current market
conditions yes man so the cool part about b2b SaaS is this is such a broad niche right so b2b
SaaS there's like SaaS for healthcare sale SaaS for law firms SaaS for legal like so many things
but there might be some ideas that I can basically just tell you and these ideas are going to be
like keep keeping customers for them is a is a big focus right so I want you to
deliver that new copy so when you are building an offer make sure you emphasize on saying things
like our systems are going to be able to keep your customer like grow your customer base
reduce churn etc etc and also focus on the benefits just because SaaS software
founders are pretty straightforward right there's typically tech savvy right so the tone also is
going to be founder to founder casual not too serious think about it because you're just talking
to a founder they the under like most of them understand AI automation etc etc so we can
basically like talk to them like uh basically what I'm like my my my main idea is don't take
yourself too seriously in the copy you can be a little bit casual right just because they are
tech savvy one and they understand how cold email works how AI and automation works so understand
that you're trying to pitch so it's easier for you to close in my in my experience like b2b SaaS
I've had like a like some good really good deals especially in healthcare so yeah emphasize on
telling them hey we can help you grow your customer base notice that I said customer base
I didn't say uh we're gonna get you leads or like we're gonna get you meetings so this is what I
mean by a clarified offer it's kind of like the same thing that we do with recruitment we say hey
we're gonna represent your candidates in front of companies hiring this kind of like a clarified
offer and if you guys wondering how I got this is basically I'm just using AI prompts that I
shared with you guys and the seven figure prompts so this one you should be using
which is uh positioning uh I think no it's uh clearing your offer clearing your offer
plus your client's offer so basically what you have to do is you're just going to put the offer
so let's say you have a client right and then their offer is like you know it's not the best
offer they they it's not clear so you can just put in this variable the offer right and then just
feed that into AI and then we just give an example so what does the what does well what
problem does the server solves example lack of leads time consuming manual process for engagement
and then we're giving another example so example instead of saying we implement lean agile marketing
strategies we say we create and manage automated sales systems that help you connect with three to
four new clients every month without relying on ads so it's like different so you can like uh make
sure you use this if you have like a client and also let's say you have like a uh let's say you
are for example uh some clients you might have they have like a SaaS software that let's say
it reduces churn so you can use this prompt and have like a very clarified offer for them
that will resonate
so yeah let me uh read through the questions here
i have opens but no replies what should i do and that's interesting uh also i am testing
by industry and also by company size should i only should i only merge all companies in one instead
can i ab test different offers and the same campaign legion and siarama for example
first of all you have opens but no replies so you're probably so you're enabling the open
tracking so you're tracking the your the people have actually opened your emails which i don't
recommend but now that you are tracking them and you see that people are like opening their emails
and then they're reading through your email and they're not replying definitely that's
a it's an offer uh it's like it's an it's like an offer issue and like a copy issue
i don't recall you actually posted an offer audit so just make sure you've posted an offer audit
right also posting the copy and then we can help refine it because 99 is going to be just an offer
like issue or like a copy issue which can easily be fixed right so now you know that people are
opening your emails so you have no problems with deliverability one and two uh the lead list is
pretty clean so there are no bounces people are not you're not getting like uh uh out of offices
etc so yeah um the next question can i ab test different offering the same campaign well
you can't really you can't just sell legion and crm at the same campaign you have you need
multiple campaigns for that right you need a campaign that where you can just sell
legion and another campaign where you sell crm because uh the way like ab like a testing works
right let me explain to you so the way ab testing works is um let's say this is this is ab testing
right so the whole point of ab testing is like it's trying like variations
of the same offer
right so we have offer a offer b which is the same offer is just a slight word in maybe like a
different guarantee because instantly basically instantly is is not gonna is not gonna like look
at your email it's gonna say okay this one is for crm this one is for legion right it's just gonna
like basically like play with the emails and just send let's say um variant a today
variant b is gonna be in two days right this is the whole point just because we use ab testing
so we can test different offers and we can also use it as spin taxing right so we're using
different words so we can have like a better uh deliverability so the idea here is you need
basically different campaigns you would have like a campaign for a crm let's say you want to build
a crm for them and you want to use cell systems for that go ahead and then uh you have another
campaign where you just go ahead and reach out to those companies uh by selling them legion okay so
hope this helps men
um our friends fans said any thoughts on the congrats on x personalization versus the impressed
by x plus follow-up question yeah the example this one i believe you are referring to this one right
here um it's the congrats line yeah this is the one this is one of the most powerful
uh personalized like prompts i've ever built uh this is like amazing right so i always use this
and i also use the original og one too but uh recently i've been just using the first one
so congrats on the cr and msp 500 list for goodness and this is a really good personalization man
but to answer your question um both are great but in for your first campaign i'll just keep it you
know basic right because i want you to do things that don't scale right i want you to first get
your clients and then worry about you know having crazy ai personalization later right uh i'm a
firm believer of doing things that don't scale like having like just squeezing as much juice
with as little costs as possible and then i can just go ahead basically and uh like i have like
a more powerful outreach right because if you like if you start going down the rabbit hole
of like personalization you can like you can spend hours and hours and hours even days prompt
engineer for the perfect uh personalization right so you don't have to do this i'll do it
and i'll just update that in the prompts library so to answer your question uh both are great but
i'm assuming this is your first campaign so i'm just gonna go with uh the second one right the
impressed by x plus follower it's the first one thanks um what no i say yeah it's the first
campaign so yeah yeah so first campaign i'll just go with the the second one oh you would go to the
second one yeah style two oh okay okay yeah first campaign i'll just go for style too yeah they're
both based on exa and then the prompts i find them equally easy to do that's the the question
really yeah man they're both they're both powerful that's the thing but uh just because um you know
what i've done in my in like 80 percent of my campaigns i've just used the impressed by x plus
follow-up question just because it's uh it's not generic like think about it like companies they
receive like congrats lines all the time people like use personalization not at the same level
but this is like asking them a question so it's like a it's like meaningful that's a really cool
one yeah it's really meaningful we're saying hey how are you tackling the challenge of maintaining
data currency and security when integrating this innovative solution with existing healthcare
provider systems so we it's kind of like we come in as um like we understand their industry in a
sense does that make sense yeah yeah and that's really helpful thanks yeah no worries man so our
friend uh wade said i'm seeing a lot of well-respected legion agency owners talk about
the end of apollo scraping taking effects in the coming days apparently they are changing
infrastructure yes that's true to prevent the past loopholes that people have been using for years
actually i'm pretty surprised why they haven't fixed their infrastructure yet obviously this is
a pretty big deal and will definitely impact a lot of us i'd love to know your thoughts how you plan
to uh how to plan oh yeah how about when it comes to scraping leads or any other recommendations
um then i wouldn't worry too much about it uh just because even if they change something in
their infrastructure um the next hour you're gonna see like new scrapers right like even now
like i'm pretty sure that people are in api are coding night and day right and they're trying to
build new scrapers so i wouldn't worry too much about it and we are also like pretty agnostic
about our platform uh we're not just using apollo we can use crunchbase linkedin uh linkedin like
linkedin is like huge can use linkedin is extremely cheap can use clutch can use clearbytes so many
like uh so many ways to get leads we can use exportlease.io so a lot of things you can use
derex app so all of these are ways uh to scrape data and like i said like if they change something
then like the next day you're gonna see scrapers like and epiphyre let me just admit uh wait
so let me just give you a tldr man so we actually just we just actually uh are answering your
question right here right so tldr is i wouldn't worry too much about it it's actually pretty good
for us since now like people like access the data for like your average joe is gonna be like pretty
limited which is amazing for us because we have like multiple ways to get data so now we have we
kind of like we're gonna capitalize the market especially with our ai personalization the way
we build our offers etc so it's even better for us but um like i said like even if they change
something even if they change like infrastructure or something like in a few hours you're gonna see
new scrapers being built and as we speak now um i'm talking to a few developers and epiphyre
and they're preparing for any like anything so keep in mind these are like like even like meta
which is like multi-billionaire like multi-billion company dollar company and it still can be scraped
like instagram facebook etc so apollo is like pretty easy to scrape i'm pretty sure like
any of us in this community just using cursor and and tropic cloud can build our own scraper
but um yeah man so i hope this helps
perfect timing yeah
so i wouldn't worry too much about it yeah uh in the meantime let's say
like the infrastructure changes for like a couple hours you can just go ahead and screen
linkedin clutch crunchbase uh clearbytes and other platforms yeah but uh i wouldn't worry
too much about it at all just because scraping has been for like 20 years now and it's only
gonna get better better and better and better okay we have uh three lovely questions from our
friend joelle sounds awesome looking forward to it looking forward to your questions too my man
uh how to avoid prevent hallucinations from gpt slash claw i see one contact out of five out of
ten where it will completely misspell the company name and usually the leads get pissed so i'm pretty
sure you are referring to the company name for matter so um what i would recommend just feed in
three to three to six examples of what could be a perfect output right so you'd given like examples
of a company that has ltd another company that has ink another company that is super long and
then you just basically uh give it like like three to six examples and then i would see where you are
like what kind of data you are mapping so in the original one we're feeding in the domain name
the person's headline and uh yeah and the original company name so you can add in the company
description too so it depends on where you get in the data but the company the the company's
websites which is the domain url along with the headline and three to six examples and it should
be pretty much good to go so yeah i'm getting prospects from different backgrounds one is
building concert halls one uh sells custom websites another does cloud infrastructure
it is it a good idea to onboard them all or will be too much of a headache afterwards ps
originally my niche is b2b sass um i'm not gonna lie to you man i would onboard all of them if i
were you just because one uh you will get a lot of experience in doing so you will understand
which uh markets is working which markets is not working so like for me like uh having no clients
is worse than having so many clients and dealing with headaches because you will be able to feel
like regardless just because when you see money coming into your bank account you will get like
you will you will work trust me like it's gonna be worth the headache so just from our few
interactions you and me you're a pretty smart guy so i think you'll you'll do okay again depends on
your ability to handle all of these uh prospects but if i were you i'd 100 just onboard them all
because you're gonna learn a lot of things you're gonna learn onboarding you're gonna learn client
communication you're gonna learn lead scraping so basically there's like multiple niches here like
one is building concert halls now like all of these like like uh on top of my head it's pretty
hard to get leads for this guy so you might learn something new you might learn like a google map
scraping etc so one sells custom websites so yeah it's a pretty interesting niche too does cloud
infrastructure this is the easiest one so yeah i would just go ahead i would just go all in right
again depends on how on your ability to basically
deal with uh so many prospects yeah do you have a good example of one of your best follow-ups
using a multi-channel approach to convince a lead to hop on a call finally yes my friend i do so
what i would recommend you do so let's say lead shows interest in a campaign so i'm looking to
like implement this too in my process which is extremely new but i'm seeing like a lot of
people actually do this and they have really good results and uh actually vichal he's a cold caller
like the greatest gold cold color on planet earth he does this too he he does multi-channel approach
so what he does is he starts a cold email campaign and this is heavily inspired by him it's not
something that i've came up with okay he would start a campaign and then as soon as someone
uh shows any like tiny bit of interest they like he said he probably sends them like a message
on linkedin right so he reaches out to them on linkedin and then i have a couple pre-drafted
like messages for you so you can just basically copy them
so you'd follow up on linkedin right or just straight up just cold call them so you send
them a link like a link linkedin message by saying hey inboxes can be hectic figure that reach out
and talk about xyz or if they haven't responded at all you can just straight up just cold call
right and say you just called them uh and you say hey i sent you an email about problem etc
you say hey i sent you an email about problem etc figured out just call instead of adding another
email to your inbox and you say hey i have a meeting right now but i'd love to set up a call
on x or y so just this shows scarcity because what i would recommend is not uh calling them
and just pitch in or like trying to sell them anything just try to set up a call right and
when you say like i have a meeting i'd love to set up a call on x or y like which day works
better for you it it implies like that you have like other meetings going on even if you don't
have like you have none so this is what i think this is what vichal does i'm not trying to butcher
that but uh hopefully he can uh maybe i'll just dm and if he can make a post about it
but uh yeah like uh multi like this is like uh this is the real juice that i'm trying to
implement too like um usually i like i just follow up on instantly until they respond and
book a call but um if you reach out to them and linked in on linkedin too and then you can just
go ahead and uh find their phone number and just straight up call call them i think this i think
you'll be able to help like get them on a call pretty easily because think about it like prospects
are not used to someone sending him email then again uh sending them a linkedin like
an outreach message and then cold calling them at the end right this shows that you are pretty
serious and more people are like are gonna respect that like most of the other prospects
are gonna be like okay this guy like like he's pushing the needle so i'll just hear what he has
to say um finally how do you know if a client is worth working uh is worth working for or not
what are your criteria yes i have a good i have some good ideas for you so how do you know if a
client is worth it or not what are your criteria so at the beginning uh you have to understand
guys that you might not you might not have the luxury to be super picky right but here's a
simpler way to decide if a client is worth working with so typically a good client understands our
why so when you are on a sales call and you show them you know the results that they can get and
the return on investment they can get they're not just saying how much but also like what can
what do i get like what what is what is the juice that i'm gonna get at the end right like how like
how much i can make out of this so a good client will understand this right if they try to negotiate
to aggressively upfront it's usually a red flag right most of my best clients were clients that
i just sent them an invoice and they just paid it right away either in like a few hours or like
the next day um one little hack that i would recommend you guys do when you send an invoice
or like when you send a proposal that has an like a stripe integration make sure it's always only
48 hours don't make like make sure you have only 48 hours just because it showcases that you're
pretty serious and you want to get up and running like as quickly as possible and get them wins
so make sure you because i see a lot of people they send them an invoice it's like seven days
like 10 days 14 days like 48 hours and that's it and they usually pay within like that
time frame um again in the beginning some projects are just worth it just to learn and
get testimonials especially if it's your first client so i wouldn't stress about it that much
if the project helps you you know build skills connections or portfolio it might be worth taking
it um even if the pay isn't perfect because it's just the beginning right after all you're just
gonna like you're gonna like qualify hard and trust me when you start qualifying hard this is
like when the real fun begins again are they respectful of your time are they cognizant of
your time um are they easy to work with now they're clear make sure you you set up the
expectations really early aligning incentives if they are rude or dismissive then i'll just
like i'll just refund them right away and then like i'll just stop working with them
if they act like there's no one you favor there are no go again if they don't trust your expertise
then that's a bad sign if they do then that's a great sign obviously if they micromanage
everything i don't like clients that micromanage everything just because if i'm taking in a project
i don't like to be you know emailed every single 10 minutes i don't like this like i always make
sure i set the expectations early right first kickoff call hey here's how you can like here's
what you can expect i'm gonna give you like progress updates twice twice a week um if you
have any questions just email me and then i'll get back to you in like a few days right so that's
uh what i would do so hope this helps man uh let me know if you guys have any questions in the chats
because we have one last question and then we're gonna go back and answer all of you guys's questions
so it's gonna be like um more of interactive and fun in a sense um aaron said what are the
send in limits for mailboxes now i thought i heard that esps were cracking down on the amount we can
send before you if you're labeled as spam okay in short do the amounts in this high entities and
send in documents to apply or should we be sending a fraction of this amount that's a really good
question man but let me explain to you guys something so esps do not just randomly like
crack your deliverability and just basically just uh put you in spam typically people need to
mark you as spam so your delivery just stinks right so esps don't work this way they don't just
let's say if you like warm up your emails for 21 days and you start using the high intensity
sending method you're not if your offer is good which i'm assuming you have a great offer and you
using the best practices you've warmed up for 21 days you're enabled 100 on reply rates you also
have a good offer you're not mentioning spammy words in your copy and like think about it like
our copy the copies that i see in the community are extremely like word class trust me like you'd
be surprised that's uh is what is the ours like writes like the most templated shitty emails
like ever so like most people here in the community like they haven't like gotten any like sales
experience like like any cold any copywriting experience and they're writing like pretty good
emails just because it's a simple formula right and it just works so i wouldn't worry too much
about it if you have like a good offer right a good offer a good copy deliverability just works
you would never worry about deliverability if you have your emails warmed up you are you have a good
copy and a good offer just because people are not gonna like uh mark you as spam but
what i what i what i'm sending currently is i'm just sending 20 emails per inbox right per inbox
and i don't go over two to three email inboxes uh per domain so two three max if you really want
to push it not just two is pretty good so two email inboxes per domain and then i'd have like
uh 10 to 15 domains and then i'd send 20 emails per email 20 emails per email and then it would
be the next day i would send like 50 emails or like 40 emails per email account and then i'll
just pause for 40 for 24 hours sorry and then i would just tank the reply rate again 100 and then
again i'll just rise and repeat again if you are looking to send more the best way is just to add
more inboxes and then you can just send more okay um so now these are all the questions that we have
here uh what i'm gonna do now is i'm just gonna read through all of you guys's questions in the
chat and then the best way to do it is just go on top first okay uh just a suggestion how about
dumping a copy of one of the notes oh this is uh not a question this is uh a comment about note
takers um yeah okay so any thoughts on minimum company size for b2b sass i'm targeting this group
sub niche health yeah sub niche health currently that's a really good niche man currently scraped
11 to 50 employees good good idea but was thinking about even smaller ones yeah yeah you can you can
go ahead and target startups and what i would recommend you do is you can uh like you can also
scrape them off crunchbase because you can filter by series a b c which is amazing this like these
are like the like gold mine because you're investing in growth and they're recently just
funded right so you can just go ahead and build a less off crunchbase and and again apollo so this
is like even more powerful you can have like even like 10 000 leads at that point um again i'm just
gonna give you like uh some guidelines since you're going for five to ten it's startups so
typically um they they wouldn't have like a crazy amount of budget but they will still have some
budget and uh you could pitch your offer as in um typically when like a company like the most
straightforward way for a company to acquire clients is through outbound right this is like
how any b2b company on planet earth started is through outbound and then once they have like
like a like a pipeline of like good quality leads then they can basically diversify
their lead acquisition methods they can basically run some ads etc so you can just have that in
your offer you could say hey uh the cheapest most straightforward way to acquire clients
um i'm pretty sure yeah you're going for b2b sass so the best way to uh grow your customer base
is through outbound we essentially just built a system that can send 70 times more than than
a typical sdrs and our emails are more relevant and timely so confident so confident we could
basically connect to you uh we can grow your customer base by x or something this is kind
of like on top of my head obviously um you're gonna have to refine the copy but just like an idea
um even as low as one was just thinking about payability as don't say yes you'd be surprised
like even some founders that are like one man agency right they make a lot of money like i
wasn't i wasn't one man agency until i hit 40k a month it wasn't easy but you know uh like there
are founders there are founders that are still like one man agencies and they're willing to work
with you right so i wouldn't i wouldn't worry too much about i would go as low as one you know one
to five even one
so you said thanks a lot in this case ab testing yes no worries my friend let's say i'm scraping
a polo do you have an idea on how to make sure the batch of leads i'm using with offer one
legion does not interfere with offer to crm if i use each one in a campaign by itself
yeah this is a really good question man
um yeah um so what i would do in this case the most straightforward way is just to diversify
like um so you can the problem is you have like a problem you have a an issue here you can't
run there's two ways okay there's like a hack you can do is the first hack is you'd have like
pre-warmed up emails and then you just test the offer this is what i would do so like i'll just
get pre-warmed up emails i wouldn't send from your primary domain because now you're just
interfering with the market shows i'll just get pre-warmed up emails and then um i would just test
the offer i'll test the offer of legion and then i'll test the offer with crm so depending on what
you want to sell like the most i'm assuming is going to be legion right so i would have your
your own company emails for legion and then for the crm or like any other offer like let's say
another offer that you want to sell it could be for like you would use just pre-warmed up emails
and you'd see what resonates additionally you can just alternate the industry right because it might
not make sense to target the same industry with the same with different offers right with as the
same company
uh sven said when starting my campaign i'm a bit confused if i should just keep it simple
do 30 emails per day or do the thing where you tone and then zero then to cool down um
what i would recommend man is just um go for the 20 emails per day then up and then pause because
it just works man uh i'm assuming you want to get up and running as quickly as possible
you want to send emails like like you don't want to like just keep waiting like an entire month
to to see if like an industry resonates right so just because i'm like we we've hopped on a call
before uh i don't think i think i think we did uh i'm not really sure no we haven't not yet right
yeah no but but just to get some numbers so you say start with 20 then go off then pause so what
do you go up to when do you go up and how long do you pause if you could put like monday tuesday
wednesday type of simple calendar yeah so um yeah you send 20 emails per inbox right and then it's
going to be like 200 emails per day which is on the lower end and then the next day you can ramp up
because your deliverability is not going to tank in one day like it can never happen right so you
wrap up on date yeah it's kind of like doing a hard set at the gym instead of doing so many like
lower like uh um tense it's like doing a hard set other than doing like 10 sets of like 20 reps
right right so this is like the uh idea where i got the idea from so again if you're skeptical
right i can you can just go ahead with 30 emails right like uh it's just a bit lost
between between all the options man so yeah yeah what's what's the easiest uh simple way but i get
20 50 zero rinse and repeat yeah man so okay like what okay if you want the most straightforward way
and like just have peace of mind because i get it like uh even like me when i before i started
implementing the high intensity sending i was a little bit skeptical but um you can just go ahead
and start with 30 emails right now yeah no it's it's just to uh if there are multiple options
it's always nice to know okay just tell me which one to do and i'll do it that's more yes yeah so
i'll just go ahead and start with 30 emails per inbox and then uh i'm assuming you have 15 emails
i have 18 18 yeah yeah that's great man so you can just multiply that by 18 and then uh you
would just keep that consistent and just get your first clients and then we can worry about
high intensity yeah exactly does that make sense thanks yeah yeah no worries man um
sas health topic crunch base startups great series yes uh yeah you you target all of them
series abc and the cool part about crunch base is they like they tell you like exactly how much
this company raised it's like every single thing she's amazing you can have like check out the last
video that i recorded like this one of the most crazy like outreach i've ever built and just using
a crunch base you guys should be scraping data from crunch base like like a lot of people are
sleeping on it trust me it's not a youtube video you're referring to yes yes yeah cool i'll check
it yes yes um makes sense yeah no worries you so feel free to reach me out via dm then like i'm
happy to help all the time hey sir thanks for taking the time to help i've decided to focus
on it in healthcare as my niche my email you guys will capitalize on this niche my emails are warmed
up and i've already purchased and warmed up three domains for 21 days do you think we should have
five domains instead uh should i go ahead and buy two more pre-warmed up domains and we can and can
you please guide me on what the next steps are should i follow the 12 uh day agency class yes
man yes 100 so you have already like you you already done the work right so you have to like
you have your emails warmed up for 21 days now the only thing i would do yes i would go ahead and
i'm assuming you did watch the the cold outbound masterclass just so you can understand basically
that cold email is a system and it's just inputs plus variables and equals clients
so yeah the call the cold outbound masterclass is like you should watch that like 100 just because
it's gonna like put you in like a this the correct state of mind and it's gonna remove all those
limits and beliefs about cold email or outbound in general so yeah um in terms of what you should
what are the next steps the next steps is picking up uh so you've picked okay so you've picked an
it help okay so just go ahead and build an offer right so go build an offer make an offer audit
and then uh we're just gonna go ahead and offer uh audit your offer and then the next step after
the offer audit is just writing the copy posting again and then we just go ahead and troubleshoot
everything uh in terms of your campaign and then we're gonna refine the copy
and then everything is going well thumbs up then you can just launch
yes go for 20 per day
so
yeah man i've been good i've been went to the gym like three days so i'm definitely feeling
not so great yes uh go for 20 per day next day ramp up to 50 third day rest
yes amazing that's exactly what i do so uh that's pretty much it i think we have 15 more minutes
okay zach asks what should the daily warm-up email limit be once your campaign is running um
10 emails typically just 10 emails yeah 10 emails and it's gonna be 100 reply rates
yeah 10 emails
yes so just draft an offer make yeah make uh an uh like an offer audit post
and then uh that's pretty much it yeah 10 emails don't go over 10 emails fan
just because 10 emails is like the general ballpark you can go over 15
but you want to leave you know some room for the emails actually being sent
you know so this is why i don't go over 10
yeah
so hopefully tomorrow we have some like a pretty good offer with the axa and that would be like
really amazing i'm just gonna make it like exclusive for the community like um maybe if
they can give us like a coupon like maybe like 70 at least and then uh like people can like uh
just get a plan or something
hey son i'm having a lot of clients saying that 10 ltv is way too high because their clients
ltvs can be really high how would you handle the objection
hmm 10 ltv uh do you mind giving me like uh like a little more context like what do they like uh
what do they say like exactly so i can just give you like the right
spot-on answer because i have a couple ideas
i'm i believe you're targeting b2b sass too
oh potential ltv of a 200k plus oh this is a big company
hmm yeah that's uh
so uh what is their cpa like cost per acquisition like uh how much do they like how much are they
willing to spend to acquire one client
one client
that's good this will give you like a lot of information
like ltv of 200k plus that's crazy
yeah they just got funded yeah so this is why yeah so they just got funded and their ltv is
200k plus man that's like that's a really good deal 100 i'd pitch 10k i'd pitch 15k for this
right i'd pitch i'd pitch a lot of money for this
uh per lead no uh like i'd pitch a setup fee 100 like um let's pitch a setup fee and i'd pitch
a percentage of each client onboarded i wouldn't go for leads so if you have like an upfront fee
so this is what i would recommend and this is exactly what i've done with uh the microsoft
affiliates so if you guys don't know i had like a company in the it health space back in september
it was one of my biggest deals that i've closed and what i've done and what i would recommend you
do just because back then uh i was talking to a few friends few like a lot of my friends
that they were better than me at that point uh and then i asked them they i mean they they
were better with sales and then what they said specifically so i'm just gonna give you the juice
what they said is that ask for a setup fee just because they now they just got funded they have
money so you can they can they can pay for like the setup fee ask for like a higher setup fee
and then instead of paying paper lead you can just go ahead and take a commission out of each
closed deal so now that you have you're gonna understand so now that you have the setup fee
which is a pretty good amount like like if you pitch like 8k or something
you're damn sure you're gonna deliver because you can basically pay for everything you can
pay for any mail finder you can pay like you can pay for like all the platforms that you're
gonna need to have like a super super good outreach for them so it's virtually impossible
not to deliver at least 10 book meetings in like let's say 60 days okay just to be just to be like
fair and uh and um you know just to be on the lower end right so if you just onboard them one
client you'd say like 10 percent right let's say like like for them to acquire one client they have
to pay um let's say like um 50k to acquire one client because assuming they have like ltv of
200k plus you can just get 10 of that i think this is like a this is a huge company they just got
funded yeah so yeah man like uh this is like this yeah that makes sense yeah now just ask for a setup
fee and frame it in a way that you want to really get them clients like actually get them clients
right you're gonna say hey i'm gonna be sending you meetings uh and for each close i'm gonna
get a commission of that right maybe like eight to ten percent right if you're not sure about
how much you should pitch in terms of commissions you can go as little as as low as five percent
it's still a lot of money um but yeah this is what i would recommend they have to pay
for like a setup fee and then like a commission this is what i would do and i would recommend you
do uh how about stepwise uh percentages first 50k 10 percent next 50k 5 percent any above 100k 3
percent just pulling around the numbers yeah that's a really good uh that's a really good idea then
yeah that might work yeah but uh i would go for commissions with these types of companies
100 commissions a setup fee and a commission hey sod can you roast this copy 100 percent
personalization i'm raj and i work specifically with uh companies like company name to get more
clients a really good offer man straight to the point i help you get more clients
that's it would it be helpful if i sent over a lead list of 100 plus oh actively looking for
companies actively looking for your service free of charge yeah this is an amazing copy so guys
take some notes this is what i mean by a really good like this that's a great copy yeah great
100 so you have context i'm raj i work specifically with recruitment companies
relevant to get more clients extremely straightforward clear would it be helpful
if i sent over a lead list of 100 companies this is the offer really like free so they don't have
to do anything literally like nothing and just reply with a job title so this is like all of the
all of these aspects are beautiful so this is like well crafted man good job let me know if
that'd be helpful you've been taking notes man soft cta and sense from my iphone this is something
that like a great copywriter would write amazing 10 out of 10 yes uh it's just that i'm missing
social proof so i was worried a little i think this copy is gonna crush it man um if you have
you can include some social proof if you've worked with past clients uh actually you are
working with a client now so why don't you just mention that person trust me like case studies
are all bullshit anyway like it's just that instant dopamine hit that elite reads through your email
that's it so do not worry about it so if you if you can like mention any work experience
and that would be really helpful that would take this from 10 to okay that would take this from
9.9 to 10 so hope this helps man okay so we have six minutes left uh i believe we skimmed through
all the questions that we have for today yeah that's pretty much it
okay we have one new message coming in what would be the term the the determining factor
push for a high setup fee and commission instead of paper lead clients yes yes 100 if they recently
got funded if they have a high ltv if their cost per acquisition slash cpa or cpa is like extremely
hard let's say if they acquire a client they have to pay like 60k right and then it makes sense to
push for like a higher setup fee another thing if they have um if it's if the project scope is
simply uh hard right if the project scope is extremely hard like extremely hard for you to
get leads for them right then it makes sense to push for a higher setup fee and this is where
you kind of like switch the game in a sense so this is what i would recommend you guys be in
this is like the state of mind that i want you guys to be in is not to be a closer right you
guys are all sales systems engineers so in the sales call you want to like put yourself in a
frame of a consultant so they need you so when you go into the discovery call and you keep asking
them questions right you're going to be determining if the project scope is worth it or not right and
then after that then you decide how much you'd like pitch them right so always put yourself in
frame of mind of you know like uh of a like someone like that is a consultant not a salesperson
okay so you would just understand their business etc etc and then if you notice that um it's going
to be hard for you to get them leads you just say it like straight up you say um your market is
um but in order for us to deliver the results we require a x amount of money
yes would you change the proposal on the fly to have it already during the yes
i've done it multiple times uh i would just go ahead and just uh change it right away and uh
another thing if they like you during the call you'd pitch your price in and then uh you just
go ahead and just change the pricing right away and then just send them the proposal via panadoc
um i don't like to automate sending proposals via mink just because when you deal with money
just a slight mistake can just mess everything up so i always send my proposals uh
like because like i just send them all like um manually well it's not really manually i just
click on send so it's not really like manually it's kind of like automatically
just because i want to make sure i send something that is high quality i don't want ai to mess with
that out like i like sometimes ai will mess things up um yeah ideally always during the call sometimes
five minutes more after the call like the you know the sooner you send the proposal the more
likely they're gonna pay just because when someone wants to pay you you you want to let
them pay you right when emotions are high this is where you capitalize yeah it just makes sense
this is just pure salesman um i get a lot of interested leads only looking to pay on clients
closed and not no setup fees so nothing how can i try to turn that out and get an upfront fee uh so
interested leads in new campaigns or like during the sales call axle
i believe during the sales call on sales call yeah um are you showing them the the ry calculator
well typically how like uh
then
yeah
on sales calls so what is the uh how do you approach your your sales calls
there might be a bottleneck
ideally you would want to show them the high ry the the ry calculator right
they joined the meeting telling me straight up they are not looking to pay anything upfront well
yeah that's that's pretty typical but you want to understand why this is where you ask them a
question you say um what like uh have you worked with other um service providers before etc etc
and then you just like yeah yeah that sounds about right
right
yeah i would i would uh
they joined the meeting telling me straight up they are not looking to pay anything upfront
so this is like the first objection how do you actually bypass that is by asking them questions
yeah yeah you just ask them questions um you say like have you worked with other service providers
before and then typically they're gonna be like they're gonna start talking they're gonna tell
you why they they're not willing to pay anything upfront and at the end like like i said like just
position yourself as a consultant not a not not a salesman and um be more willing to walk away from
clients that are there's going to be headaches okay if someone just joins the meeting and just
straight up like ask like tells you not alone not to pay for anything upfront that's like that's
totally normal right you can you can you can bypass that yeah i'd love if you can record your
sales call so we can just go ahead and like uh see how typically is
is
additionally uh you can you know you can show them the ry calculator and then uh you'd uh you'd uh
you'd work with them on a let's say a paper lead right and you'd have you'd have like an offer
let's say for example you would have like a small setup fee right they they still have to pay some
sort of money upfront they have to just because it shows that they are um interested one and two
they are more committed and they're serious about their lead generation right so just frame everything
in terms of ry and then uh yeah maybe we can say we have a team that's why and we are certain yes
yeah i used to say this too so yeah man that's that's what i would do yeah and at the end it's
just a numbers game like uh not every sales call i close even now like i don't close all my sales
calls sometimes it's just not a fit just keep that in mind okay um i think that's pretty much it
uh we finished our hour so yeah thanks for everyone who came in today uh really uh i
love talking to you guys and answering you guys's questions um so yeah great call thanks zack thanks
axel thanks fenn thanks uh nicole uh thanks uh izak um zack kartik hopefully i did i said
your name right raj again and yeah looking forward to the next week guys
