#!/bin/bash

# Exit on any error
set -e

# === CONFIG ===
WHISPER_ENV="$HOME/whisper-env"
OUTPUT_DIR="$HOME/transcriber-web/transcripts"
WHISPER_MODEL="medium"  # or "small", "base", etc.

# === INPUT ===
MP3_FILE="$1"
if [ -z "$MP3_FILE" ]; then
  echo "❌ Please provide an MP3 file path."
  exit 1
fi

# === Activate whisper virtualenv ===
source "$WHISPER_ENV/bin/activate"

# === Transcribe using whisper ===
echo "📝 Transcribing: $MP3_FILE"
whisper "$MP3_FILE" --model "$WHISPER_MODEL" --output_format txt --output_dir "$OUTPUT_DIR"

# === Remove timestamps from transcript ===
BASENAME=$(basename "$MP3_FILE" .mp3)
TXT_FILE="$OUTPUT_DIR/$BASENAME.txt"
CLEAN_FILE="$OUTPUT_DIR/$BASENAME-clean.txt"

# Remove lines that look like timestamps
grep -vE '^\[[0-9]{2}:[0-9]{2}\.[0-9]{3} -->' "$TXT_FILE" > "$CLEAN_FILE"

echo "✅ Done! Transcript saved to:"
echo "$CLEAN_FILE"
